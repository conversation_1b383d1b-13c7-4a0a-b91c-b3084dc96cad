import React, { useEffect, useState, useRef } from "react";
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  IconButton,
  MenuItem,
  Box,
  Select,
  FormHelperText,
} from "@mui/material";
import { bgColors } from "../../../utils/bgColors";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import RemoveCircleOutlineIcon from "@mui/icons-material/RemoveCircleOutline";
import TextFieldWithBorderComponent from "../../common/TextFieldWithBorderComponent";
import VariableAssignmentComponentReactflow from "./variableAssignmentComponentReactflow";
import { useAppSelector } from "../../../utils/redux-hooks";

interface WebhookAccordionsProps {
  requestType: string;
  requestUrl: string;
  handleRequestTypeChange: (e: any) => void;
  handleRequestUrlChange: (value: string) => void;
  handleAddVariable: () => void;
  headers: Array<{ key: string; value: string }>;
  handleHeaderChange: (index: number, field: string, value: string) => void;
  handleRemoveHeader: (index: number) => void;
  handleAddHeader: () => void;
  handleAddNewClick: () => void;
  handlePopupClose: () => void;
  handlePopupSuccess: () => void;
  handleVariablesChange: (stepNumber: number, newVariables: any[]) => void;
  handleEditorStateChange: (
    stepNumber: number,
    newEditorOrTextState: any,
    type: string
  ) => void;
  requestUrlInputRef: React.RefObject<HTMLInputElement>;
  bodyDescriptionInputRef: React.RefObject<HTMLInputElement>;
  handleBodyDescriptionChange: (e: any) => void;
  bodyDescription: string;
  handleSaveUserVariableChange: (e: any, newValue: any) => void;
  selectedVariable: any;
  saveResponseType: string;
  handleSaveResponseType: (value: string) => void;
  variableNames: any;
  isAddNewPopupOpen: boolean;
  setNewVariableName: (value: string) => void;
  newVariableName: string;
  errorDescription: string;
  handleErrorDescriptionChange: (e: any) => void;
  handleSelectedListChange: (list: any) => void;
  handleButtonsChange: (buttons: any) => void;
  showSaveUserResponse: boolean;
  setShowSaveUserResponse: React.Dispatch<React.SetStateAction<boolean>>;
  toggleSaveUserResponse: () => void;
  handleSave: (step: number) => void;
  isWorkflowEditing: boolean;
  jsonError?: any;
  urlError?: any;
  variablesError?: any;
  setRequestUrl: (value: string) => void;
  setHeaders: (value: any[]) => void;
  setBodyDescription: (value: string) => void;
  textFieldValue: any;
  setTextFieldValue: any;
  variables: any;
  handleRemoveVariable: (index: number) => void;
  handleVariablesValueChange: (newVariable: any) => void;
  handleVariablesFallbackValueChange: (newVariable: any) => void;
  isCurlMode: boolean;
  curlBodyProperties: Array<{
    propertyName: string;
    variable: string;
    attribute: string;
    fallback: string;
  }>;
  handleAddCurlBodyProperty: () => void;
  handleUpdateCurlBodyProperty: (
    index: number,
    field: string,
    value: string
  ) => void;
  handleRemoveCurlBodyProperty: (index: number) => void;
  onAccordionChange?: (expanded: boolean) => void;
}

const WebhookAccordionReactflow: React.FC<WebhookAccordionsProps> = ({
  requestType,
  requestUrl,
  handleRequestTypeChange,
  handleRequestUrlChange,
  handleAddVariable,
  headers,
  handleHeaderChange,
  handleRemoveHeader,
  handleAddHeader,
  handleVariablesChange,
  requestUrlInputRef,
  bodyDescriptionInputRef,
  handleBodyDescriptionChange,
  bodyDescription,
  jsonError,
  urlError,
  variablesError,
  setRequestUrl,
  variables,
  handleRemoveVariable,
  handleVariablesValueChange,
  handleVariablesFallbackValueChange,
  isCurlMode,
  curlBodyProperties,
  handleAddCurlBodyProperty,
  handleUpdateCurlBodyProperty,
  handleRemoveCurlBodyProperty,
  onAccordionChange,
}) => {
  const [hasErrors, setHasErrors] = useState(false);
  const [curlPropertyErrors, setCurlPropertyErrors] = useState<
    Array<{
      propertyName: boolean;
      attribute: boolean;
      fallback: boolean;
    }>
  >([]);

  useEffect(() => {
    const errors = [jsonError, urlError, variablesError];
    setHasErrors(errors.some((error) => error === true));
  }, [jsonError, urlError]);

  useEffect(() => {
    // Initialize errors for curl properties
    setCurlPropertyErrors(
      curlBodyProperties.map(() => ({
        propertyName: false,
        attribute: false,
        fallback: false,
      }))
    );
  }, [curlBodyProperties]);

  const validateCurlProperty = (index: number, property: any) => {
    const errors = {
      propertyName: !property.propertyName,
      attribute: !property.attribute,
      fallback: !property.fallback,
    };

    setCurlPropertyErrors((prev) => {
      const newErrors = [...prev];
      newErrors[index] = errors;
      return newErrors;
    });

    return !Object.values(errors).some(Boolean);
  };

  const handlePropertyChange = (
    index: number,
    field: string,
    value: string
  ) => {
    handleUpdateCurlBodyProperty(index, field, value);
    validateCurlProperty(index, {
      ...curlBodyProperties[index],
      [field]: value,
    });
  };

  // Create an array of refs for header value inputs
  const headerValueInputRefs = useRef<Array<React.RefObject<HTMLInputElement>>>(
    []
  );

  const data = useAppSelector(
    (state: any) => state.getSaveResponseAttribute.getSaveResponseAttributeData
  );
  const attributesData = data?.data;

  // Initialize refs when headers change
  useEffect(() => {
    headerValueInputRefs.current = headers.map(() =>
      React.createRef<HTMLInputElement>()
    );
  }, [headers]);

  const handleAccordionChange = (expanded: boolean) => {
    onAccordionChange?.(expanded);
  };

  return (
    <Box sx={{ overflowY: "auto", maxHeight: "100%" }}>
      {/* First Accordion: Define URL */}
      <Accordion
        sx={{ padding: "0px", marginBottom: "8px" }}
        onChange={(_, expanded) => handleAccordionChange(expanded)}
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          sx={{
            minHeight: "30px",
            padding: "1px 6px",
            margin: "0px",
            overflowY: "auto",
          }}
        >
          <Typography sx={{ fontSize: "14px" }}>Define URL</Typography>
        </AccordionSummary>
        <AccordionDetails
          sx={{ padding: "8px", overflowY: "auto", maxHeight: "60vh" }}
        >
          <div style={{ marginBottom: "8px" }}>
            <Select
              //   label="Request Type"
              placeholder="Select HTTP Method"
              value={requestType}
              onChange={(e) => handleRequestTypeChange(e as any)}
              fullWidth
              variant="outlined"
              sx={{
                height: "36px",
                fontSize: "12px",
                borderRadius: "6px",
                "& .MuiOutlinedInput-root": {
                  borderRadius: "6px",
                },
              }}
            >
              <MenuItem
                sx={{ fontSize: "12px" }}
                value="Select HTTP Method"
                disabled
              >
                Select HTTP Method
              </MenuItem>
              <MenuItem sx={{ fontSize: "12px" }} value="GET">
                GET
              </MenuItem>
              <MenuItem sx={{ fontSize: "12px" }} value="POST">
                POST
              </MenuItem>
              <MenuItem sx={{ fontSize: "12px" }} value="PUT">
                PUT
              </MenuItem>
              <MenuItem sx={{ fontSize: "12px" }} value="PATCH">
                PATCH
              </MenuItem>
              <MenuItem sx={{ fontSize: "12px" }} value="DELETE">
                DELETE
              </MenuItem>
            </Select>
            <TextFieldWithBorderComponent
              inputRef={requestUrlInputRef}
              label="Request URL"
              variant="outlined"
              placeholder=""
              name="key"
              fullWidth
              sx={{
                marginTop: "8px",
                fontSize: "12px",
              }}
              value={requestUrl}
              onChange={(e: any) => handleRequestUrlChange(e.target.value)}
              // onBlur={() => setTouched(true)}
              error={urlError} // Conditionally set the error state
              helperText={
                urlError && requestUrl === ""
                  ? "Url is Required"
                  : urlError
                  ? "Invalid URL format"
                  : ""
              }
              disabled={requestType === "Select HTTP Method"}
            />
          </div>
        </AccordionDetails>
      </Accordion>

      {/* Second Accordion: Customize Header */}
      <Accordion
        sx={{ padding: "0px", marginBottom: "8px" }}
        onChange={(_, expanded) => handleAccordionChange(expanded)}
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          sx={{ minHeight: "30px", padding: "1px 6px", margin: "0px" }}
        >
          <Typography sx={{ fontSize: "14px" }}>Customize Header</Typography>
        </AccordionSummary>
        <AccordionDetails sx={{ overflowY: "auto", padding: "8px" }}>
          {headers?.map((header, index) => (
            <div
              key={index}
              style={{
                alignItems: "center",
                marginBottom: "8px",
              }}
            >
              <Box sx={{ display: "flex" }}>
                <TextFieldWithBorderComponent
                  label="Key"
                  variant="outlined"
                  placeholder=""
                  name="key"
                  value={header.key}
                  onChange={(e: any) => {
                    handleHeaderChange(index, "key", e.target.value);
                  }}
                  sx={{ marginRight: "8px", fontSize: "12px" }}
                  fullWidth
                  disabled={index === 0}
                />
                <TextFieldWithBorderComponent
                  inputRef={headerValueInputRefs.current[index]}
                  label="Value"
                  variant="outlined"
                  placeholder=""
                  name="value"
                  value={header.value}
                  onChange={(e: any) => {
                    handleHeaderChange(index, "value", e.target.value);
                  }}
                  sx={{ marginRight: "8px", fontSize: "12px" }}
                  fullWidth
                  disabled={index === 0}
                />
                {index > 0 && (
                  <IconButton
                    color="secondary"
                    onClick={() => handleRemoveHeader(index)}
                    aria-label="remove header"
                    size="small"
                  >
                    <RemoveCircleOutlineIcon fontSize="small" />
                  </IconButton>
                )}
              </Box>
            </div>
          ))}
          <Typography
            variant="body2"
            color="#9400d3"
            onClick={handleAddHeader}
            sx={{ cursor: "pointer", fontSize: "12px", marginTop: "6px" }}
          >
            + Add another header
          </Typography>
        </AccordionDetails>
      </Accordion>

      {/* Third Accordion: Customize Body */}
      <Accordion
        sx={{ padding: "0px", marginBottom: "8px" }}
        onChange={(_, expanded) => handleAccordionChange(expanded)}
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          sx={{
            minHeight: "30px",
            padding: "1px 6px",
            margin: "0px",
          }}
        >
          <Box sx={{ display: "block" }}>
            <Typography sx={{ fontSize: "14px" }}>Customize Body</Typography>
          </Box>
        </AccordionSummary>
        <AccordionDetails sx={{ padding: "8px" }}>
          {isCurlMode ? (
            <>
              <Typography
                variant="body2"
                sx={{ fontSize: "12px", color: "gray" }}
              >
                Request Body Properties
              </Typography>
              {curlBodyProperties.map((prop, idx) => (
                <Box
                  key={idx}
                  sx={{
                    mb: 2,
                    p: 1,
                    border: `1px solid ${bgColors.green1}`,
                    borderRadius: 1,
                    background: bgColors.green1,
                  }}
                >
                  <TextFieldWithBorderComponent
                    label="Property Name"
                    name={`propertyName-${idx}`}
                    placeholder=""
                    value={prop.propertyName}
                    onChange={(e) =>
                      handlePropertyChange(idx, "propertyName", e.target.value)
                    }
                    sx={{ mb: 1 }}
                    error={curlPropertyErrors[idx]?.propertyName}
                    helperText={
                      curlPropertyErrors[idx]?.propertyName
                        ? "Property name is required"
                        : ""
                    }
                  />
                  <Box
                    sx={{
                      display: "flex",
                      gap: 1,
                      mb: 1,
                      alignItems: "center",
                    }}
                  >
                    <TextFieldWithBorderComponent
                      label="Variable"
                      name={`variable-${idx}`}
                      placeholder=""
                      value={prop.variable}
                      sx={{ flex: 0.5, minWidth: 60, maxWidth: 90 }}
                      disabled
                    />
                    <Select
                      label="Attribute"
                      value={prop.attribute}
                      onChange={(e) =>
                        handlePropertyChange(idx, "attribute", e.target.value)
                      }
                      displayEmpty
                      error={curlPropertyErrors[idx]?.attribute}
                      sx={{
                        minWidth: 120,
                        maxWidth: 180,
                        flex: 1,
                        height: 36,
                        "& .MuiSelect-select": {
                          height: 36,
                          display: "flex",
                          alignItems: "center",
                          padding: "0 14px",
                          fontSize: "14px",
                        },
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: bgColors.green,
                        },
                        "& .MuiInputBase-root": {
                          height: 36,
                        },
                      }}
                    >
                      <MenuItem value="" disabled>
                        Select Attribute
                      </MenuItem>
                      {attributesData?.map((opt: any) => (
                        <MenuItem key={opt.id} value={opt.name}>
                          {opt.name}
                        </MenuItem>
                      ))}
                    </Select>
                    {curlPropertyErrors[idx]?.attribute && (
                      <FormHelperText error sx={{ mt: 0.5 }}>
                        Please select an attribute
                      </FormHelperText>
                    )}
                  </Box>
                  <TextFieldWithBorderComponent
                    label="Fallback Value"
                    name={`fallback-${idx}`}
                    placeholder=""
                    value={prop.fallback}
                    onChange={(e) =>
                      handlePropertyChange(idx, "fallback", e.target.value)
                    }
                    sx={{ mb: 1 }}
                    error={curlPropertyErrors[idx]?.fallback}
                    helperText={
                      curlPropertyErrors[idx]?.fallback
                        ? "Fallback value is required"
                        : ""
                    }
                  />
                  <Box
                    sx={{ display: "flex", justifyContent: "center", mt: 1 }}
                  >
                    <IconButton
                      onClick={() => handleRemoveCurlBodyProperty(idx)}
                      size="small"
                      color="error"
                    >
                      <RemoveCircleOutlineIcon fontSize="small" />
                    </IconButton>
                  </Box>
                </Box>
              ))}
              <Box>
                <Typography
                  color={bgColors.green}
                  variant="body2"
                  sx={{
                    cursor: "pointer",
                    fontSize: "12px",
                    margin: "0px 6px",
                  }}
                  onClick={handleAddCurlBodyProperty}
                >
                  + Add property
                </Typography>
              </Box>
            </>
          ) : (
            <>
              <Typography
                variant="body2"
                sx={{ fontSize: "12px", color: "gray" }}
              >
                Request Body (JSON only)
              </Typography>
              <TextFieldWithBorderComponent
                inputRef={bodyDescriptionInputRef}
                label="Body"
                variant="outlined"
                placeholder=""
                name="body"
                value={bodyDescription}
                onChange={(e: any) => handleBodyDescriptionChange(e)}
                fullWidth
                multiline
                rows={4}
                sx={{ fontSize: "12px" }}
                error={jsonError || variablesError}
                helperText={
                  jsonError && bodyDescription === ""
                    ? "Body Description is Required"
                    : jsonError
                    ? "invalid json"
                    : variablesError
                    ? "Variables are not serialized"
                    : ""
                }
              />
              <Typography
                color={bgColors.green}
                variant="body2"
                sx={{ cursor: "pointer", fontSize: "12px", margin: "0px 6px" }}
                onClick={handleAddVariable}
              >
                + Add variable
              </Typography>
              {Array.isArray(variables?.webhookBodyVariables) &&
                variables.webhookBodyVariables.some(
                  (variable: any) => variable.type === 3
                ) && (
                  <VariableAssignmentComponentReactflow
                    requestUrl={requestUrl}
                    setRequestUrl={setRequestUrl}
                    headers={headers}
                    variables={variables}
                    variableType={3}
                    handleVariablesChange={handleVariablesChange}
                    handleRemoveVariable={handleRemoveVariable}
                    onVariablesValueChange={handleVariablesValueChange}
                    onVariableFallbackValueChange={
                      handleVariablesFallbackValueChange
                    }
                    attributesData={attributesData}
                  />
                )}
            </>
          )}
        </AccordionDetails>
      </Accordion>
    </Box>
  );
};

export default WebhookAccordionReactflow;
