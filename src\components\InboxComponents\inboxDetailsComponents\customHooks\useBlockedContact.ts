import { blockContact } from "../../../../redux/slices/Inbox/BlockContactSlice";
import { unblockContact } from "../../../../redux/slices/Inbox/UnblockContactSlice";
import { getAllBlockedContacts } from "../../../../redux/slices/Inbox/GetAllBlockedContactsSlice";
import { useAppSelector } from "../../../../utils/redux-hooks";
import { useAppDispatch } from "../../../../utils/redux-hooks";
import { useEffect, useState } from "react";
import { toastActions } from "../../../../utils/toastSlice";

export const useBlockedContacts = () => {
  const dispatch = useAppDispatch();
  const [blockedContacts, setBlockedContacts] = useState<any[]>([]);
  const isContactBlocked = (contact: any) => {
    if (!contact) return false;
    return blockedContacts.some(
      (blockedContact: any) => blockedContact?.contact === contact
    );
  };

  // Selectors
  const blockStatus = useAppSelector((state:any) => state.blockContactData.status);
  const unblockStatus = useAppSelector(
    (state:any) => state.unblockContactData.status
  );
  const blockedListStatus = useAppSelector(
    (state:any) => state.getBlockedContactsData.status
  );
  const blockedList = useAppSelector(
    (state:any) => state.getBlockedContactsData.data
  );

  // Block a contact
  const handleBlockContact = async (payload: any) => {
    const response = await dispatch(blockContact(payload));
    if (
      response?.meta?.requestStatus === "fulfilled" &&
      response?.payload?.success === true
    ) {
      handleFetchBlockedContacts({
        businessId: payload?.businessId,
      });
      dispatch(
        toastActions.setToaster({
          type: "success",
          message: response?.payload?.message,
        })
      );
      setBlockedContacts((prev) => [...prev, response?.payload?.data]);
    } else {
      dispatch(
        toastActions.setToaster({
          type: "error",
          message: response?.payload?.message,
        })
      );
    }
  };

  // Unblock a contact
  const handleUnblockContact = async (payload: any) => {
    const response = await dispatch(unblockContact(payload));
    if (
      response?.meta?.requestStatus === "fulfilled" &&
      response?.payload?.success === true
    ) {
      handleFetchBlockedContacts({
        businessId: payload?.businessId,
      });
      dispatch(
        toastActions.setToaster({
          type: "success",
          message: response?.payload?.message,
        })
      );
      setBlockedContacts((prev) =>
        prev.filter(
          (blockedContact) =>
            blockedContact?.contact !== response?.payload?.data?.contact
        )
      );
    } else {
      dispatch(
        toastActions.setToaster({
          type: "error",
          message: response?.payload?.message,
        })
      );
    }
  };

  // Fetch all blocked contacts
  const handleFetchBlockedContacts = async (payload: any) => {
    const response = await dispatch(getAllBlockedContacts(payload));
    if (
      response?.meta?.requestStatus === "fulfilled" &&
      response?.payload?.success === true
    ) {
      setBlockedContacts(response?.payload?.data);
    }
  };

  useEffect(() => {
    if (blockedList?.length > 0) {
      setBlockedContacts(blockedList);
    }
  }, [blockedList]);

  return {
    blockContact: handleBlockContact,
    unblockContact: handleUnblockContact,
    fetchBlockedContacts: handleFetchBlockedContacts,
    blockStatus,
    unblockStatus,
    blockedListStatus,
    blockedList,
    isContactBlocked,
  };
};
