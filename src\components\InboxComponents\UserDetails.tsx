import { <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import ChatInformationSvg from "../../assets/svgs/ChatInformationSvg";
import { makeStyles } from "@mui/styles";
import CloseSvg from "../../assets/svgs/CloseSvg";
import { bgColors } from "../../utils/bgColors";
import ChatUserEmailSvg from "../../assets/svgs/ChatUserEmailSvg";
import ChatUserLocationSvg from "../../assets/svgs/ChatUserLocationSvg";
import ChatUserArrowDownSvg from "../../assets/svgs/ChatUserArrowDownSvg";
import ChatUserArrowSide from "../../assets/svgs/ChatUserArrowSide";
import TagPopover from "./userDetailsComponents/TagPopover";
import { Delete, Flag, Block } from "@mui/icons-material";
import ChatUserCloseSvg from "../../assets/svgs/ChatUserCloseSvg";
import { useAppDispatch, useAppSelector } from "../../utils/redux-hooks";
import { toastActions } from "../../utils/toastSlice";
import SearchIconSvg2 from "../../assets/svgs/SearchIconSvg2";
import MediaPopover from "./userDetailsComponents/MediaPopover";
import SharedMedia from "./userDetailsComponents/sharedmedia";
import { removeContactTagsFromContact } from "../../redux/slices/Contacts/DeleteContactTags";
import { createContactNotes } from "../../redux/slices/Contacts/CreateContactNotes";
import { removeContactNotes } from "../../redux/slices/Contacts/DeleteContactNotes";
import { markContactAsSpam } from "../../redux/slices/Inbox/markContactAsSpam";
import LoadingComponent from "../common/LoadingComponent";
import TextFieldWithBorderComponent from "../common/TextFieldWithBorderComponent";
import { fetchInboxContactDetails } from "../../redux/slices/Inbox/InboxContactDetails";
import CustomAudioPlayer from "./userDetailsComponents/AudioComponent";
import SharedMediaDropdownComponent from "./userDetailsComponents/sharedMediaDropdownComponent";
import TagsDropdownComponent from "./userDetailsComponents/tagsDropdownComponent";
import NotesDropdownComponent from "./userDetailsComponents/notesDropdownComponent";
import WorkflowVariablesDropdownComponent from "./userDetailsComponents/workflowVariablesDropdownComponent";
import { useBlockedContacts } from "./inboxDetailsComponents/customHooks/useBlockedContact";

const useStyles = makeStyles({
  grayColor: {
    color: `#4B5A5A !important`,
    fontWeight: "600 !important",
  },
  grayColor2: {
    color: `#4B5A5A !important`,
    opacity: "80%",
  },
  searchField: {
    width: "100%",
    borderRadius: "12px",
    height: "45px",
    fontSize: "18px",
    backgroundColor: "transparent",
    "& input::placeholder": {
      textAlign: "left",
      fontSize: "14px",
      fontFamily: "Roboto Slab",
      color: "#4B5A5A !important",
      Opacity: "60%",
    },
  },
  spaceBetweenCenter: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  flexRowCenter: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  },
  cursor: {
    cursor: "pointer",
  },
});

// Helper function to filter images and videos
export const filterMedia = (mediaArray: any) => {
  const validExtensions = [
    ".png",
    ".jpg",
    ".jpeg",
    ".gif",
    ".mp4",
    ".mp3",
    ".aar",
    ".amr",
    ".ogg",
  ];
  return mediaArray?.filter((media: any) =>
    validExtensions?.some((ext) => media?.mediaUrl?.endsWith(ext))
  );
};

interface UserDetailsProps {
  details: any;
  status: string;
  messages: any[];
  currentResultIndex: number;
  setCurrentResultIndex: (index: number) => void;
  onClickClose: (value: boolean) => void;
  setSearchText: (text: string) => void;
  contactNumber?: string;
  setChatsPageNumber: (page: number) => void;
  handleBlockContact: () => void;
  handleUnblockContact: () => void;
  isBlocked: boolean;
  contactChatStatus: string;
}

const UserDetails = ({
  details,
  status,
  messages,
  currentResultIndex,
  setCurrentResultIndex,
  onClickClose,
  setSearchText,
  contactNumber,
  setChatsPageNumber,
  handleBlockContact,
  handleUnblockContact,
  isBlocked,
  contactChatStatus,
}: UserDetailsProps) => {
  const classes = useStyles();
  const dispatch = useAppDispatch();
  const userProfileSlice = useAppSelector((state: any) => state?.adminLogin);
  const userData = userProfileSlice?.data;
  const {
    isContactBlocked,
    blockContact,
    unblockContact,
    fetchBlockedContacts,
    blockStatus,
    unblockStatus,
    blockedListStatus,
    blockedList,
  } = useBlockedContacts();
  const manageInboxObject = useAppSelector(
    (state: any) => state.getUserPermissions?.data?.inbox
  );
  const inboxUserObject = manageInboxObject?.find((item: any) =>
    Object.prototype.hasOwnProperty.call(item, "inboxUserDetails")
  );
  const inboxUserActions = inboxUserObject
    ? inboxUserObject?.inboxUserDetails
    : [];

  const [media, setMedia] = useState(false);
  const [workflowVariables, setWorkflowVariables] = useState(false);
  const [tags, setTags] = useState(false);
  const [openNotes, setOpenNotes] = useState(false);
  const [writtenNotes, setWrittenNotes] = useState("");
  const [, setSelectedTags] = useState<any[]>([]);
  const [searchInput, setSearchInput] = useState("");
  const [tagPopoverAnchorEl, setTagPopoverAnchorEl] = useState(null);
  const [addTagTooltipOpen, setAddTagTooltipOpen] = useState(false);
  const [updateTagTooltipOpen, setUpdateTagTooltipOpen] = useState("");
  const [addNotesTooltipOpen, setAddNotesTooltipOpen] = useState(false);
  const [deleteNotestooltipOpen, setDeleteNotestooltipOpen] = useState("");
  const [openDialog, setOpenDialog] = useState(false);
  const [seeAllTooltipOpen, setSeeAllTooltipOpen] = useState(false);
  const [rightSidebarOpen, setRightSidebarOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isBlockLoading, setIsBlockLoading] = useState(false);
  // const [contactDetails, setContactDetails] = useState(details);

  const [searchResults, setSearchResults] = useState<any[]>([]);
  const userDataProp = useAppSelector((state: any) => state?.adminLogin?.data);

  const containsAllWords = (obj: any, words: string[]): boolean => {
    if (typeof obj === "string") {
      const lowerCasedText = obj.toLowerCase();
      return words.every((word) => lowerCasedText.includes(word));
    } else if (Array.isArray(obj)) {
      return obj.some((item) => containsAllWords(item, words));
    } else if (obj && typeof obj === "object") {
      return Object.values(obj).some((value) => containsAllWords(value, words));
    }
    return false;
  };

  useEffect(() => {
    if (searchInput) {
      const searchText = searchInput?.toLowerCase(); // Convert search input to lowercase
      const words = searchText?.split(/\s+/); // Split input into words based on whitespace
      const results: any =
        Array.isArray(messages) &&
        messages?.filter((message: any) => containsAllWords(message, words));

      setSearchResults(results || []);
      setCurrentResultIndex(0);
    } else {
      setSearchResults([]);
    }
  }, [searchInput, messages]);

  const hasAcess = (permission: any) => {
    if (inboxUserActions?.includes(permission)) {
      return true;
    }
    return false;
  };

  useEffect(() => {
    fetchBlockedContacts({
      businessId: userData?.companyId,
      contact: contactNumber,
    });
  }, [contactNumber]);

  // Handle search text change
  const handleSearchTextChange = (event: any) => {
    const searchText = event.target.value;
    setSearchInput(searchText);
    setSearchText(searchText); // Pass the search text to parent component
  };

  // const handleNextResult = () => {
  //   setCurrentResultIndex(
  //     (prevIndex: number) => (prevIndex + 1) % searchResults.length
  //   );
  // };

  // const handlePreviousResult = () => {
  //   setCurrentResultIndex(
  //     (prevIndex: number) =>
  //       (prevIndex - 1 + searchResults.length) % searchResults.length
  //   );
  // };

  const handleOpenTagPopover = (event: any) => {
    const hasPermission = hasAcess("addTag");

    if (hasPermission) {
      setTagPopoverAnchorEl(event.currentTarget);
    } else {
      setAddTagTooltipOpen(true);
      setSeeAllTooltipOpen(false);
      setTimeout(() => setAddTagTooltipOpen(false), 2000);
    }
  };

  const handleCloseTagPopover = () => {
    setTagPopoverAnchorEl(null);
  };

  const handleSelectTags = (tags: any) => {
    setSelectedTags(tags);
  };

  const handleRemoveTagFromContact = async (tag: string) => {
    const hasPermission = hasAcess("updateTag");
    if (hasPermission) {
      try {
        const data = {
          businessId: userData?.companyId,
          userId: userData?.userId,
          tag: [tag],
          contactIds: [details?.contactId],
        };

        const response = await dispatch(removeContactTagsFromContact(data));

        if (response?.meta?.requestStatus === "fulfilled") {
          dispatch(
            toastActions.setToaster({
              type: "success",
              message: response?.payload?.message,
            })
          );
          const data = {
            businessId: userDataProp?.companyId,
            contactId: contactNumber,
          };
          dispatch(fetchInboxContactDetails(data));
          // window.location.reload();
        } else {
          dispatch(
            toastActions.setToaster({
              type: "error",
              message: response?.payload?.message,
            })
          );
        }
      } catch (err: any) {
        dispatch(
          toastActions.setToaster({
            type: "error",
            message: err?.reponse?.data?.message,
          })
        );
      }
      handleCloseTagPopover();
    } else {
      setUpdateTagTooltipOpen(tag);
      setTimeout(() => setUpdateTagTooltipOpen(""), 2000);
    }
  };

  const handleSaveNote = async () => {
    const hasPermission = hasAcess("createNotes");
    if (hasPermission) {
      try {
        const data = {
          businessId: userData?.companyId,
          userId: userData?.userId,
          note: [writtenNotes],
          contactId: details?.contactId,
        };

        const response = await dispatch(createContactNotes(data));

        if (response?.meta?.requestStatus === "fulfilled") {
          dispatch(
            toastActions.setToaster({
              type: "success",
              message: response?.payload?.message,
            })
          );
          const data = {
            businessId: userDataProp?.companyId,
            contactId: contactNumber,
          };
          setWrittenNotes("");
          dispatch(fetchInboxContactDetails(data));
          // window.location.reload();
        } else {
          dispatch(
            toastActions.setToaster({
              type: "error",
              message: response?.payload?.message,
            })
          );
        }
      } catch (err: any) {
        dispatch(
          toastActions.setToaster({
            type: "error",
            message: err?.response?.data?.message,
          })
        );
      }
    } else {
      setAddNotesTooltipOpen(true);
      setTimeout(() => setAddNotesTooltipOpen(false), 2000);
    }
  };

  const handleDeleteNotes = async (noteId: string) => {
    const hasPermission = hasAcess("deleteNotes");
    if (hasPermission) {
      try {
        const data = {
          businessId: userData?.companyId,
          userId: userData?.userId,
          note: [noteId],
          contactId: details?.contactId,
        };

        const response = await dispatch(removeContactNotes(data));

        if (response?.meta?.requestStatus === "fulfilled") {
          dispatch(
            toastActions.setToaster({
              type: "success",
              message: response?.payload?.message,
            })
          );
          // window.location.reload();

          const data = {
            businessId: userDataProp?.companyId,
            contactId: contactNumber,
          };
          dispatch(fetchInboxContactDetails(data));
        } else {
          dispatch(
            toastActions.setToaster({
              type: "error",
              message: response?.payload?.message,
            })
          );
        }
      } catch (err: any) {
        dispatch(
          toastActions.setToaster({
            type: "error",
            message: err?.response?.data?.message,
          })
        );
      }
    } else {
      setDeleteNotestooltipOpen(noteId);
      setTimeout(() => setDeleteNotestooltipOpen(""), 2000);
    }
  };

  const handleSpamContact = async () => {
    // const hasPermission = hasCloseChatPermission(getuserPermissionData?.inbox);
    // if (hasPermission) {
    setIsLoading(true);
    try {
      const postData = {
        businessId: userData?.companyId,
        userId: userData?.userId,
        contact: details?.phoneNumber,
      };
      const spamResponse = await dispatch(markContactAsSpam(postData));

      if (spamResponse?.meta?.requestStatus === "fulfilled") {
        // setContactDetails({
        //   ...contactDetails,
        //   isSpam: !contactDetails.isSpam,
        // });
        const data = {
          businessId: userDataProp?.companyId,
          contactId: contactNumber,
        };
        dispatch(fetchInboxContactDetails(data));
        setChatsPageNumber(1);
        dispatch(
          toastActions.setToaster({
            type: "success",
            message: spamResponse?.payload?.message,
          })
        );
        // window?.location?.reload();
      } else {
        dispatch(
          toastActions.setToaster({
            type: "error",
            message: spamResponse?.payload?.message,
          })
        );
      }
    } catch (err: any) {
      dispatch(
        toastActions.setToaster({
          type: "error",
          message: err?.response?.data?.message,
        })
      );
    } finally {
      setIsLoading(false);
    }
    // } else {
    //   setCloseChatTooltipOpen(true);
    //   setTimeout(() => {
    //     setCloseChatTooltipOpen(false);
    //   }, 2000);
    // }
  };

  const handleClose = () => {
    setSearchText("");
    onClickClose(false);
  };

  const handleOpenRightSidebar = () => {
    setSeeAllTooltipOpen(true);
    setTimeout(() => setSeeAllTooltipOpen(false), 2000);
    setRightSidebarOpen(true);
    const data = {
      businessId: userData?.companyId,
      contactId: contactNumber,
    };
    dispatch(fetchInboxContactDetails(data));
  };

  const handleCloseRightSidebar = () => {
    setRightSidebarOpen(false);
  };

  const handleOpenMediaPopover = (event: any) => {
    setOpenDialog(true);
    setMedia(event);
  };

  const handleCloseMediaPopover = () => {
    setOpenDialog(false);
  };

  // Filter the sharedMedia to get documents
  const documentExtensions = [".pdf", ".doc", ".docx", ".xlsx"];
  const sharedDocuments = details?.shareMedia?.filter((media: any) =>
    documentExtensions?.some((ext) => media?.mediaUrl?.endsWith(ext))
  );

  const filteredMedia = details?.shareMedia
    ? filterMedia(details.shareMedia)
    : [];

  const filteredLinks =
    // messages?.length > 0 ? getLinksFromMessages(messages)
    details?.links?.length > 0 ? details?.links : [];

  useEffect(() => {
    if (searchInput) {
      const searchText = searchInput.toLowerCase(); // Convert search input to lowercase
      const results =
        messages?.length > 0 &&
        messages?.filter((message: any) => {
          if (typeof message?.textMessage !== "string") return false;
          const messageText = message.textMessage.toLowerCase();
          const isMatch = messageText.includes(searchText);
          return isMatch;
        });

      setSearchResults(results || []);
      setCurrentResultIndex(0);
    } else {
      setSearchResults([]);
    }
  }, [searchInput, messages]);

  return (
    <Box
      m={{ xs: 1, md: 2 }}
      sx={{
        height:
          !isBlocked &&
          !contactChatStatus?.includes("expired") &&
          !contactChatStatus?.includes("new")
            ? "90vh"
            : "100vh",
        width: "94%",
        scrollbarWidth: "thin",
        overflowY: "auto",
        // overflowY: "hidden",
        // zIndex: 100,
      }}
    >
      {status === "loading" ? (
        <LoadingComponent height="100%" color={bgColors?.blue} />
      ) : (
        <>
          <Box ml={0.5} className={classes.spaceBetweenCenter}>
            <Box className={classes.flexRowCenter}>
              <ChatInformationSvg />
              <Typography
                sx={{ fontSize: 16 }}
                ml={2.5}
                className={classes.grayColor}
              >
                User Details
              </Typography>
            </Box>

            <Box onClick={handleClose} className={classes.cursor}>
              {/* <ChatUserDetailsCloseIcon /> */}
              <CloseSvg />
            </Box>
          </Box>
          <Box className={classes.flexRowCenter} my={2}>
            {/* <Box> */}
            <img
              src={details?.logo || "/images/profile.png"}
              alt="profile"
              height={40}
              style={{ marginLeft: -3 }}
            />
            {/* </Box> */}
            <Box ml={1}>
              <Typography
                sx={{ fontSize: "14px" }}
                className={classes.grayColor}
              >
                {details?.name}
              </Typography>
              <Typography
                sx={{ fontSize: "12px" }}
                className={classes.grayColor2}
              >
                +{details?.phoneNumber}
              </Typography>
            </Box>
          </Box>
          <Box className={classes.flexRowCenter}>
            <ChatUserEmailSvg />
            <Typography
              ml={2}
              sx={{ fontSize: "14px" }}
              className={classes.grayColor2}
            >
              {details?.email || "N/A"}
            </Typography>
          </Box>
          <Box mt={2} className={classes.flexRowCenter}>
            <ChatUserLocationSvg />
            <Typography
              ml={2}
              sx={{ fontSize: "14px" }}
              className={classes.grayColor2}
            >
              {details?.countryName || "N/A"}
            </Typography>
          </Box>
          <SharedMediaDropdownComponent
            media={media}
            seeAllTooltipOpen={seeAllTooltipOpen}
            handleOpenRightSidebar={handleOpenRightSidebar}
            handleOpenMediaPopover={handleOpenMediaPopover}
            rightSidebarOpen={rightSidebarOpen}
            handleCloseRightSidebar={handleCloseRightSidebar}
            filteredMedia={filteredMedia}
            sharedDocuments={sharedDocuments}
            filteredLinks={filteredLinks}
            setMedia={setMedia}
            setSeeAllTooltipOpen={setSeeAllTooltipOpen}
            details={details}
          />

          <Box my={4} className={classes.flexRowCenter}>
            <TextField
              className={classes.searchField}
              variant="standard"
              size="small"
              fullWidth
              value={searchInput}
              onChange={handleSearchTextChange}
              InputProps={{
                disableUnderline: true,
                style: { padding: "10px", fontSize: "12px" },
                startAdornment: (
                  <IconButton
                    sx={{ p: 0, color: "inherit" }}
                    aria-label="search"
                  >
                    {/* <SearchIconSvg /> */}
                    <SearchIconSvg2 />
                  </IconButton>
                ),
              }}
              placeholder="Search Messages..."
            />
            {searchInput && (
              <Box display="flex" alignItems="center" mt={1}>
                <Typography sx={{ fontSize: 10, minWidth: 80 }}>
                  {
                    // currentResultIndex + "/" +
                    searchResults?.length
                  }{" "}
                  results found!
                </Typography>
              </Box>
            )}
          </Box>

          {details?.contactExist && (
            <>
              <TagsDropdownComponent
                tags={tags}
                details={details}
                handleOpenTagPopover={handleOpenTagPopover}
                addTagTooltipOpen={addTagTooltipOpen}
                setTags={setTags}
                setAddTagTooltipOpen={setAddTagTooltipOpen}
                updateTagTooltipOpen={updateTagTooltipOpen}
                setUpdateTagTooltipOpen={setUpdateTagTooltipOpen}
                handleRemoveTagFromContact={handleRemoveTagFromContact}
              />
              <NotesDropdownComponent
                notes={openNotes}
                setNotes={setOpenNotes}
                writtenNotes={writtenNotes}
                setWrittenNotes={setWrittenNotes}
                addNotesTooltipOpen={addNotesTooltipOpen}
                setAddNotesTooltipOpen={setAddNotesTooltipOpen}
                details={details}
                handleSaveNote={handleSaveNote}
                deleteNotestooltipOpen={deleteNotestooltipOpen}
                setDeleteNotestooltipOpen={setDeleteNotestooltipOpen}
                handleDeleteNotes={handleDeleteNotes}
              />
              <WorkflowVariablesDropdownComponent
                workflowVariables={workflowVariables}
                details={details}
                setWorkflowVariables={setWorkflowVariables}
              />
            </>
          )}
          <Box
            my={4}
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "flex-start",
              gap: "15px",
            }}
          >
            <Box onClick={() => handleSpamContact()}>
              {isLoading ? (
                <LoadingComponent height="auto" color={bgColors?.blue} />
              ) : (
                <Box sx={{ display: "flex", alignItems: "center" }}>
                  <Flag
                    sx={{
                      color: !details?.isSpam ? bgColors?.red : "#2E7D32",
                    }}
                  />
                  <Typography
                    ml={1}
                    sx={{
                      fontSize: "14px",
                      cursor: "pointer",
                      color: !details?.isSpam ? bgColors?.red : "#2E7D32",
                      fontWeight: "600 !important",
                    }}
                  >
                    {!details?.isSpam ? "Mark as spam" : "Mark as not spam"}
                  </Typography>
                </Box>
              )}
            </Box>

            <Box
              onClick={
                contactChatStatus?.includes("expired")
                  ? undefined
                  : isBlocked
                  ? handleUnblockContact
                  : handleBlockContact
              }
            >
              {blockStatus === "loading" || unblockStatus === "loading" ? (
                <LoadingComponent height="auto" color={bgColors?.blue} />
              ) : (
                <Box sx={{ display: "flex", alignItems: "center" }}>
                  <Block
                    sx={{
                      color: isBlocked ? "#2E7D32" : bgColors?.red,
                      opacity: contactChatStatus?.includes("expired") ? 0.5 : 1,
                    }}
                  />
                  <Tooltip
                    title={contactChatStatus?.includes("expired") ? "Expired contacts cannot be blocked." : ""}
                    arrow
                    disableHoverListener={!contactChatStatus?.includes("expired")}
                  >
                    <Typography
                      ml={1}
                      sx={{
                        fontSize: "14px",
                        cursor: contactChatStatus?.includes("expired")
                          ? "not-allowed"
                          : "pointer",
                        color: isBlocked ? "#2E7D32" : bgColors?.red,
                        fontWeight: "600 !important",
                        display: "flex",
                        alignItems: "center",
                        opacity: contactChatStatus?.includes("expired") ? 0.5 : 1,
                      }}
                    >
                      {isBlocked ? "Unblock contact" : "Block contact"}
                    </Typography>
                  </Tooltip>
                </Box>
              )}
            </Box>
          </Box>
          <MediaPopover
            open={openDialog}
            media={media}
            onClose={handleCloseMediaPopover}
            sharedMedia={media}
          />
          <TagPopover
            anchorEl={tagPopoverAnchorEl}
            contactDetails={details}
            onClose={handleCloseTagPopover}
            onSelectTags={handleSelectTags}
            onRemoveTag={handleRemoveTagFromContact}
            contactNumber={contactNumber}
          />
        </>
      )}
    </Box>
  );
};

export default UserDetails;
