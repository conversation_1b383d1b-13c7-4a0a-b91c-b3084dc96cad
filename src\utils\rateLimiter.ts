/**
 * Rate Limiting Utility for SignalR Contact API Calls
 *
 * This utility manages rate limiting for contact API calls during campaigns
 * and persists the block state in localStorage to survive page refreshes.
 */

import { useState, useEffect, useCallback } from "react";

interface RateLimitState {
  callTimestamps: number[];
  isBlocked: boolean;
  blockStartTime: number | null;
}

// Rate limiting configuration constants
const RATE_LIMIT_WINDOW = 10000; // 10 seconds
const MAX_CALLS = 50;
const BLOCK_DURATION = 5 * 60 * 1000; // 5 minutes
const STORAGE_KEY = "contact_api_rate_limit_state";

/**
 * Gets the current rate limit state from localStorage or returns default state
 */
const getRateLimitState = (): RateLimitState => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      const parsed = JSON.parse(stored);
      return {
        callTimestamps: parsed.callTimestamps || [],
        isBlocked: parsed.isBlocked || false,
        blockStartTime: parsed.blockStartTime || null,
      };
    }
  } catch (error) {
    console.warn("[Rate Limiter] Error reading from localStorage:", error);
  }

  return {
    callTimestamps: [],
    isBlocked: false,
    blockStartTime: null,
  };
};

/**
 * Saves the current rate limit state to localStorage
 */
const saveRateLimitState = (state: RateLimitState): void => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(state));
  } catch (error) {
    console.warn("[Rate Limiter] Error saving to localStorage:", error);
  }
};

/**
 * Checks if the current API call should be allowed based on rate limiting rules
 * Returns true if the call should be allowed, false if it should be blocked
 */
export const checkRateLimit = (): boolean => {
  const now = Date.now();
  const state = getRateLimitState();

  // Check if currently blocked
  if (state.isBlocked && state.blockStartTime) {
    const timeSinceBlock = now - state.blockStartTime;
    if (timeSinceBlock < BLOCK_DURATION) {
      const remainingTime = Math.ceil((BLOCK_DURATION - timeSinceBlock) / 1000);
      console.warn(`[Rate Limiter] Blocked for ${remainingTime} more seconds`);
      return false; // Still blocked
    } else {
      // Block period expired, reset state
      const resetState: RateLimitState = {
        callTimestamps: [],
        isBlocked: false,
        blockStartTime: null,
      };
      saveRateLimitState(resetState);
      console.log(
        "[Rate Limiter] Block period expired, resuming normal operation"
      );
    }
  }

  // Add current timestamp
  const updatedTimestamps = [...state.callTimestamps, now];

  // Remove timestamps older than the rate limit window
  const recentTimestamps = updatedTimestamps.filter(
    (timestamp) => now - timestamp <= RATE_LIMIT_WINDOW
  );

  // Check if rate limit exceeded
  if (recentTimestamps.length > MAX_CALLS) {
    console.warn(
      `[Rate Limiter] Exceeded ${MAX_CALLS} calls in ${
        RATE_LIMIT_WINDOW / 1000
      } seconds. Blocking for ${BLOCK_DURATION / 1000 / 60} minutes.`
    );

    const blockedState: RateLimitState = {
      callTimestamps: [],
      isBlocked: true,
      blockStartTime: now,
    };
    saveRateLimitState(blockedState);
    return false; // Block this call
  }

  // Update state with recent timestamps
  const updatedState: RateLimitState = {
    callTimestamps: recentTimestamps,
    isBlocked: false,
    blockStartTime: null,
  };
  saveRateLimitState(updatedState);

  // Log current rate limit status for debugging
  if (recentTimestamps.length > 25) {
    // Log when approaching limit (half of max calls)
    console.log(
      `[Rate Limiter] Current calls in window: ${recentTimestamps.length}/${MAX_CALLS}`
    );
  }

  return true; // Allow this call
};

/**
 * Gets the current block status and remaining time
 * Returns null if not blocked, or an object with block info if blocked
 */
export const getBlockStatus = (): {
  isBlocked: boolean;
  remainingMinutes?: number;
} => {
  const state = getRateLimitState();

  if (!state.isBlocked || !state.blockStartTime) {
    return { isBlocked: false };
  }

  const now = Date.now();
  const timeSinceBlock = now - state.blockStartTime;

  if (timeSinceBlock >= BLOCK_DURATION) {
    // Block has expired, clean up state
    const resetState: RateLimitState = {
      callTimestamps: [],
      isBlocked: false,
      blockStartTime: null,
    };
    saveRateLimitState(resetState);
    return { isBlocked: false };
  }

  const remainingTime = BLOCK_DURATION - timeSinceBlock;
  const remainingMinutes = Math.ceil(remainingTime / 1000 / 60);

  return {
    isBlocked: true,
    remainingMinutes,
  };
};

/**
 * Manually resets the rate limiter state (for testing or admin purposes)
 */
export const resetRateLimit = (): void => {
  const resetState: RateLimitState = {
    callTimestamps: [],
    isBlocked: false,
    blockStartTime: null,
  };
  saveRateLimitState(resetState);
  console.log("[Rate Limiter] State manually reset");
};

/**
 * Gets current call count in the rate limit window (for debugging)
 */
export const getCurrentCallCount = (): number => {
  const now = Date.now();
  const state = getRateLimitState();

  const recentTimestamps = state.callTimestamps.filter(
    (timestamp) => now - timestamp <= RATE_LIMIT_WINDOW
  );

  return recentTimestamps.length;
};

/**
 * React hook for managing rate limit banner visibility
 * Returns the current block status and a function to refresh it
 */
export const useRateLimitBanner = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [blockInfo, setBlockInfo] = useState<{
    isBlocked: boolean;
    remainingMinutes?: number;
  }>({ isBlocked: false });

  const updateBannerStatus = useCallback(() => {
    const status = getBlockStatus();
    setIsVisible(status.isBlocked);
    setBlockInfo(status);
  }, []);

  useEffect(() => {
    // Check immediately
    updateBannerStatus();

    // Set up interval to check every minute
    const interval = setInterval(updateBannerStatus, 60000);

    return () => clearInterval(interval);
  }, [updateBannerStatus]);

  return {
    isVisible,
    blockInfo,
    updateBannerStatus,
  };
};
