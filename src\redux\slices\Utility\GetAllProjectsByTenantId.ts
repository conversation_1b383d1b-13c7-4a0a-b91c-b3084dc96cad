import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { LEADRAT_QA_APIS } from "../../../Apis/Utility/leadratApis";

const initialState = {
  status: "idle",
  data: null,
};

export const GetAllProjectsByTenantId = createAsyncThunk(
  "GetAllProjectsByTenantId",
  async (data: any) => {
    const response = await LEADRAT_QA_APIS.getAllWorkflowProjectsByTenantId(
      data
    );
    return response?.data;
  }
);

export const GetAllProjectsByTenantIdSlice = createSlice({
  name: "GetAllProjectsByTenantIdSlice",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(GetAllProjectsByTenantId.pending, (state) => {
        state.status = "loading";
      })
      .addCase(GetAllProjectsByTenantId.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.data = action.payload;
      })
      .addCase(GetAllProjectsByTenantId.rejected, (state) => {
        state.status = "failed";
      });
  },
});

export const GetAllProjectsByTenantIdActions =
  GetAllProjectsByTenantIdSlice.actions;
export default GetAllProjectsByTenantIdSlice.reducer;
