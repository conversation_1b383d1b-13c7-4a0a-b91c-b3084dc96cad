import { Box, Tooltip } from "@mui/material";
import { makeStyles } from "@mui/styles";
import { useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "../../utils/redux-hooks";
import EditIconSvg from "../../assets/svgs/EditIconSvg";
import DeleteIconSvg from "../../assets/svgs/DeleteIconSvg";
import DeletePopUp from "../common/DeletePopup";
import { bgColors } from "../../utils/bgColors";
import NewTagMember from "../ContactsComponents/NewTagMember";
import { CONTACT_TAGS_APIS } from "../../Apis/Contacts/ContactTagsApis";
import { toastActions } from "../../utils/toastSlice";
import { getContactTags } from "../../redux/slices/Contacts/getContactTags";
import CommonTable from "../common/CommonTable";

const TagsTableData = ({ data, newTagDialog, filteredTags }: any) => {
  const dispatch = useAppDispatch();
  const getTagsData = useAppSelector((state: any) => state?.getContactTagsData);
  const getContactTag = getTagsData?.data;
  const userProfileSlice = useAppSelector((state: any) => state?.adminLogin);
  const userData = userProfileSlice?.data;
  const filteredOptions = getContactTag?.filter(
    (option: { tag: string }) => option?.tag?.trim() !== ""
  );

  const getuserPermissionData = useAppSelector(
    (state: any) => state.getUserPermissions?.data
  );
  const manageContactsObject = getuserPermissionData?.contacts;

  const formatDate = (datetime: any) => {
    const date = new Date(datetime);
    const year = date.getFullYear();
    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];
    const month = monthNames[date.getMonth()];
    const day = ("0" + date.getDate()).slice(-2);
    return `${day} ${month} ${year}`;
  };
  const [tableData, setTableData] = useState(filteredOptions);

  const [deleteTag, setDeleteTag] = useState(false);
  const [deleteId, setDeleteId] = useState(null);
  const [isDeleteLoading, setIsDeleteTagLoading] = useState(false);
  const [deleteTagsTooltip, setDeleteTagsTooltip] = useState(null);
  const [editTagPopup, setEditTagPopup] = useState(false);
  const [editTag, setEditTag] = useState(null);
  const [tagId, seTagId] = useState(null);

  const handleCloseDelete = () => {
    setDeleteTag(false);
  };
  const hasDeleteTagPermission = (permission: any) => {
    for (const profileItem of permission) {
      if (Object.prototype.hasOwnProperty.call(profileItem, "deleteTags")) {
        return true;
      }
    }
  };
  const handleDeleteTag = async (tagId: any) => {
    const hasPermission = hasDeleteTagPermission(manageContactsObject);
    if (hasPermission) {
      setDeleteTag(true);
      const data = {
        businessId: userData?.companyId,
        userId: userData?.userId,
        tagIds: deleteId,
      };
      setIsDeleteTagLoading(true);
      const getDelRes = await CONTACT_TAGS_APIS.deleteTags(data);
      if (getDelRes?.status === 200) {
        handleCloseDelete();
        setIsDeleteTagLoading(false);
        dispatch(
          toastActions.setToaster({
            type: "success",
            message: "Tag Deleted Successfully",
          })
        );
        setIsDeleteTagLoading(false);
        const body = {
          businessId: userData?.companyId,
          userId: userData?.userId,
          search: "",
        };
        // await dispatch(getContactTags(body));
        await dispatch(getContactTags(body));
      }
    } else {
      setDeleteTagsTooltip(tagId);
      setTimeout(() => {
        setDeleteTagsTooltip(null);
      }, 2000);
    }
  };
  // const isDeleteLoading=()=>{

  // }
  const handleCloseEditTagPopover = () => {
    setEditTagPopup(false);
  };
  useEffect(() => {
    setTableData(filteredTags);
  }, [data, filteredTags]);

  const columns = [
    {
      id: "index",
      label: "So.no",
    },
    {
      id: "tagName",
      label: "Tag Name",
    },
    {
      id: "createdBy",
      label: "Created By",
    },
    {
      id: "createdAt",
      label: "Created At",
    },
    {
      id: "updatedAt",
      label: "Updated At",
    },
  ];

  const renderActions = (row: any) => {
    return (
      <Box sx={{ display: "flex", gap: "5px" }}>
        <Tooltip title="Edit" placement="top">
          <Box
            onClick={(e) => {
              e.stopPropagation();
              {
                data === "Tags" && setEditTagPopup(true);
                setEditTag(row?.tag);
                seTagId(row?.id);
              }
            }}
            sx={{ cursor: "pointer" }}
          >
            <EditIconSvg />
          </Box>
        </Tooltip>
        <Tooltip title="Delete" placement="top">
          <Box
            onClick={(e) => {
              e.stopPropagation();

              {
                data === "Tags" && setDeleteTag(true);
                setDeleteId(row?.id);
              }
            }}
            sx={{ cursor: "pointer" }}
          >
            <DeleteIconSvg />
          </Box>
        </Tooltip>
      </Box>
    );
  };

  const transformedData = tableData?.map((row: any, index: number) => ({
    ...row,
    index: index + 1,
    tagName: row?.tag,
    createdBy: row?.createdBy?.trim() === "" ? "N/A" : row?.createdBy,
    createdAt: formatDate(row?.createdAt),
    updatedAt: formatDate(row?.updatedAt),
  }));

  return (
    <>
      <CommonTable
        columns={columns}
        data={transformedData}
        actions={renderActions}
        showPagination={false}
      />

      <DeletePopUp
        title="selected tag"
        open={deleteTag}
        handleClose={handleCloseDelete}
        handleDelete={handleDeleteTag}
        handleLoad={isDeleteLoading}
      />
      <NewTagMember
        open={editTagPopup}
        handleClose={handleCloseEditTagPopover}
        isEdit={editTag}
        tagId={tagId}
      />
    </>
  );
};

export default TagsTableData;
