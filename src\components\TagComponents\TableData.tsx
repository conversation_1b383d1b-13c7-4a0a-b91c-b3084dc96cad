import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TableContainer,
  <PERSON><PERSON>ip,
  Typography,
} from "@mui/material";
import { makeStyles } from "@mui/styles";
import React, { useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "../../utils/redux-hooks";
import EditIconSvg from "../../assets/svgs/EditIconSvg";
import DeleteIconSvg from "../../assets/svgs/DeleteIconSvg";
import DeletePopUp from "../common/DeletePopup";
import EditPopOver from "../ContactsComponents/EditPopOver";
import { bgColors } from "../../utils/bgColors";
import NewTagMember from "../ContactsComponents/NewTagMember";
import { CONTACT_TAGS_APIS } from "../../Apis/Contacts/ContactTagsApis";
import { toastActions } from "../../utils/toastSlice";
import { getContactTags } from "../../redux/slices/Contacts/getContactTags";
import LoadingComponent from "../common/LoadingComponent";
import CommonTable from "../common/CommonTable";

const useStyles = makeStyles({
  bgContainer: {
    backgroundColor: bgColors.white,
    borderRadius: "25px",
    overflow: "100%",
    // height:"100vh !important",
    height: "calc(100vh - 3px)",
    width: "100%",
    // overflowY:"hidden",
    // scrollbarWidth:"none"
  },
  manageTeamContainer: {
    display: "flex",
    alignItems: "center",
    width: "full",
  },
  blackColor: {
    color: `${bgColors.black1} !important`,
    // fontWeight: "600 !important",
  },
  searchField: {
    width: "100%",
    borderRadius: "12px",
    // height: "38px",
    // backgroundColor: bgColors.white2,
    backgroundColor: "white",
    "& input::placeholder": {
      textAlign: "left",
      fontSize: "14px",
      fontFamily: "inter",
      color: "#000000 !important",
    },
  },
  SaveChangesButton: {
    // backgroundColor: bgColors.green,
    // color: bgColors.white,
    color: bgColors.green,
    border: `1px solid ${bgColors.green}`,
    borderRadius: "8px",
    // width: "fit-content",
    width: "140px",
    height: "32px",
    // padding: "10px",
    cursor: "pointer",
  },
  messageCount: {
    backgroundColor: bgColors?.gray5,
    color: bgColors?.black,
    borderRadius: "24px",
    height: "24px",
    width: "34px",
    fontSize: "10px",
    textAlign: "center",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  },
  tagsCount: {
    backgroundColor: "#F4F4F4",
    borderRadius: "24px",
    padding: "3px",
    height: "25px",
    width: "150px",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    overflow: "hidden",
    whiteSpace: "nowrap",
    textOverflow: "ellipsis",
    cursor: "pointer",
  },
  messageCountContainer: {
    border: "1px solid #F2F2F2",
    cursor: "pointer",
    borderRadius: "5px",
    paddingInline: "8px",
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  },
  MuiLoadingButtonRoot: {
    backgroundColor: "#FFF",
  },
  campaignCountContainer: {
    // border: `1px solid ${bgColors.green}`,
    cursor: "pointer",
    color: bgColors.black,
    borderRadius: "8px",
    paddingInline: "8px",
    display: "flex",
    flexDirection: "row",
    height: "34px",
    alignItems: "center",
  },
  campaignCountContainer1: {
    border: "1px solid #F2F2F2",
    color: bgColors.green,
    borderRadius: "20px",
    paddingInline: "8px",
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  },
  messageInnerContainer: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  grayColor: {
    color: "#303030",
    // opacity: "60%",
    fontSize: "13px",
    // padding:"5px"
  },
  grayColor1: {
    color: "#303030",
    opacity: "90%",
    fontSize: "13px",
    // padding:"5px"
  },
  campaignColor: {
    color: bgColors.green,
    // color: "#007aff",
    fontSize: "13px",
    fontWeight: "Semi Bold",
    // padding:"5px"
  },
  iconStyles: {
    cursor: "pointer",
    paddingLeft: "5px",
    alignItems: "center",
    // marginTop: "2px",
    color: "#ffffff",
  },
  table: {
    borderCollapse: "separate",
    borderSpacing: "0",
    textAlign: "center",
    borderColor: "lightgray",
    "& th, & td": {
      // borderTop: '1px solid gray',
      borderBottom: "1px solid #f0f0f0",
      padding: "4px",
    },
    "& th:first-child, & td:first-child": {
      borderLeft: "none",
    },
    "& th:last-child, & td:last-child": {
      borderRight: "none",
    },
  },
  teamProfileContainer: {
    display: "flex",
    alignItems: "center",
  },
  editButtonContainer: {
    border: "2px solid #DBDBDB",
    padding: "8px",
    borderRadius: "12px",
    backgroundColor: "#F4F4F4",
    width: "50px",
    // paddingBottom: 0,
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    cursor: "pointer",
  },
});

const TagsTableData = ({ data, newTagDialog, filteredTags }: any) => {
  const classes = useStyles();
  const dispatch = useAppDispatch();
  const getTagsData = useAppSelector((state: any) => state?.getContactTagsData);
  const getContactTag = getTagsData?.data;

  const getTagStatus = getTagsData?.status;
  const userProfileSlice = useAppSelector((state: any) => state?.adminLogin);
  const userData = userProfileSlice?.data;
  const filteredOptions = getContactTag?.filter(
    (option: { tag: string }) => option?.tag?.trim() !== ""
  );

  const getuserPermissionData = useAppSelector(
    (state: any) => state.getUserPermissions?.data
  );
  const manageContactsObject = getuserPermissionData?.contacts;

  const formatDate = (datetime: any) => {
    const date = new Date(datetime);
    const year = date.getFullYear();
    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];
    const month = monthNames[date.getMonth()];
    const day = ("0" + date.getDate()).slice(-2);
    return `${day} ${month} ${year}`;
  };
  const [tableData, setTableData] = useState(filteredOptions);

  const [deleteTag, setDeleteTag] = useState(false);
  const [deleteId, setDeleteId] = useState(null);
  const [isDeleteLoading, setIsDeleteTagLoading] = useState(false);
  const [deleteTagsTooltip, setDeleteTagsTooltip] = useState(null);
  const [editTagPopup, setEditTagPopup] = useState(false);
  const [editTag, setEditTag] = useState(null);
  const [tagId, seTagId] = useState(null);

  const handleOpenDelete = () => {};
  const handleCloseDelete = () => {
    setDeleteTag(false);
  };
  const hasDeleteTagPermission = (permission: any) => {
    for (const profileItem of permission) {
      if (Object.prototype.hasOwnProperty.call(profileItem, "deleteTags")) {
        return true;
      }
    }
  };
  const handleDeleteTag = async (tagId: any) => {
    const hasPermission = hasDeleteTagPermission(manageContactsObject);
    if (hasPermission) {
      setDeleteTag(true);
      const data = {
        businessId: userData?.companyId,
        userId: userData?.userId,
        tagIds: deleteId,
      };
      setIsDeleteTagLoading(true);
      const getDelRes = await CONTACT_TAGS_APIS.deleteTags(data);
      if (getDelRes?.status === 200) {
        handleCloseDelete();
        setIsDeleteTagLoading(false);
        dispatch(
          toastActions.setToaster({
            type: "success",
            message: "Tag Deleted Successfully",
          })
        );
        setIsDeleteTagLoading(false);
        const body = {
          businessId: userData?.companyId,
          userId: userData?.userId,
          search: "",
        };
        // await dispatch(getContactTags(body));
        await dispatch(getContactTags(body));
      }
    } else {
      setDeleteTagsTooltip(tagId);
      setTimeout(() => {
        setDeleteTagsTooltip(null);
      }, 2000);
    }
  };
  // const isDeleteLoading=()=>{

  // }
  const handleCloseEditTagPopover = () => {
    setEditTagPopup(false);
  };
  useEffect(() => {
    // if (data === "Attributes") {
    //   setTableData(attributesData);
    // } else {
    //   setTableData(filteredOptions);
    // }
    setTableData(filteredTags);
  }, [data, filteredTags]);

  const columns = [
    {
      id: "index",
      label: "So.no",
    },
    {
      id: "tagName",
      label: "Tag Name",
    },
    {
      id: "createdBy",
      label: "Created By",
    },
    {
      id: "createdAt",
      label: "Created At",
    },
    {
      id: "updatedAt",
      label: "Updated At",
    },
  ];

  const renderActions = (row: any) => {
    return (
      <Box sx={{ display: "flex", gap: "5px" }}>
        <Tooltip title="Edit" placement="top">
          <Box
            onClick={(e) => {
              e.stopPropagation();
              {
                data === "Tags" && setEditTagPopup(true);
                setEditTag(row?.tag);
                seTagId(row?.id);
              }
            }}
            sx={{ cursor: "pointer" }}
          >
            <EditIconSvg />
          </Box>
        </Tooltip>
        <Tooltip title="Delete" placement="top">
          <Box
            onClick={(e) => {
              e.stopPropagation();

              {
                data === "Tags" && setDeleteTag(true);
                setDeleteId(row?.id);
              }
            }}
            sx={{ cursor: "pointer" }}
          >
            <DeleteIconSvg />
          </Box>
        </Tooltip>
      </Box>
    );
  };

  const transformedData = tableData?.map((row: any, index: number) => ({
    ...row,
    index: index + 1,
    tagName: row?.tag,
    createdBy: row?.createdBy?.trim() === "" ? "N/A" : row?.createdBy,
    createdAt: formatDate(row?.createdAt),
    updatedAt: formatDate(row?.updatedAt),
  }));

  return (
    <>
      <CommonTable
        columns={columns}
        data={transformedData}
        actions={renderActions}
        showPagination={false}
      />

      <DeletePopUp
        title="selected tag"
        open={deleteTag}
        handleClose={handleCloseDelete}
        handleDelete={handleDeleteTag}
        handleLoad={isDeleteLoading}
      />
      <NewTagMember
        open={editTagPopup}
        handleClose={handleCloseEditTagPopover}
        isEdit={editTag}
        tagId={tagId}
      />
    </>
  );
};

export default TagsTableData;
