import React, { useState, forwardRef, useEffect } from "react";
import {
  <PERSON>,
  IconButton,
  <PERSON><PERSON><PERSON>,
  <PERSON>lap<PERSON>,
  useTheme,
  Typo<PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  TextField,
} from "@mui/material";
import { Panel } from "reactflow";
import NodeCard from "./Nodecard";
import SidebarSection from "./SidebarSection";
import {
  ChevronLeft,
  ChevronRight,
  Edit as EditIcon,
  Check as CheckIcon,
  Close as CloseIcon,
} from "@mui/icons-material";
import { is } from "immutable";
import { bgColors } from "../../../utils/bgColors";

interface FlowSidebarProps {
  isCollapsed: boolean;
  onToggle: () => void;
  messages: any[];
  actions: any[];
  onDragStart: (event: React.DragEvent, label: string) => void;
  draggable: boolean;
  onClick?: (label: string) => void;
  onNodeClick?: (item: any) => void;
  onSave: any;
  onCancel: any;
  tempWorkflowName: string;
  setTempWorkflowName: any;
  workflowData: any;
  isWorkflowActive: boolean;
  isSavingWorkflow: boolean;
  nodes: any[];
}

const FlowSidebar = forwardRef<HTMLDivElement, FlowSidebarProps>(
  (
    {
      isCollapsed,
      onToggle,
      messages,
      actions,
      onDragStart,
      draggable,
      onClick,
      onNodeClick,
      onSave,
      onCancel,
      tempWorkflowName,
      isWorkflowActive,
      isSavingWorkflow,
      nodes,
    },
    ref
  ) => {
    const theme = useTheme();

    const handleNodeClick = (item: any) => {
      if (onNodeClick) {
        onNodeClick(item);
      } else if (onClick) {
        onClick(item.label);
      }
    };

    if (isCollapsed) {
      return (
        <Panel position="top-left" style={{ margin: 0, padding: 0 }}>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              padding: "6px",
              backgroundColor: "white",
              borderRadius: "4px",
              boxShadow: theme.shadows[1],
            }}
          >
            <IconButton
              onClick={onToggle}
              size="small"
              sx={{
                "&:hover": {
                  backgroundColor: "white",
                },
              }}
            >
              <ChevronRight fontSize="small" />
            </IconButton>
          </Box>
        </Panel>
      );
    }

    return (
      <Panel
        position="top-left"
        style={{
          margin: 0,
          padding: 0,
          maxWidth: "330px",
          minWidth: "180px",
        }}
      >
        <Box
          ref={ref}
          sx={{
            width: { xs: "180px", sm: "220px", md: "360px" },
            height: "100vh",
            background: "linear-gradient(to bottom, #f9fafb, #ffffff)",
            borderRight: "1px solid #e0e0e0",
            display: "flex",
            flexDirection: "column",
            position: "relative",
            transition: theme.transitions.create("width", {
              easing: theme.transitions.easing.sharp,
              duration: theme.transitions.duration.enteringScreen,
            }),
            overflowY: "auto",
            "&::-webkit-scrollbar": {
              width: "4px",
            },
            "&::-webkit-scrollbar-track": {
              background: "#f1f1f1",
            },
            "&::-webkit-scrollbar-thumb": {
              background: "#888",
              borderRadius: "2px",
            },
          }}
        >
          <Box
            sx={{
              padding: { xs: "12px", sm: "8px", md: "12px" },
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              borderBottom: "1px solid #e0e0e0",
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
              <Typography
                variant="subtitle1"
                sx={{
                  fontWeight: 600,
                  fontSize: { xs: "11px", sm: "12px", md: "14px" },
                }}
              >
                {tempWorkflowName || "defalut workflow"}
              </Typography>
            </Box>
            <IconButton onClick={onToggle} size="small">
              <ChevronLeft fontSize="small" />
            </IconButton>
          </Box>

          <Box sx={{ p: { xs: 2, sm: 1, md: 2 }, flex: 1, overflowY: "auto" }}>
            <SidebarSection title="Messages" isCollapsed={false}>
              {messages?.map((message: any) => (
                <NodeCard
                  key={message?.id}
                  icon={message?.icon}
                  label={message?.label}
                  description={message?.description}
                  isCollapsed={false}
                  onDragStart={(event: any) =>
                    onDragStart(event, message?.label)
                  }
                  draggable={draggable}
                  onClick={() => handleNodeClick(message)}
                  sx={{
                    cursor: "pointer",
                  }}
                />
              ))}
            </SidebarSection>

            <SidebarSection title="Actions" isCollapsed={false}>
              {actions?.map((action: any) => (
                <NodeCard
                  key={action?.id}
                  icon={action?.icon}
                  label={action?.label}
                  description={action?.description}
                  isCollapsed={false}
                  onDragStart={(event: any) =>
                    onDragStart(event, action?.label)
                  }
                  draggable={draggable}
                  onClick={() => handleNodeClick(action)}
                  sx={{
                    cursor: "pointer",
                  }}
                  disabled={action.isDisabled ? action.isDisabled(nodes) : false}
                  disabledReason={action.disabledReason}
                />
              ))}
            </SidebarSection>
          </Box>

          <Box
            sx={{
              padding: { xs: "16px", sm: "12px", md: "16px" },
              borderTop: "1px solid #e0e0e0",
              backgroundColor: "white",
              position: "sticky",
              bottom: 0,
            }}
          >
            <Stack direction="row" spacing={1} justifyContent="flex-end">
              <Button
                variant="outlined"
                onClick={onCancel}
                size="small"
                sx={{
                  minWidth: "70px",
                  fontSize: { xs: "12px", sm: "11px", md: "12px" },
                  borderColor: bgColors.red,
                  color: bgColors.red,
                }}
              >
                Cancel
              </Button>
              {isSavingWorkflow ? (
                <Button
                  variant="contained"
                  size="small"
                  sx={{
                    minWidth: "100px",
                    fontSize: { xs: "12px", sm: "11px", md: "12px" },
                    backgroundColor: bgColors.green,
                  }}
                >
                  Saving...
                </Button>
              ) : (
                <Button
                  variant="contained"
                  onClick={onSave}
                  size="small"
                  sx={{
                    backgroundColor: bgColors.green,
                    minWidth: "100px",
                    fontSize: { xs: "12px", sm: "11px", md: "12px" },
                    ...(isWorkflowActive
                      ? {}
                      : {
                          backgroundColor: "#ff9800",
                          "&:hover": {
                            backgroundColor: "#f57c00",
                          },
                        }),
                  }}
                >
                  {isWorkflowActive ? "Save" : "Save to Draft"}
                </Button>
              )}
            </Stack>
          </Box>
        </Box>
      </Panel>
    );
  }
);

export default FlowSidebar;
