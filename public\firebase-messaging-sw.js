/* global importScripts */
/* global firebase */
/* global clients */

importScripts("https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js");
importScripts(
  "https://www.gstatic.com/firebasejs/8.10.1/firebase-messaging.js"
);

// Initialize Firebase
firebase.initializeApp({
  apiKey: "AIzaSyDc0_07hmfzPnov43At6ZhH85Q8jsfsqgg",
  authDomain: "ahex-demo.firebaseapp.com",
  projectId: "ahex-demo",
  storageBucket: "ahex-demo.appspot.com",
  messagingSenderId: "1002605836270",
  appId: "1:1002605836270:web:3a45dd32493173d973c0ff",
  measurementId: "G-VM413N1R92",
});

// Retrieve an instance of Firebase Messaging
var messaging = firebase.messaging();

self.addEventListener("install", function () {
  self.skipWaiting();
});

self.addEventListener("activate", function (event) {
  event.waitUntil(clients.claim());
});

// Handle background messages from FCM
messaging.onBackgroundMessage(function (payload) {
  // Customize notification here

  var notificationTitle = payload.notification.title || "New Message";
  var notificationOptions = {
    body: payload.notification.body || "You have a new message.",
    icon: payload.notification.icon || "/default-icon.png",
  };

  self.registration.showNotification(notificationTitle, notificationOptions);
});

// Handle messages from the main thread (SignalR)
self.addEventListener("message", function (event) {
  if (event.data && event.data.type === "NEW_MESSAGE") {
    var message = event.data.message;
    var notificationTitle = message.title || "New Message";
    var notificationOptions = {
      body: message.body || "You have a new message.",
      icon: message.icon || "/default-icon.png",
      data: message.data,
    };

    self.registration.showNotification(notificationTitle, notificationOptions);
  }
});

self.addEventListener("notificationclick", function (event) {
  

  // event.notification.close();

  var notificationData = event.notification.data;
  var urlToOpen =
    notificationData && notificationData.url ? notificationData.url : "/";

  event.waitUntil(
    clients.matchAll({ type: "window" }).then(function (clientList) {
      for (var i = 0; i < clientList.length; i++) {
        var client = clientList[i];

        if (client.url.includes("app.engageto")) {
          client.focus();

          client.postMessage({
            type: "navigate-to-chat",
            urlToOpen: urlToOpen,
            contactNumber: notificationData.contactNumber,
          });
          return;
          //  return client.focus()
        }
      }
      // if (clients.openWindow) {
      //   return clients.openWindow(urlToOpen);
      // }
    })
  );
});
// Handle notification click event (optional)
