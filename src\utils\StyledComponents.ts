import { Box, styled, IconButton } from "@mui/material";

// Facebook-style carousel components
export const CarouselContainer = styled(Box)({
  position: "relative",
  width: "100%", // Use full width of parent container
  maxWidth: "380px", // Reduced max width to match new maxWidthStyle
  margin: "0 auto 0", // Reduced top margin to decrease space between body and carousel
  padding: "0 0 25px", // Reduced bottom padding for indicators
  display: "flex", // Ensure proper centering
  flexDirection: "column", // Stack children vertically
  alignItems: "center", // Center horizontally
});

export const CarouselContent = styled(Box)({
  overflow: "hidden",
  borderRadius: "8px", // Facebook uses slightly less rounded corners
  position: "relative",
  width: "100%", // Ensure full width
});

export const CarouselTrack = styled(Box)(() => ({
  display: "flex",
  transition: "transform 300ms ease-in-out",
  margin: "0",
  justifyContent: "flex-start", // Align items to the left
  paddingLeft: "8px", // Add padding to show partial visibility of next card
  paddingRight: "8px", // Add padding to show partial visibility of next card
  willChange: "transform", // Optimize for animations
  width: "100%", // Ensure full width
}));

export const CarouselItem = styled(Box)({
  width: "240px", // Reduced width for each card
  height: "auto", // Allow height to adjust based on content
  flexShrink: 0,
  padding: "0 4px", // Reduced padding to show more of adjacent cards
  boxSizing: "border-box",
  transition: "all 300ms ease", // Smooth transition for all effects
  marginRight: "0", // Ensure consistent spacing
  position: "relative", // For z-index to work
  display: "flex", // Enable flexbox
});

export const NavigationButton = styled(IconButton)(() => ({
  position: "absolute",
  top: "50%",
  transform: "translateY(-50%)",
  backgroundColor: "rgba(255, 255, 255, 0.95)",
  color: "#1877F2", // Facebook blue color
  "&:hover": {
    backgroundColor: "rgba(255, 255, 255, 1)",
    transform: "translateY(-50%) scale(1.1)",
  },
  transition: "all 200ms",
  boxShadow: "0 2px 8px rgba(0,0,0,0.3)",
  padding: "6px",
  zIndex: 10, // Increased z-index to ensure visibility
  width: "28px",
  height: "28px",
  minWidth: "28px", // Ensure consistent size
  fontSize: "0.9rem", // Smaller icon size
}));

// New component for the navigation dots
export const CarouselIndicators = styled(Box)({
  display: "flex",
  justifyContent: "center",
  position: "absolute",
  bottom: 0, // Moved up slightly
  left: 0,
  right: 0,
  zIndex: 1,
});

export const CarouselIndicator = styled(Box)<{ active?: boolean }>(
  ({ active }) => ({
    width: "6px", // Smaller dots
    height: "6px", // Smaller dots
    borderRadius: "50%",
    backgroundColor: active ? "#4CAF50" : "#BDC7D8", // Facebook blue for active, gray for inactive
    margin: "0 3px", // Reduced margin between dots
    transition: "background-color 300ms ease",
    cursor: "pointer",
    
  })
);
