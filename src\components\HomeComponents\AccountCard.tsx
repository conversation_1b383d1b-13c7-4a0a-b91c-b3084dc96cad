import React from "react";
import { Paper, Typography, Box, Stack, Tooltip } from "@mui/material";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import PhoneIcon from "@mui/icons-material/Phone";
import AccountBalanceWalletIcon from "@mui/icons-material/AccountBalanceWallet";
import StarIcon from "@mui/icons-material/Star";
import ChatBubbleOutlineIcon from "@mui/icons-material/ChatBubbleOutline";
import { Account } from "./AccountsConatiner"; // Make sure path is correct
import { QualityScore } from "../../utils/enums";

interface MUIContactCardProps {
  account: Account;
  selected: Account | null;
  onSelect: (account: Account) => void;
}

const AccountCard: React.FC<MUIContactCardProps> = ({
  account,
  selected,
  onSelect,
}) => {
  const isSelected = selected?.businessId === account?.businessId;

  return (
    <Paper
      elevation={isSelected ? 3 : 1}
      onClick={() => onSelect(account)}
      sx={{
        position: "relative",
        width: "100%",
        maxWidth: 600,
        borderRadius: 2,
        overflow: "hidden",
        bgcolor: isSelected ? "action.selected" : "background.paper",
        border: isSelected ? "2px solid #00934F" : "1px solid",
        borderColor: isSelected ? "#00934F" : "grey.200",
        transition: "all 0.2s ease",
        cursor: "pointer",
        "&:hover": {
          boxShadow: 3,
        },
      }}
    >
      {/* Check Icon */}
      {isSelected && (
        <Box
          sx={{
            position: "absolute",
            top: 16,
            right: 16,
            color: "#00934F",
            zIndex: 10,
          }}
        >
          <CheckCircleIcon fontSize="small" />
        </Box>
      )}

      {/* Main content */}
      <Stack
        direction={{ xs: "column", md: "row" }}
        sx={{ position: "relative", height: "100%", zIndex: 10 }}
      >
        {/* Info Section */}
        <Box
          sx={{
            width: { xs: "100%", md: "66.66%" },
            p: { xs: 3, md: 4 },
            display: "flex",
            flexDirection: "column",
            gap: 1.5,
          }}
        >
          <Tooltip title={account?.businessName}>
            <Typography variant="body1" fontWeight="bold" noWrap>
              {account?.businessName
                ? account.businessName.charAt(0).toUpperCase() +
                  account.businessName.slice(1)
                : "--"}
            </Typography>
          </Tooltip>

          <Box sx={{ display: "flex", gap: 1, alignItems: "center" }}>
            <PhoneIcon fontSize="small" sx={{ color: "#6b7280" }} />
            <Typography variant="body2">
              {account?.phoneNumber || "--"}
            </Typography>
          </Box>

          <Box sx={{ display: "flex", gap: 1, alignItems: "center" }}>
            <AccountBalanceWalletIcon
              fontSize="small"
              sx={{ color: "#6b7280" }}
            />
            <Typography
              variant="h5"
              fontWeight="bold"
              sx={{ color: Number(account.balance) < 0 ? "red" : "#00934F" }}
            >
              ₹
              {account?.balance?.toLocaleString(undefined, {
                minimumFractionDigits: 2,
              })}
            </Typography>
          </Box>

          <Box sx={{ display: "flex", alignItems: "center" }}>
            <Tooltip
              title={
                <Box
                  sx={{
                    textAlign: "left",
                    maxWidth: "500px",
                    padding: "12px",
                    "@media (min-width: 600px)": {
                      maxWidth: "700px",
                    },
                  }}
                >
                  <Typography
                    variant="subtitle2"
                    sx={{
                      fontWeight: 600,
                      mb: 1,
                      fontSize: { xs: "14px", sm: "16px" },
                    }}
                  >
                    Message Quality:
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{ mb: 1, fontSize: { xs: "12px", sm: "14px" } }}
                  >
                    - Green: High quality
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{ mb: 1, fontSize: { xs: "12px", sm: "14px" } }}
                  >
                    - Yellow: Medium quality
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{ mb: 1, fontSize: { xs: "12px", sm: "14px" } }}
                  >
                    - Red: Low quality
                  </Typography>
                </Box>
              }
              placement="bottom"
            >
              <Box
                sx={{
                  display: "flex",
                  justifyContent: { xs: "center", sm: "space-between" },
                  alignItems: "center",
                  gap: 1,
                }}
              >
                <Box>
                  <StarIcon color="action" sx={{ color: "#6b7280" }} />
                </Box>
                <Box>
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "center",
                      flexDirection: "column",
                      alignItems: "center",
                    }}
                  >
                    <Typography
                      sx={{
                        fontSize: { xs: 10, md: 12, lg: 14 },
                        color: `${
                          account?.qualityRating === 1
                            ? "#00934F"
                            : account?.qualityRating === 2
                            ? "red"
                            : account?.qualityRating === 3
                            ? "#FF9D0D"
                            : ""
                        }`,
                      }}
                      fontWeight={"bold"}
                    >
                      {account?.qualityRating === QualityScore.GREEN
                        ? "GREEN"
                        : account?.qualityRating === QualityScore.RED
                        ? "RED"
                        : account?.qualityRating === QualityScore.YELLOW
                        ? "YELLOW"
                        : "UNKNOWN"}
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Tooltip>
          </Box>

          <Box sx={{ display: "flex", gap: 1, alignItems: "center" }}>
            <ChatBubbleOutlineIcon fontSize="small" sx={{ color: "#6b7280" }} />
            <Typography variant="body2">
              {account.conversationCount + "/" + account?.messageLimit}
            </Typography>
          </Box>
        </Box>

        {/* Logo Section */}
        {account?.companyLogoLink && (
          <Box
            sx={{
              width: { xs: "50%", md: "33.33%" },
              p: { xs: 3, md: 4 },
              display: "flex",
              alignItems: "end",
              justifyContent: "center",
            }}
          >
            {/* Placeholder for your company logo */}
            <img
              src={account?.companyLogoLink.trim()}
              alt="Company Logo"
              style={{ maxWidth: "100%", maxHeight: "100%" }}
            />
          </Box>
        )}
      </Stack>
    </Paper>
  );
};

export default AccountCard;
