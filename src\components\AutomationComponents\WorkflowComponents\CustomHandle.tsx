import { Handle, HandleProps } from "reactflow";
import { bgColors } from "../../../utils/bgColors";

interface CustomHandleProps extends HandleProps {
  style?: React.CSSProperties;
  onConnectStart?: () => void;
  onConnectEnd?: () => void;
}

const CustomHandle = (props: CustomHandleProps) => {
  const { onConnectStart, onConnectEnd, ...restProps } = props;

  return (
    <Handle
      // style={{
      //   width: 12,
      //   height: 12,
      //   background: "green",
      //   border: "none",
      //   borderRadius: "50%",
      //   boxShadow: "0px 0px 5px rgba(0, 128, 0, 0.8)",
      // }}
      style={{
        background: bgColors.green,
        width: 10,
        height: 10,
        borderRadius: "50%",
        top: "50%",
        ...props.style,
      }}
      // onConnectStart={onConnectStart}
      // onConnectEnd={onConnectEnd}
      {...restProps}
    />
  );
};

export default CustomHandle;
