import {
  Avatar,
  Box,
  Grid,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { bgColors } from "../../../utils/bgColors";
import { makeStyles } from "@mui/styles";
import EditDeletePopover from "../../../components/ManageClientsComponents/EditAndDeletePopover";
import EditIconSvg from "../../../assets/svgs/EditIconSvg";
import DeleteIconSvg from "../../../assets/svgs/DeleteIconSvg";
import AddNewClientMember from "../../../components/ManageClientsComponents/AddNewClientMember";
import { MANAGE_CLIENTS } from "../../../Apis/AdminLogin/ManageClients/ManageClient";
import { useAppDispatch, useAppSelector } from "../../../utils/redux-hooks";
import { toastActions } from "../../../utils/toastSlice";
import { fetchGetAllTeamMembersByCompanyId } from "../../../redux/slices/ManageCompany/GetAllTeamMembersByCompanyId";
import EditTeamMember from "../../../components/ManageCompanyComponents/EditTeamMember";
import { ADMIN_MANAGE_COMPANY_APIS } from "../../../Apis/ManageCompany/ManageCompany";
import LoadingComponent from "../../../components/common/LoadingComponent";
import SearchIconSvg2 from "../../../assets/svgs/SearchIconSvg2";
import DeletePopUp from "../../../components/common/DeletePopup";
import useDebouncedFetch from "../../../utils/debounceHook";
import GroupsIcon from "@mui/icons-material/Groups";
import PeopleIcon from "@mui/icons-material/People";
import { hasManageClientsPermission } from "../../../utils/permissions";
import NoAccessPage from "../../../components/common/NoAccess";
import CommonHeader from "../../../components/common/CommonHeader";
import CommonTable from "../../../components/common/CommonTable";
import AddNewTeamMember from "../../../components/ManageCompanyComponents/AddNewTeamMember";

const useStyles = makeStyles({
  mainContainer: {
    // backgroundColor: bgColors.white1,
    height: "100vh",
    width: "100%",
  },

  bgContainer: {
    backgroundColor: bgColors.white,
    height: "94%",
    width: "100%",
    overflow: "auto",
  },
  headerContainer: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    borderBottom: "1px solid #f0f0f0",
    width: "100%",
    paddingLeft: "24px",
    paddingRight: "24px",
    paddingTop: "8px",
    paddingBottom: "8px",
  },
  mainBorderStyles: {
    borderRadius: "18px",
    border: `2px solid ${bgColors.gray5}`,
  },
  profileContainer: {
    display: "flex",
    flexDirection: "row",
  },
  blackColor: {
    // color: `${bgColors.black1} !important`,
    // fontWeight: "600 !important",
  },
  grayColor: {
    color: `${bgColors.black1} !important`,
    opacity: "60% !important",
    // fontWeight: "600 !important",
  },
  emailContainer: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  planButton: {
    backgroundColor: bgColors.green1,
    color: bgColors.green,
    fontWeight: "700",
    paddingTop: 6,
    paddingRight: 22,
    paddingBottom: 6,
    paddingLeft: 22,
    borderRadius: 25,
  },
  basicPlanButton: {
    backgroundColor: bgColors.blue3,
    color: bgColors.blue2,
    fontWeight: "700",
    paddingTop: 6,
    paddingRight: 22,
    paddingBottom: 6,
    paddingLeft: 22,
    borderRadius: 25,
  },
  proPlanButton: {
    backgroundColor: bgColors.yellow2,
    color: bgColors.yellow,
    fontWeight: "700",
    paddingTop: 6,
    paddingRight: 22,
    paddingBottom: 6,
    paddingLeft: 22,
    borderRadius: 25,
  },
  enterPricePlanButton: {
    backgroundColor: bgColors.red2,
    color: bgColors.red1,
    fontWeight: "700",
    paddingTop: 6,
    paddingRight: 22,
    paddingBottom: 6,
    paddingLeft: 22,
    borderRadius: 25,
  },
  editButtonContainer: {
    marginLeft: "auto",
    cursor: "pointer",
    display: "flex",
    flexDirection: "row",
  },
  profileTextContainer: {
    display: "flex",
    flexDirection: "row",
    width: "100%",
  },
  manageTeamContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    gap: "20px",
    width: "full",
  },
  SaveChangesButton: {
    border: `1px solid ${bgColors.green}`,
    color: bgColors.green,
    borderRadius: "8px",
    width: "120px",
    height: "32px",
    // padding: "10px",
    cursor: "pointer",
  },
  searchField: {
    width: "100%",
    borderRadius: "12px",
    // backgroundColor: bgColors.white2,
    backgroundColor: "white",
    "& input::placeholder": {
      textAlign: "left",
      fontSize: "14px",
      fontFamily: "inter",
      color: "#000000 !important",
    },
  },
  table: {
    borderCollapse: "separate",
    borderSpacing: "0 8px",
    borderColor: "lightgray",
    "& th, & td": {
      // borderTop: '1px solid gray',
      borderBottom: "1px solid #f0f0f0",
      padding: "3px",
    },
    "& th:first-child, & td:first-child": {
      paddingLeft: "20px",
    },
    "& th:last-child, & td:last-child": {
      paddingRight: "20px",
    },
  },
  teamProfileContainer: {
    display: "flex",
    alignItems: "center",
  },
});

export const capitalizeWords = (str: any) => {
  // Replace underscores with spaces, then capitalize each word
  return str
    .replace(/_/g, " ")
    .replace(/\b\w/g, (char: any) => char.toUpperCase());
};

const ManageClientDetails = () => {
  const classes = useStyles();
  const history = useNavigate();
  const dispatch = useAppDispatch();
  const location = useLocation();
  const { clientDetails } = location.state || {};

  // const loginData = useAppSelector((state) => state?.adminLogin?.data);
  // const getPermissionData = loginData?.permissions;
  const getuserPermissionData = useAppSelector(
    (state: any) => state.getUserPermissions?.data
  );
  const hasClientPermission = hasManageClientsPermission(
    getuserPermissionData?.profile
  );
  const manageClientObject = getuserPermissionData?.profile?.find((item: any) =>
    Object?.prototype?.hasOwnProperty?.call(item, "manageClients")
  );
  // const clientsByIdSlice = useAppSelector((state) => state.companyData);
  // const clientsById = clientsByIdSlice?.data && clientsByIdSlice?.data;
  // const permissions = useAppSelector((state) => state.managePermissions.data);
  const manageClientActions = manageClientObject
    ? manageClientObject?.manageClients
    : [];
  const getAllTeamMembers = useAppSelector(
    (state: any) => state?.getAllTeamMembersByCompanyId?.data
  );
  
  const debouncedFetchClientMembers = useDebouncedFetch(
    fetchGetAllTeamMembersByCompanyId,
    1500
  );

  const [isDeleteLoading, setIsDeleteLoading] = useState(false);
  const [isDeleteTeamMemberLoading, setIsDeleteTeamMemberLoading] =
    useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialog, setDialog] = useState(false);
  const [dialogDEL, setDialogDEL] = useState(false);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [clientsById, setClientsById] = useState<any>(null);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [editTeamMember, setEditTeamMember] = useState("");
  const [deleteId, setDeleteId] = useState("");
  const [editClientTooltip, setEditClientTooltip] = useState(false);
  const [deleteClientTooltip, setDeleteClientTooltip] = useState(false);
  const [addMemberTooltip, setAddMemberTooltip] = useState(false);
  const [editMemberTooltip, setEditMemberTooltip] = useState(null);
  const [deleteMemberTooltip, setDeleteMemberTooltip] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // const earliestCreationDate = getAllTeamMembers?.reduce(
  //   (earliest: any, member: any) => {
  //     return new Date(member.creationDate) < new Date(earliest)
  //       ? member.creationDate
  //       : earliest;
  //   },
  //   getAllTeamMembers[0]?.creationDate
  // );

  const handleChangePage = (event: any, newPage: any) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: any) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const hasAcess = (permission: any) => {
    if (manageClientActions?.includes(permission)) {
      return true;
    } else {
      return false;
    }
  };

  const handleDeleteDialog = () => {
    setDeleteDialog(true);
  };

  // useEffect(() => {
  //   const getT =  getAllTeamMembers?.users?.map((each) => each?.)
  // })

  const handleDeleteCloseDialog = () => {
    setDeleteDialog(false);
  };

  const handleDeleteDailog = () => {
    const hasPermission = hasAcess("deleteClient");
    if (hasPermission) {
      setDeleteDialog(true);
    } else {
      setDeleteClientTooltip(true);
      setTimeout(() => {
        setDeleteClientTooltip(false);
      }, 2000);
    }
  };

  const handleOpenDialog = () => {
    const hasPermission = hasAcess("addMember");
    if (hasPermission) {
      setOpenDialog(true);
    } else {
      setAddMemberTooltip(true);
      setTimeout(() => {
        setAddMemberTooltip(false);
      }, 2000);
    }
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  useEffect(() => {
    const postData = {
      search: searchQuery,
      companyId: location?.state?.clientDetails?.id,
    };
    if (searchQuery) {
      debouncedFetchClientMembers(postData);
    } else {
      dispatch(fetchGetAllTeamMembersByCompanyId(postData));
    }
  }, [dispatch, searchQuery, debouncedFetchClientMembers]);

  const handlePopoverClose = () => {
    setAnchorEl(null);
  };

  const handleEdit = () => {
    // setClientDetail(c);
    const hasPermission = hasAcess("editClient");

    if (hasPermission) {
      history(`/profile/manage-clients/edit/${clientsById?.id}`, {
        state: { clientDetails: clientsById },
      });
    } else {
      setEditClientTooltip(true);
      setTimeout(() => {
        setEditClientTooltip(false);
      }, 2000);
    }
  };
  // const loginData = useAppSelector((state) => state?.adminLogin?.data);
  const handleDeleteTeamMember = async () => {
    setIsDeleteTeamMemberLoading(true);
    if (deleteId) {
      try {
        const response = await ADMIN_MANAGE_COMPANY_APIS.deleteTeamMember(
          deleteId
        );
        if (response.status === 200) {
          dispatch(
            toastActions.setToaster({
              type: "success",
              message: response?.data?.message,
            })
          );
          const postData = {
            search: searchQuery,
            companyId: location?.state?.clientDetails?.id,
          };
          dispatch(fetchGetAllTeamMembersByCompanyId(postData));
          handleCloseDEL();
          // window.location.reload();
        }
      } catch (err: any) {
        dispatch(
          toastActions.setToaster({
            type: "error",
            message: err?.response?.data?.message,
          })
        );
        // window.location.reload();
      }
    }
    setIsDeleteTeamMemberLoading(false);
  };

  const handleDelete = () => {
    handleDeleteDialog();
  };

  const handleDialog = (row: any) => {
    const hasPermission = hasAcess("editMember");
    if (hasPermission) {
      setDialog(true);
      if (row !== undefined && row !== null) {
        setEditTeamMember(row);
      }
    } else {
      setEditMemberTooltip(row);
      setTimeout(() => {
        setEditMemberTooltip(null);
      }, 2000);
    }
  };
  const handleDialogDEL = (row: any) => {
    const hasPermission = hasAcess("deleteMember");
    if (hasPermission) {
      if (row !== undefined && row !== null) {
        setDeleteId(row?.id);
      }
      setDialogDEL(true);
    } else {
      setDeleteMemberTooltip(row);
      setTimeout(() => {
        setDeleteMemberTooltip(null);
      }, 2000);
    }
  };

  const handleClose = () => {
    setDialog(false);
  };
  const handleCloseDEL = () => {
    setDialogDEL(false);
  };

  const getClientsById = async () => {
    const data = {
      clientId: location?.state?.clientDetails?.id,
    };
    try {
      // dispatch(fetchCompanyDetails(clientId));
      const getRes = await MANAGE_CLIENTS.getClientsById(data);
      setClientsById(getRes?.data);
    } catch (error) {}
  };

  useEffect(() => {
    getClientsById();
  }, []);

  const handleDeleteClient = async () => {
    setIsDeleteLoading(true);
    try {
      const data = {
        clientId: location?.state?.clientDetails?.id,
      };
      const deleteRes = await MANAGE_CLIENTS.deleteClient(data);

      if (deleteRes?.status === 200) {
        history("/profile/manage-clients");
        dispatch(
          toastActions.setToaster({
            type: "success",
            message: "Client Deleted Successfully",
          })
        );
      }
    } catch (error) {}
    setIsDeleteLoading(false);
  };

  const formatDate = (dateString: any) => {
    const date = new Date(dateString);
    return date?.toISOString().split("T")[0];
  };
  const formatDate2 = (datetime: any) => {
    const date = new Date(datetime);
    const year = date.getFullYear();
    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];
    const month = monthNames[date.getMonth()];
    const day = ("0" + date.getDate()).slice(-2);
    return `${day} ${month} ${year}`;
  };

  // React.useEffect(() => {
  //   // dispatch(fetchManagePermissions({ roleId: roleId, companyId }));
  // }, [dispatch, loginData]);

  const [hoveredRow, setHoveredRow] = useState(null);

  const handleRowHover = (rowId: any) => {
    setHoveredRow(rowId);
  };
  const splitCamelCaseAndJoin = (str: string): string => {
    // Replace special characters with a space
    const cleanedStr = str.replace(/[^a-zA-Z0-9]+/g, " ");
    // Split the string at capital letters and join with a space
    const splitStr = cleanedStr.replace(/([a-z])([A-Z])/g, "$1 $2");
    // Capitalize the first letter of each word
    const result = splitStr.replace(/\b\w/g, (char: string) =>
      char.toUpperCase()
    );
    return result;
  };

  const columns = [
    {
      id: "member",
      label: "Member",
      format: (row: any) => {
        return (
          <Box className={classes.teamProfileContainer}>
            <Avatar
              alt={row?.member}
              src={row?.image ? row?.image : "/images/profile.png"}
              style={{
                width: "30px",
                height: "30px",
              }}
            />
            <Typography
              className={classes.blackColor}
              style={{
                marginLeft: "4px",
                fontSize: "12px",
              }}
            >
              {row?.name}
            </Typography>
          </Box>
        );
      },
    },
    {
      id: "createdBy",
      label: "Created By",
    },
    {
      id: "email",
      label: "Email",
    },
    {
      id: "contact",
      label: "Contact",
    },
    {
      id: "createdOn",
      label: "Created On",
    },
    {
      id: "role",
      label: "Role",
    },
  ];

  const renderActions = (row: any) => {
    return (
      <Box>
        <Tooltip
          title="Access Denied"
          open={editMemberTooltip === row}
          onClose={() => setEditMemberTooltip(null)}
          placement="top"
        >
          <Box mr={1} onClick={() => handleDialog(row)}>
            <EditIconSvg />
          </Box>
        </Tooltip>
        <Tooltip
          title="Access Denied"
          open={deleteMemberTooltip === row}
          onClose={() => setDeleteMemberTooltip(null)}
          placement="top"
        >
          <Box onClick={() => handleDialogDEL(row)}>
            <DeleteIconSvg />
          </Box>
        </Tooltip>
      </Box>
    );
  };

  const transformData = getAllTeamMembers
    ?.filter((each: any) => each?.status == true)
    ?.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
    .map((each: any) => {
      return {
        member: {
          image: each?.image,
          member: each?.member,
          name: each?.name,
        },
        createdBy: each?.createdBy,
        email: each?.emailAddress,
        contact:
          each?.countryCode && each?.phoneNumber
            ? each?.countryCode + " " + each?.phoneNumber
            : "",
        createdOn: each?.creationDate,
        role: each?.roleId,
      };
    });

  return (
    <>
      {hasClientPermission ? (
        <Grid className={classes.mainContainer}>
          <Grid className={classes.bgContainer}>
            <Box className={classes.headerContainer}>
              <CommonHeader title="View Client Details" />
            </Box>
            {!clientsById ? (
              // clientsByIdSlice?.status === "loading"
              <Box sx={{ height: "70vh" }}>
                <LoadingComponent height="100%" color={bgColors.blue} />
              </Box>
            ) : (
              <>
                <Box m={3}>
                  <Box p={3} className={classes.mainBorderStyles}>
                    <Box className={classes.profileContainer}>
                      <Box>
                        <img
                          alt="profile"
                          style={{
                            width: "70px",
                            height: "70px",
                            borderRadius: "14px",
                          }}
                          src={
                            clientsById?.companyLogoLink ||
                            "/images/companyLogo.png"
                          }
                        />
                      </Box>
                      <Box className={classes.profileTextContainer}>
                        <Box ml={3} mt={1}>
                          <Typography
                            variant="body2"
                            className={classes.blackColor}
                          >
                            {clientsById?.businessName}
                          </Typography>
                          {/* <Typography
                        variant="body2"
                        mt={2}
                        className={classes.grayColor}
                      >
                        {clientsById?.businessCategory || "Agency"}
                      </Typography> */}
                          <Typography
                            variant="body2"
                            mt={2}
                            className={classes.grayColor}
                          >
                            {clientsById?.businessCategory
                              ? splitCamelCaseAndJoin(
                                  clientsById?.businessCategory
                                )
                              : ""}
                          </Typography>
                        </Box>
                        <Box>
                          <Box ml={3}>
                            <button
                              className={
                                clientDetails.currentPlan === "INTRO PLAN"
                                  ? classes.planButton
                                  : clientDetails.currentPlan === "BASIC PLAN"
                                  ? classes.basicPlanButton
                                  : clientDetails.currentPlan === "PRO PLAN"
                                  ? classes.proPlanButton
                                  : classes.enterPricePlanButton
                              }
                            >
                              {/* INTRO PLAN */}
                              {clientDetails.currentPlan
                                ? clientDetails.currentPlan.toUpperCase()
                                : "INTRO PLAN"}
                            </button>
                          </Box>
                        </Box>
                      </Box>
                      <Box
                        className={classes.editButtonContainer}
                        // onClick={handlePopoverOpen}
                      >
                        {/* <ThreeDots /> */}

                        <Tooltip
                          title="Access Denied"
                          open={editClientTooltip}
                          placement="top"
                          onClose={() => setEditClientTooltip(false)}
                        >
                          <Box mr={1} onClick={handleEdit}>
                            <EditIconSvg />
                          </Box>
                        </Tooltip>
                        <Tooltip
                          title="Access Denied"
                          open={deleteClientTooltip}
                          placement="top"
                          onClose={() => setDeleteClientTooltip(false)}
                        >
                          <Box onClick={handleDeleteDailog}>
                            <DeleteIconSvg />
                          </Box>
                        </Tooltip>
                      </Box>
                      <EditDeletePopover
                        open={Boolean(anchorEl)}
                        anchorEl={anchorEl}
                        onClose={handlePopoverClose}
                        onEdit={handleEdit}
                        onDelete={handleDelete}
                      />
                    </Box>
                    <DeletePopUp
                      title="Client"
                      open={deleteDialog}
                      handleClose={handleDeleteCloseDialog}
                      handleDelete={handleDeleteClient}
                      handleLoad={isDeleteLoading}
                    />

                    <Box
                      mt={{ xs: 2, md: 3 }}
                      // mr={3}
                      sx={{
                        justifyContent: "space-between",
                        display: { xs: "block", md: "flex" },
                      }}
                    >
                      <Box
                        mb={{ xs: 2, md: 0 }}
                        // mx={{ xs: 2, md: 4 }}
                        sx={{
                          width: "40%",
                        }}
                      >
                        <Typography
                          variant="body2"
                          mb={1}
                          className={classes.grayColor}
                        >
                          Company Email
                        </Typography>
                        <Typography
                          variant="body2"
                          className={classes.blackColor}
                        >
                          {clientsById?.businessEmail}
                        </Typography>
                      </Box>
                      <Box
                        mb={{ xs: 2, md: 0 }}
                        sx={{
                          width: "34%",
                        }}
                      >
                        <Typography
                          variant="body2"
                          mb={1}
                          className={classes.grayColor}
                        >
                          Phone
                        </Typography>
                        <Typography
                          variant="body2"
                          className={classes.blackColor}
                        >
                          {clientsById?.countryCode && clientsById?.phoneNumber
                            ? clientsById?.countryCode +
                              " " +
                              clientsById?.phoneNumber
                            : ""}
                        </Typography>
                      </Box>
                      <Box
                        mb={{ xs: 2, md: 0 }}
                        sx={{
                          width: "23%",
                        }}
                      >
                        <Typography
                          variant="body2"
                          mb={1}
                          className={classes.grayColor}
                        >
                          Created On
                        </Typography>
                        <Typography
                          variant="body2"
                          className={classes.blackColor}
                        >
                          {clientsById?.creationDate
                            ? formatDate(clientsById?.creationDate)
                            : ""}
                        </Typography>
                      </Box>
                    </Box>
                    <Box
                      mt={{ xs: 2, md: 3 }}
                      // mr={3}
                      sx={{
                        justifyContent: "space-between",
                        display: { xs: "block", md: "flex" },
                      }}
                    >
                      <Box
                        mb={{ xs: 2, md: 0 }}
                        // mx={{ xs: 2, md: 4 }}
                        sx={{
                          width: "40%",
                        }}
                      >
                        <Typography
                          variant="body2"
                          mb={1}
                          className={classes.grayColor}
                        >
                          Client ID
                        </Typography>
                        <Typography
                          variant="body2"
                          className={classes.blackColor}
                        >
                          {clientsById?.id}
                        </Typography>
                      </Box>
                      <Box
                        mb={{ xs: 2, md: 0 }}
                        sx={{
                          width: "34%",
                        }}
                      >
                        <Typography
                          variant="body2"
                          mb={1}
                          className={classes.grayColor}
                        >
                          Country
                        </Typography>
                        <Typography
                          variant="body2"
                          className={classes.blackColor}
                        >
                          {clientsById?.countryName}
                        </Typography>
                      </Box>
                      <Box
                        mb={{ xs: 2, md: 0 }}
                        sx={{
                          width: "23%",
                        }}
                      ></Box>
                    </Box>
                    <Box mt={3}>
                      <Typography
                        variant="body2"
                        mb={1}
                        className={classes.grayColor}
                      >
                        Company Address
                      </Typography>
                      <Typography
                        variant="body2"
                        className={classes.blackColor}
                      >
                        {clientsById?.address}
                      </Typography>
                    </Box>
                  </Box>
                </Box>
                <CommonTable
                  title="Manage Team"
                  data={transformData}
                  columns={columns}
                  actions={renderActions}
                  rowIdKey="id"
                  searchProps={{
                    value: searchQuery,
                    onChange: setSearchQuery,
                    placeholder: "Search team members",
                  }}
                  primaryAction={{
                    label: "Add Member",
                    onClick: handleOpenDialog,
                  }}
                  heightOfTable="auto"
                  showPagination={true}
                  page={page}
                  onPageChange={handleChangePage}
                  totalPages={Math.ceil(
                    getAllTeamMembers?.total / rowsPerPage
                  )}
                  count={getAllTeamMembers?.total}
                  perPage={rowsPerPage}
                />
                <AddNewTeamMember
                open={openDialog}
                handleClose={handleCloseDialog}
              />
              </>
            )}
          </Grid>
          {/* <TablePagination
            rowsPerPageOptions={[10, 25]}
            component="div"
            count={
              getAllTeamMembers?.filter((each: any) => each?.status)
                ? getAllTeamMembers?.filter((each: any) => each?.status).length
                : 0
            }
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
          /> */}
        </Grid>
      ) : (
        <NoAccessPage />
      )}
    </>
  );
};

export default ManageClientDetails;
