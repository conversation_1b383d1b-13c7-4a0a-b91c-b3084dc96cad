import { createAsyncThunk } from "@reduxjs/toolkit";
import { WORKFLOW_API } from "../../../Apis/AdminLogin/Automation/Workflows";

export const saveWorkflowCustomResponse = createAsyncThunk(
  "workflow/saveWorkflowCustomResponse",
  async (payload: {
    businessId: string;
    nodeId: string;
    userId: string;
    attributeId: string;
  }) => {
    const response = await WORKFLOW_API.saveWorkflowCustomResponse(payload);
    return response.data;
  }
); 