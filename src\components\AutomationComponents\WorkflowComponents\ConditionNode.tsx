import { Box, Button, IconButton, Paper, Typography } from "@mui/material";
import React, { useState, useEffect } from "react";
import { bgColors } from "../../../utils/bgColors";
import { Position } from "reactflow";
import { NodeProps, useReactFlow } from "reactflow";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import DeleteOutlinedIcon from "@mui/icons-material/DeleteOutlined";
import CustomHandle from "./CustomHandle";
import { CheckCircle, ArrowRight } from "@mui/icons-material";
import MessagePanelCondition from "./MessagePanelCondition";
import CurrencyRupeeIcon from "@mui/icons-material/CurrencyRupee";
import { ConditionOperator } from "./enums";
import { useWorkflow } from "../../../contexts/WorkflowContext";

interface ConditionalNodeProps {
  data: {
    conditionType: ConditionOperator;
    attribute: string;
    conditionValue: string;
    selectedPaths?: ("true" | "false")[];
    onConditionTypeChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
    onAttributeChange: (attribute: string) => void;
    onValueChange: (value: string) => void;
    onPathChange?: (paths: ("true" | "false")[]) => void;
    onDelete?: () => void;
    isMessagePanelOpen: boolean;
    isValid?: boolean;
  };

  id: string;
}

const ConditionNode: React.FC<ConditionalNodeProps> = ({ data, id }) => {
  const {
    conditionType,
    attribute,
    conditionValue,
    selectedPaths = ["true", "false"],
    isMessagePanelOpen,
  } = data;

  const { handleNodeDelete } = useWorkflow();
  const { getNode, getNodes, getEdges, setNodes, fitView } = useReactFlow();

  const currentNode = getNode(id);
  const currentNodeData = currentNode?.data;

  const updateNodeData = (updateFn: (data: any) => any) => {
    setNodes((nds: any[]) =>
      nds.map((node: any) => {
        if (node.id === id) {
          const updatedData = updateFn(node.data);

          // Only validate if the node has already been marked as invalid
          // This ensures validation only runs after the Save button has been clicked
          if (updatedData.isValid === false) {
            // Validate the node data to determine if all errors are resolved
            // For condition nodes, we need to check if attribute and condition value are provided
            const isAttributeSelected = !!updatedData.attribute;
            const isConditionValueProvided = !!updatedData.conditionValue;

            // Check if both paths are connected to other nodes
            // This would require checking edges, which we don't have direct access to here
            // We'll rely on the main validation function for this check

            // Only set isValid to true if all validation checks pass
            if (isAttributeSelected && isConditionValueProvided) {
              updatedData.isValid = true;
            }
          }

          return { ...node, data: updatedData };
        }
        return node;
      })
    );
  };
  const handleEdit = () => {
    updateNodeData((data) => ({
      ...data,
      isMessagePanelOpen: !data.isMessagePanelOpen,
    }));
  };
  const handleDelete = () => {
    handleNodeDelete(id);
  };

  const handlePathClick = (path: "true" | "false") => {
    // Remove the path deletion functionality since condition nodes should always have both paths
    if (data.onPathChange) {
      data.onPathChange(["true", "false"]);
    }

    updateNodeData((data) => ({
      ...data,
      selectedPaths: ["true", "false"],
      conditionButtons: data.conditionButtons.filter((btn: any) =>
        ["true", "false"].includes(btn.name)
      ),
    }));
  };

  const handleConditionTypeChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    updateNodeData((data) => ({
      ...data,
      conditionType: e.target.value,
    }));
  };
  const handleOperationTypeChange = (value: any) => {
    updateNodeData((data) => ({
      ...data,
      operationType: value,
    }));
  };
  const handleAttributeChange = (value: string) => {
    updateNodeData((data) => ({
      ...data,
      attribute: value,
      conditionValue: "",
    }));
  };
  const handleConditionValueChange = (value: string) => {
    updateNodeData((data) => ({
      ...data,
      conditionValue: value,
    }));
  };
  const handlePathChange = (value: any) => {
    updateNodeData((data) => ({
      ...data,
      selectedPaths: value,
    }));
  };

  // Format value based on attribute type
  const formatValue = (value: string, attr: string): string => {
    switch (attr.toLowerCase()) {
      case "budget":
        // Format number in Indian number system
        return new Intl.NumberFormat("en-IN").format(Number(value));
      case "phone":
        // Format phone number with spaces
        return value.replace(/(\d{3})(\d{3})(\d{4})/, "$1 $2 $3");
      default:
        return value;
    }
  };

  // Render value with appropriate formatting
  const renderValue = (value: string, attr: string) => {
    const formattedValue = formatValue(value, attr);

    if (attr.toLowerCase() === "budget") {
      return (
        <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
          <CurrencyRupeeIcon sx={{ fontSize: 16 }} />
          <Typography
            component="span"
            sx={{
              backgroundColor: "#F0F0F0",
              color: "#8E9196",
              borderRadius: "4px",
              padding: "2px 6px",
              fontSize: "16px",
            }}
          >
            {formattedValue}
          </Typography>
        </Box>
      );
    }

    return (
      <Typography
        component="span"
        sx={{
          backgroundColor: "#F0F0F0",
          color: "#8E9196",
          borderRadius: "4px",
          padding: "2px 6px",
          fontSize: "16px",
        }}
      >
        {formattedValue}
      </Typography>
    );
  };

  const [connectingButtonId, setConnectingButtonId] = useState<string | null>(
    null
  );

  useEffect(() => {
    const handleConnect = (event: any) => {
      const sourceHandle = event.sourceHandle;
      if (sourceHandle?.startsWith("right-conditionButtonId-")) {
        const buttonId = sourceHandle.split("-")[2];
        if (!data.attribute) {
          setConnectingButtonId(buttonId);
          // Prevent the connection
          event.preventDefault();
        }
      }
    };

    const handleConnectEnd = () => {
      setConnectingButtonId(null);
    };

    // Add event listeners
    document.addEventListener("reactflow-connect", handleConnect);
    document.addEventListener("reactflow-connect-end", handleConnectEnd);

    // Cleanup
    return () => {
      document.removeEventListener("reactflow-connect", handleConnect);
      document.removeEventListener("reactflow-connect-end", handleConnectEnd);
    };
  }, [data.attribute]);

  const renderPathButton = (pathType: "true" | "false") => {
    // Only render button if the path is selected
    if (!selectedPaths.includes(pathType)) return null;

    const buttonId = currentNodeData?.conditionButtons?.find(
      (btn: any) => btn.name === pathType
    )?.id;
    const hasAttribute = Boolean(data?.attribute);
    const isConnecting = connectingButtonId === buttonId;

    return (
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          gap: "6px",
          fontSize: "13px",
          color: "#555",
          padding: "6px 10px",
          borderRadius: "4px",
          cursor: "pointer",
          position: "relative",
          border: "1px solid #e0e0e0",
          width: "100%",
        }}
        onClick={() => handlePathClick(pathType)}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: "6px" }}>
          <CheckCircle
            fontSize="small"
            sx={{
              color: pathType === "true" ? "#4CAF50" : "#EB5757",
            }}
          />
          <Button
            variant="outlined"
            fullWidth
            sx={{
              background: bgColors.green1,
              borderRadius: 2,
              mt: 0,
              color: bgColors.green,
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              position: "relative",
              height: "36px",
              "& .react-flow__handle": {
                zIndex: 1002,
              },
              ...(hasAttribute
                ? {}
                : {
                    borderColor: "error.main",
                    "&:hover": {
                      borderColor: "error.main",
                    },
                  }),
            }}
          >
            {pathType}
            <Box
              sx={{
                position: "absolute",
                right: -2,
                height: "100%",
                display: "flex",
                alignItems: "center",
              }}
            >
              <CustomHandle
                type="source"
                position={Position.Right}
                id={`right-conditionButtonId-${buttonId}`}
                style={{
                  background: hasAttribute ? bgColors.green : "#d32f2f",
                  width: "8px",
                  height: "8px",
                  border: "2px solid white",
                }}
              />
            </Box>
          </Button>
        </Box>
        {!hasAttribute && isConnecting && (
          <Typography
            variant="caption"
            sx={{
              color: "error.main",
              fontSize: "12px",
              ml: 4,
            }}
          >
            Please select an attribute to enable connections
          </Typography>
        )}
      </Box>
    );
  };

  return (
    <Paper
      elevation={3}
      sx={{
        // p: 2,
        borderRadius: 2,
        position: "relative",
        backgroundColor: "white",
        border: `1px solid ${bgColors.gray2}`,
        boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.1)",
        width: 280,
      }}
    >
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        sx={{
          backgroundColor: data.isValid === false ? "#FF7171" : bgColors.green,
          color: "white",
          padding: "8px 12px",
          borderTopLeftRadius: "6px",
          borderTopRightRadius: "6px",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography variant="h6">Send Message</Typography>
        <Box display="flex" flexDirection="row">
          <IconButton
            size="small"
            sx={{
              background: "transparent",
              border: "none",
              color: "white",
              cursor: "auto",
              padding: "2px",
              borderRadius: "3px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              "&:hover": {
                backgroundColor: "rgba(255, 255, 255, 0.2)",
              },
            }}
            onClick={handleEdit}
          >
            <EditOutlinedIcon />
          </IconButton>
          <IconButton
            size="small"
            sx={{
              background: "transparent",
              border: "none",
              color: "white",
              cursor: "auto",
              padding: "2px",
              borderRadius: "3px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              "&:hover": {
                backgroundColor: "rgba(255, 255, 255, 0.2)",
              },
            }}
            onClick={handleDelete}
          >
            <DeleteOutlinedIcon />
          </IconButton>
        </Box>
        <Box sx={{ position: "absolute", top: 100, left: -2 }}>
          <CustomHandle
            type="target"
            position={Position.Left}
            id={`left-nodeId-${id}`}
          />
        </Box>
        <Box sx={{ position: "absolute", top: 100, right: -2 }}>
          <CustomHandle
            type="source"
            position={Position.Right}
            id={`right-nodeId-${id}`}
          />
        </Box>
      </Box>
      <Box p={1}>
        {data.isValid === false && (
          <Box
            sx={{
              backgroundColor: "#FFEEEE",
              p: 1,
              mb: 2,
              borderRadius: 1,
              border: "1px solid #FF7171",
            }}
          >
            <Box component="ul" sx={{ m: 0, pl: 2 }}>
              {!data.attribute && (
                <Typography component="li" variant="caption" color="error">
                  Attribute must be selected
                </Typography>
              )}
              {!data.conditionValue && (
                <Typography component="li" variant="caption" color="error">
                  Condition value must be provided
                </Typography>
              )}
              <Typography component="li" variant="caption" color="error">
                Both TRUE and FALSE paths must be connected to other nodes
              </Typography>
            </Box>
          </Box>
        )}
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            flexWrap: "wrap",
            alignItems: "start",
            gap: "6px",
            marginBottom: "16px",
            pl: 2,
          }}
        >
          {data?.conditionType && (
            <Box display={"flex"} flexDirection={"column"} gap={1}>
              <Typography variant="subtitle1">
                Selected Condition Type:{" "}
              </Typography>
              <Typography
                component="span"
                sx={{
                  backgroundColor: bgColors.green1,
                  color: bgColors.green,
                  borderRadius: "4px",
                  padding: "2px 6px",
                  width: "fit-content",
                }}
              >
                {ConditionOperator[data.conditionType]}
              </Typography>
            </Box>
          )}

          {data?.attribute && (
            <Box>
              <Typography variant="subtitle1">Selected Attribute: </Typography>
              <Typography
                component="span"
                sx={{
                  backgroundColor: bgColors.green1,
                  color: bgColors.green,
                  borderRadius: "4px",
                  padding: "2px 6px",
                  fontSize: "16px",
                  fontWeight: "bold",
                }}
              >
                {data.attribute}
              </Typography>
            </Box>
          )}

          {data?.conditionValue && (
            <Box>
              <Typography variant="subtitle1">Selected Value: </Typography>
              {renderValue(data.conditionValue, data.attribute)}
            </Box>
          )}
        </Box>
      </Box>
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          gap: "6px",
          fontSize: "13px",
          color: "#555",
          padding: "6px 10px",
          borderRadius: "4px",
          cursor: "pointer",
          position: "relative",
          border: "1px solid #e0e0e0",
        }}
      >
        {renderPathButton("true")}
        {renderPathButton("false")}
      </Box>
      {isMessagePanelOpen && (
        <MessagePanelCondition
          handleEdit={handleEdit}
          handleDelete={handleDelete}
          conditionType={conditionType}
          attribute={attribute}
          conditionValue={conditionValue}
          selectedPaths={selectedPaths}
          handleConditionTypeChange={handleConditionTypeChange}
          handleAttributeChange={handleAttributeChange}
          handleConditionValueChange={handleConditionValueChange}
          handlePathChange={handlePathChange}
        />
      )}
    </Paper>
  );
};

export default ConditionNode;
