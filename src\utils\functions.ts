import { ContentState, convertFromHTML, convertToRaw } from "draft-js";
import parsePhoneNumberFromString from "libphonenumber-js";
import moment from "moment-timezone";
import LanguagesList from "./languagesList.json";

/**
 * Converts a UTC time string to the local system time zone.
 *
 * @param {string} utcTimeString - The UTC time string (e.g., '2024-08-01T12:00:00Z').
 * @returns {string} - The local time formatted as per the local system time zone.
 */

export const parseTextToDraft = (text: string) => {
  // Replace markdown-like symbols with HTML tags for inline styles
  let htmlText = text
    ?.replace(/\*(.*?)\*/g, "<b>$1</b>")
    ?.replace(/_(.*?)_/g, "<i>$1</i>")
    ?.replace(/~(.*?)~/g, "<strike>$1</strike>");

  // Replace number points with HTML list items for ordered list
  htmlText = htmlText?.replace(/^\d+\.\s+(.*)$/gm, "<ol><li>$1</li></ol>");

  // Replace bullet points with HTML list items for unordered list
  htmlText = htmlText?.replace(/^-\s+(.*)$/gm, "<ul><li>$1</li></ul>");

  // Wrap ordered list items in <ol> tags
  if (htmlText?.includes("<ol><li>")) {
    htmlText = htmlText?.replace(/<\/li><ol>/g, "</li>");
  }

  // Wrap unordered list items in <ul> tags
  if (htmlText?.includes("<ul><li>")) {
    htmlText = htmlText?.replace(/<\/li><ul>/g, "</li>");
  }

  // Replace new lines with HTML <br> tags for preserving line breaks
  htmlText = htmlText?.replace(/\n/g, "<br>");
  // Convert HTML to draft content state
  const blocksFromHTML = convertFromHTML(htmlText);
  const contentState = ContentState?.createFromBlockArray(
    blocksFromHTML?.contentBlocks,
    blocksFromHTML?.entityMap
  );
  return contentState;
};

export const parseTextToHtml = (text: string) => {
  let inUl = false;
  let inOl = false;
  let htmlText = "";

  // Split the text into lines
  const lines = text?.split("\n");

  lines?.forEach((line) => {
    // Handle unordered list items
    if (line.match(/^-\s+(.*)$/)) {
      if (!inUl) {
        if (inOl) {
          htmlText += "</ol>";
          inOl = false;
        }
        inUl = true;
        htmlText += "<ul>";
      }
      htmlText += `<li>${line?.replace(/^-\s+/, "")}</li>`;
    }
    // Handle ordered list items
    else if (line?.match(/^\d+\.\s+(.*)$/)) {
      if (!inOl) {
        if (inUl) {
          htmlText += "</ul>";
          inUl = false;
        }
        inOl = true;
        htmlText += "<ol>";
      }
      htmlText += `<li>${line?.replace(/^\d+\.\s+/, "")}</li>`;
    }
    // Handle non-list items
    else {
      if (inUl) {
        htmlText += "</ul>";
        inUl = false;
      }
      if (inOl) {
        htmlText += "</ol>";
        inOl = false;
      }
      htmlText += line + "<br>";
    }
  });

  // Close any remaining open tags
  if (inUl) htmlText += "</ul>";
  if (inOl) htmlText += "</ol>";

  // Handle inline styles
  htmlText = htmlText
    ?.replace(/\*(.*?)\*/g, "<b>$1</b>")
    ?.replace(/_(.*?)_/g, "<i>$1</i>")
    ?.replace(/~(.*?)~/g, "<strike>$1</strike>");

  return htmlText;
};

export const formatContent = (contentState: ContentState): string => {
  const blocks = convertToRaw(contentState).blocks;
  let formattedText = "";
  let listItemCounter = 1;

  const applyStyles = (text: string, styles: string[]): string => {
    let leadingSpaces = text.match(/^(\s*)/)?.[0] || "";
    let trailingSpaces = text.match(/(\s*)$/)?.[0] || "";
    let coreText = text?.trim();

    styles.forEach((style) => {
      if (style === "BOLD") coreText = `*${coreText}*`;
      if (style === "ITALIC") coreText = `_${coreText}_`;
      if (style === "STRIKETHROUGH") coreText = `~${coreText}~`;
    });

    return `${leadingSpaces}${coreText}${trailingSpaces}`;
  };

  blocks.forEach((block, index) => {
    if (index !== 0) formattedText += "\n";

    const chars = Array.from(block.text);
    const styleRanges = block.inlineStyleRanges;

    // Build a style map for each character index
    const styleMap: Record<number, string[]> = {};
    styleRanges.forEach((range) => {
      for (let i = range.offset; i < range.offset + range.length; i++) {
        styleMap[i] = styleMap[i] || [];
        styleMap[i].push(range.style);
      }
    });

    // Group characters by style
    let result = "";
    let currentGroup: string[] = [];
    let currentStyles: string[] | undefined = undefined;

    for (let i = 0; i < chars.length; i++) {
      const char = chars[i];
      const styles = styleMap[i] || [];

      const sameStyles =
        currentStyles?.length === styles.length &&
        currentStyles.every((s) => styles.includes(s));

      if (!sameStyles) {
        if (currentGroup.length) {
          result += applyStyles(currentGroup.join(""), currentStyles || []);
        }
        currentGroup = [char];
        currentStyles = styles;
      } else {
        currentGroup.push(char);
      }
    }

    if (currentGroup.length) {
      result += applyStyles(currentGroup.join(""), currentStyles || []);
    }

    let blockText = result;

    if (block.type === "unordered-list-item") {
      blockText = `- ${blockText?.trim()}`;
    } else if (block.type === "ordered-list-item") {
      blockText = `${listItemCounter}. ${blockText?.trim()}`;
      listItemCounter++;
    } else {
      listItemCounter = 1;
    }

    formattedText += blockText;
  });

  return formattedText;
};

export const processMessages = (messages: any) => {
  let lastDisplayedTime: string | null = null;
  return messages?.map((msg: any) => {
    const messageDate = new Date(msg?.createdAt);
    const formattedTime = messageDate?.toLocaleTimeString("en-UK", {
      hour: "2-digit",
      minute: "2-digit",
    });

    const displayTime =
      !lastDisplayedTime || lastDisplayedTime !== formattedTime;
    lastDisplayedTime = formattedTime;

    return {
      ...msg,
      displayTime,
    };
  });
};

export const sumOfUnreads = (contacts: any) => {
  return contacts?.reduce(
    (total: number, contact: any) => total + (contact?.unRead || 0),
    0
  );
};

export function convertUtcToLocal(utcTimeString: string): string {
  // Get the local system time zone
  const localTimeZone = moment.tz.guess();

  // Convert the UTC time to the local system time zone
  const localTime = moment.utc(utcTimeString).clone().tz(localTimeZone);

  // Return the formatted local time
  return localTime.format();
}

export const getUniformString = (array: any) => {
  if (array.length === 0) return null;

  const firstItem = array[0];
  for (let i = 1; i < array.length; i++) {
    if (array[i]?.assignee !== firstItem?.assignee) {
      return null;
    }
  }
  return firstItem?.assignee;
};

// Function to validate phone number with country code
export const validatePhoneNumber = (
  phoneNumber: string,
  countryCode: string
) => {
  const phone = parsePhoneNumberFromString(countryCode + phoneNumber);
  return phone ? phone.isValid() : false;
};

export const validateVariablesContainedText = (type: string, body: string) => {
  // Trim spaces around variables
  const bodyText = body.replace(/(\s*)\{\{(\d+)\}\}(\s*)/g, "{{$2}}");

  // Check if body text starts or ends with a variable
  if (bodyText.startsWith("{{") || bodyText.endsWith("}}")) {
    return false;
  }

  if (bodyText.startsWith("#") || bodyText.endsWith("#")) {
    return false;
  }

  // Check if at least one letter is present between two variables
  const variableRegex = /{{\d+}}/g;
  const matches = bodyText.match(variableRegex);

  // if (matches) {
  //   for (let i = 0; i < matches.length - 1; i++) {
  //     const startIndex = bodyText.indexOf(matches[i]) + matches[i].length;
  //     const endIndex = bodyText.indexOf(matches[i + 1]);
  //     if (endIndex - startIndex <= 0) {
  //       return false;
  //     }
  //   }
  // }

  const leadratVariableRegex = /#([^\s#][\w\s]*[^\s#])#/g;
  const leadratMatches = bodyText.match(leadratVariableRegex);
  // if (leadratMatches) {
  //   for (let i = 0; i < leadratMatches.length - 1; i++) {
  //     const startIndex =
  //       bodyText.indexOf(leadratMatches[i]) + leadratMatches[i].length;
  //     const endIndex = bodyText.indexOf(leadratMatches[i + 1]);
  //     if (endIndex - startIndex <= 0) {
  //       return false;
  //     }
  //   }
  // }

  // Split the sentence into words
  const words = body.split(/\s+/).filter((word) => word.length > 0);

  // Filter out variables like {{1}}, {{2}}, etc.
  const nonVariableWords = words.filter((word) => !/{{\d+}}/.test(word));

  // const nonLeadratVariableWords = words.filter((word) => !/#.*?#/.test(word));
  const nonLeadratVariableWords = words.filter((word) => !/#.*?#/.test(word));

  // Calculate the word count excluding variables
  const wordCount = nonVariableWords.length;

  const nonLeadratVariableWordsCount = nonLeadratVariableWords.length;

  // Calculate the number of variables
  const variableCount = matches ? matches.length : 0;
  const leadratVariableCount = leadratMatches ? leadratMatches.length : 0;

  // Ensure the number of variables is less than half of the word count
  if (type === "body" && variableCount >= wordCount / 2) {
    return false;
  } else if (type === "header" && variableCount > 1) {
    return false;
  }

  if (
    type === "body" &&
    leadratVariableCount >= nonLeadratVariableWordsCount / 2
  ) {
    return true;
  } else if (type === "header" && leadratVariableCount > 1) {
    return false;
  }

  return true;
};

export function formatLastMessage(message: string) {
  if (!message) {
    return "";
  }
  let formattedMessage = message.replace(/\n/g, " "); // Replace new lines with spaces

  formattedMessage = formattedMessage.replace(
    /(\d+(\.\d+)?%)/g,
    "<strong>$1</strong>"
  );
  formattedMessage = formattedMessage.replace(
    /🔹\s(.+)/g,
    "🔹 <strong>$1</strong>"
  );
  formattedMessage = formattedMessage.replace(
    /(https?:\/\/[^\s]+)/g,
    "<strong>$1</strong>"
  );
  formattedMessage = formattedMessage.replace(
    /\*(.*?)\*/g,
    "<strong>$1</strong>"
  );

  return formattedMessage;
}

// Utility function to convert a string to camelCase
export const toCamelCase = (str: string): string => {
  return str
    .replace(/_./g, (match) => match.charAt(1).toUpperCase())
    .replace(/^(.)/, (match) => match.toLowerCase());
};

// Function to recursively transform object keys to camelCase
export const transformKeysToCamelCase = (obj: any): any => {
  if (Array.isArray(obj)) {
    return obj.map(transformKeysToCamelCase); // Recursively transform each item in the array
  } else if (obj !== null && typeof obj === "object") {
    return Object.keys(obj).reduce((acc: any, key: string) => {
      const camelCaseKey = toCamelCase(key);
      acc[camelCaseKey] = transformKeysToCamelCase(obj[key]);
      return acc;
    }, {});
  }
  return obj; // Return the value if it's not an object or array
};

export const getCurrentDate = () => {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, "0"); // January is 0!
  const day = String(today.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
};

// Helper function to count variables in a string 88
export const countVariables = (text: string) => {
  // Regex to match both {{x}} and {x} patterns #[^#\s]+#
  const variablePattern = /{{\d+}}|{\d+}|#.*?#/g;

  const matches = text.match(variablePattern) || [];
  return matches.length;
};

export const validateUrl = (url: string) => {
  const urlRegex = new RegExp(
    "^(https?:\\/\\/)?" + // optional protocol
      "((([a-zA-Z\\d]([a-zA-Z\\d-]*[a-zA-Z\\d])*)\\.)*([a-zA-Z\\d]([a-zA-Z\\d-]*[a-zA-Z\\d])*\\.[a-zA-Z]{2,})|" + // domain name or subdomain
      "localhost|" + // OR localhost
      "\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}|" + // OR IPv4
      "\\[([a-fA-F\\d:.]+)\\])" + // OR IPv6
      "(\\:\\d+)?(\\/[-a-zA-Z\\d%_.~+{{\\d*}}]*)*" + // port and path, allowing {{x}} placeholders
      "(\\?[;&a-zA-Z\\d%_.~+=-{{\\d*}}]*)?" + // query string, allowing {{x}} placeholders
      "(\\#[-a-zA-Z\\d_\\/?{{\\d*}}]*)?$", // fragment locator with placeholders
    "i"
  );
  return urlRegex.test(url);
};
export const validateJson = (json: string) => {
  return JSON.parse(json);
};

export let getLangValue = (code: any) => {
  return LanguagesList.find((obj: any) => obj.code === code)?.value;
};

export function unescapeJsonString(jsonString: any) {
  return jsonString?.replace(/\\(["\\/bfnrt])/g, (match: any, char: any) => {
    switch (char) {
      case '"':
        return '"'; // Unescape double quote
      case "\\":
        return "\\"; // Unescape backslash
      case "/":
        return "/"; // Unescape forward slash
      case "b":
        return "\b"; // Unescape backspace
      case "f":
        return "\f"; // Unescape form feed
      case "n":
        return "\n"; // Unescape newline
      case "r":
        return "\r"; // Unescape carriage return
      case "t":
        return "\t"; // Unescape tab
      default:
        return match; // Return the original match if no replacement is found
    }
  });
}

export function getFormattedDateLabel(inputTimestamp: number): string {
  const now = new Date();
  const inputDate = new Date(inputTimestamp);

  // Strip time from both dates to compare only dates
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const input = new Date(
    inputDate.getFullYear(),
    inputDate.getMonth(),
    inputDate.getDate()
  );

  const diffInDays =
    (today.getTime() - input.getTime()) / (1000 * 60 * 60 * 24);

  if (diffInDays === 0) {
    return "Today";
  }

  if (diffInDays === 1) {
    return "Yesterday";
  }

  // Format as dd/mm/yy
  const day = String(inputDate.getDate()).padStart(2, "0");
  const month = String(inputDate.getMonth() + 1).padStart(2, "0");
  const year = String(inputDate.getFullYear()).slice(-2);

  return `${day}/${month}/${year}`;
}
export const formatTimeSinceMidnight = (date = new Date()): string => {
  const msSinceMidnight =
    date.getTime() -
    new Date(date.getFullYear(), date.getMonth(), date.getDate()).getTime();

  const totalSeconds = Math.floor(msSinceMidnight / 1000);
  const milliseconds = msSinceMidnight % 1000;

  const hours = Math.floor(totalSeconds / 3600)
    .toString()
    .padStart(2, "0");
  const minutes = Math.floor((totalSeconds % 3600) / 60)
    .toString()
    .padStart(2, "0");
  const seconds = (totalSeconds % 60).toString().padStart(2, "0");
  const microseconds = (milliseconds * 10000).toString().padStart(7, "0"); // 7-digit precision

  return `${hours}:${minutes}:${seconds}.${microseconds}`;
};

export const getMaxFileSize = (fileType: string): number => {
  switch (fileType) {
    case "image/png":
    case "image/jpg":
    case "image/jpeg":
      return 5 * 1024 * 1024; // 5 MB
    case "video/mp4":
    case "video/avi":
    case "video/mov":
    case "video/mpeg":
      return 16 * 1024 * 1024; // 16 MB
    case "application/pdf":
    case "application/msword":
    case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
    case "application/vnd.ms-excel":
    case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
      return 100 * 1024 * 1024; // 100 MB
    default:
      return 0;
  }
};

export function formatCarouselPayload(
  templateData: any,
  businessId: String,
  userId: String
) {
  const payload: any = {
    businessId: businessId,
    userId: userId,
    templateName: templateData.templateName,
    languageCode: templateData.language,
    category: templateData.category,
    subCategory: templateData.subCategory,
    mediaType: templateData.mediaType,
    header: templateData.header,
    body: templateData.body,
    mediaFile: templateData.mediaFile,
    carouselCardsJson: [],
  };
  if (templateData.templateId) {
    payload.templateId = templateData.templateId;
  }
  templateData.carouselCards.forEach((carousel: any) => {
    let formatedCarousel: any = {
      body: carousel.body,
      headerMediaUrl: carousel.headerMediaUrl,
      mediaUrlType: carousel.mediaUrlType,
    };
    carousel.carouselButtons.forEach((button: any) => {
      if (button.buttonType === "PHONE_NUMBER") {
        formatedCarousel.callButtonName = button.buttonName;
        formatedCarousel.countryCode = button.countryCode;
        formatedCarousel.phoneNumber = button.buttonValue;
      } else if (button.buttonType === "URL") {
        formatedCarousel.urlButtonName = formatedCarousel.urlButtonName
          ? [...formatedCarousel.urlButtonName, button.buttonName]
          : [button.buttonName];
        formatedCarousel.redirectUrl = formatedCarousel.redirectUrl
          ? [...formatedCarousel.redirectUrl, button.buttonValue]
          : [button.buttonValue];
      } else if (button.buttonType === "QUICK_REPLY") {
        formatedCarousel.quickReply = formatedCarousel.quickReply
          ? [...formatedCarousel.quickReply, button.buttonValue]
          : [button.buttonValue];
      }
    });
    payload.carouselCardsJson = [
      ...payload.carouselCardsJson,
      formatedCarousel,
    ];
  });

  return payload;
}

export function checkFileType(url: any) {
  const imageExtensions = ["jpg", "jpeg", "png", "gif", "bmp", "webp", "tiff"];
  const videoExtensions = ["mp4", "mov", "avi", "mkv", "flv", "webm", "wmv"];

  const extension = url.split(".").pop().toLowerCase();
  if (imageExtensions.includes(extension)) {
    return 3;
  } else if (videoExtensions.includes(extension)) {
    return 4;
  } else {
    return -1;
  }
}

export function refactorCarouselCards(data: any) {
  const result = data?.map((c: any, i: number) => {
    let carousel = JSON.parse(c);
    const buttonsArray = [];
    if (carousel.PhoneNumber) {
      const phone = {
        buttonType: "PHONE_NUMBER",
        buttonValue: carousel.PhoneNumber,
        countryCode: carousel.CountryCode,
        buttonName: carousel.CallButtonName,
      };
      buttonsArray.push(phone);
    }
    if (carousel.QuickReply?.length) {
      carousel.QuickReply?.forEach((reply: any) => {
        const quick = {
          buttonType: "QUICK_REPLY",
          buttonValue: reply,
        };
        buttonsArray.push(quick);
      });
    }
    if (carousel.RedirectUrl?.length) {
      carousel.RedirectUrl?.forEach((url: any, index: number) => {
        const urlType = {
          buttonType: "URL",
          buttonValue: url,
          buttonName: carousel.UrlButtonName[index],
        };
        buttonsArray.push(urlType);
      });
    }
    return {
      body: carousel.Body,
      headerMediaUrl: carousel.HeaderMediaUrl,
      mediaUrlType: carousel.MediaUrlType,
      carouselButtons: buttonsArray,
    };
  });
  return result;
}

export function refactorCarouselCardsForPreview(data: any) {
  const result = data.map((c: any, i: number) => {
    let carousel = c;
    const buttonsArray = [];
    if (carousel.PhoneNumber) {
      const phone = {
        buttonType: "PHONE_NUMBER",
        buttonValue: carousel.PhoneNumber,
        countryCode: carousel.CountryCode,
        buttonName: carousel.CallButtonName,
      };
      buttonsArray.push(phone);
    }
    if (carousel.QuickReply?.length) {
      carousel.QuickReply?.forEach((reply: any) => {
        const quick = {
          buttonType: "QUICK_REPLY",
          buttonValue: reply,
        };
        buttonsArray.push(quick);
      });
    }
    if (carousel.RedirectUrl?.length) {
      carousel.RedirectUrl?.forEach((url: any, index: number) => {
        const urlType = {
          buttonType: "URL",
          buttonValue: url,
          buttonName: carousel.UrlButtonName[index],
        };
        buttonsArray.push(urlType);
      });
    }
    return {
      body: carousel.Body,
      headerMediaUrl: carousel.HeaderMediaUrl,
      mediaUrlType: carousel.MediaUrlType,
      carouselButtons: buttonsArray,
    };
  });
  return result;
}
