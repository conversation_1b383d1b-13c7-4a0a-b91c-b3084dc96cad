import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { CAMPAIGN_API } from "../../../Apis/Campaign/Campaign";

export interface IData {
    status: "loading" | "succeeded" | "failed" | "idle";
    data: any;
  }

  const initialState: IData = {
    status: "idle",
    data: null,
  };

  export const rerunCampaign = createAsyncThunk(
    "rerunCampaign",
    async ( payload: any, { rejectWithValue }) => {
      try {
        const response = await CAMPAIGN_API.rerunCampaign(payload);
        return response?.data;
      } catch (error: any) {
        // Provide a descriptive error message
        return rejectWithValue(error.response || 'An error occurred');
      }
    }
  );


  export const rerunCampaignSlice = createSlice({
    name: "rerunCampaignSlice",
    initialState,
    reducers: {
       
    },
    extraReducers: (builder) => {
      builder
        .addCase(rerunCampaign.pending, (state) => {
          state.status = "loading";
          state.data = null;
        })
        .addCase(rerunCampaign.fulfilled, (state, action) => {
          state.status = "succeeded";
          state.data = action.payload;
        })
        .addCase(rerunCampaign.rejected, (state) => {
          state.status = "failed";
        });
    },
  });


export const rerunCampaignActions = rerunCampaignSlice.actions;
export default rerunCampaignSlice.reducer;
