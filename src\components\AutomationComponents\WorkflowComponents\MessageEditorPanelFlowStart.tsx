import {
  Autocomplete,
  Box,
  Button,
  Checkbox,
  Chip,
  CircularProgress,
  ClickAwayListener,
  Collapse,
  Divider,
  FormControl,
  FormControlLabel,
  IconButton,
  Paper,
  Radio,
  RadioGroup,
  TextField,
  Typography,
  Select,
  MenuItem,
} from "@mui/material";
import React, { useEffect, useRef, useState, useCallback } from "react";
import CustomResponseComp from "./CustomResponseComp";
import { bgColors } from "../../../utils/bgColors";
import CloseIconSvg from "../../../assets/svgs/CloseIconSvg";
import { ContentState, convertToRaw, EditorState } from "draft-js";

import { formatContent } from "../../../utils/functions";
import { LeadSource, LeadSourceLabels } from "./enums";
import {
  CheckBoxOutlineBlank,
  CheckBox,
  ExpandMore,
  ExpandLess,
} from "@mui/icons-material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import CheckIcon from "@mui/icons-material/Check";
import Popper from "@mui/material/Popper";
import { useReactFlow } from "reactflow";
import { useParams } from "react-router-dom";
import { createKeywordReactflow } from "../../../redux/slices/Workflows/createKeywordsReactflowSlice";
import { useAppDispatch, useAppSelector } from "../../../utils/redux-hooks";
import { toastActions } from "../../../utils/toastSlice";
import { deleteKeywordReactflow } from "../../../redux/slices/Workflows/deleteKeywordsReactflowSlice";
import { keys } from "lodash";
import { getKeywords } from "../../../redux/slices/Workflows/getKeywordsSlice";

interface MessageEditorPanelFlowStartProps {
  keywords: string[];
  allLeadSources: string[];
  allLeadStatusChanges: string[];
  allLeadProjects: string[];
  triggerType: string;
  handleEdit: () => void;
  handleKeywordsChange: (value: string[]) => void;
  handleTriggerTypeChange: (value: string) => void;
  sources: any;
  statusWithSubStatus?: any;
  projectData: any;
  onSubSourceChange: (selected: Record<string, string[]>) => void;
  onSubStatusChange: (selected: Record<string, string[]>) => void;
  placeholder?: string;
  className?: string;
  newLead: Record<string, string[]>;
  newStatusWithSubStatus: Record<string, string[]>;
  newProject: string[];
  setNewLead: (value: Record<string, string[]>) => void;
  setNewStatusWithSubStatus: (value: Record<string, string[]>) => void;
  setNewProject: (value: string[]) => void;
  newKeyword: string;
  setNewKeyword: (value: string) => void;
  data: any;
  allKeywords: any;
}

export type LeadSourceOption = {
  label: string;
  value: LeadSource;
};

const MessageEditorPanelFlowStart = ({
  keywords,
  allKeywords,
  allLeadSources,
  allLeadStatusChanges,
  allLeadProjects,
  triggerType,
  handleEdit,
  handleKeywordsChange,
  handleTriggerTypeChange,
  sources,
  statusWithSubStatus,
  projectData,
  onSubSourceChange,
  onSubStatusChange,
  placeholder,
  newLead,
  newStatusWithSubStatus,
  newProject,
  setNewLead,
  setNewStatusWithSubStatus,
  setNewProject,
  newKeyword,
  setNewKeyword,
  data,
}: MessageEditorPanelFlowStartProps) => {
  const { id: workflowId } = useParams();
  const dispatch = useAppDispatch();
  const createKeywordReactflowSlice = useAppSelector(
    (state: any) => state.createKeywordReactflow
  );
  const isLoading =
    createKeywordReactflowSlice.createKeywordReactflowStatus === "loading";

  const userData = useAppSelector((state: any) => state?.adminLogin?.data);

  const [error, setError] = useState("");
  const [localTriggerType, setLocalTriggerType] = useState(triggerType);

  const [isOpen, setIsOpen] = useState(false);
  const [isStatusOpen, setIsStatusOpen] = useState(false);
  const [isProjectOpen, setIsProjectOpen] = useState(false);
  const [activeSourceId, setActiveSourceId] = useState<string | null>(null);
  const [activeStatusId, setActiveStatusId] = useState<string | null>(null);
  const [activeProjectId, setActiveProjectId] = useState<string | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const handleTriggerTypeSelect = (value: string) => {
    setLocalTriggerType(value);
    handleTriggerTypeChange(value);
    // Close all dropdowns when switching trigger types
    setIsOpen(false);
    setIsStatusOpen(false);
    setIsProjectOpen(false);
    setActiveSourceId(null);
    setActiveStatusId(null);
    setActiveProjectId(null);
  };

  // keywords: keyword Handlers
  const handleAddKeyword = async (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    const trimmed = newKeyword.trim().toLocaleLowerCase();
    if (!trimmed) return setError("Keyword cannot be empty");
    if (keywords.includes(trimmed)) return setError("Keyword already exists");
    if (allKeywords.includes(trimmed))
      return setError("Keyword already exists in another workflow");
    if (keywords.length >= 5) return setError("Maximum 5 keywords allowed");
    handleKeywordsChange([...keywords, trimmed]);
    setNewKeyword("");
    setError("");
  };

  const handleRemoveKeyword = async (indexOrKeywords: number | string[]) => {
    let keywordsToDelete: string[];

    if (typeof indexOrKeywords === "number") {
      // Single keyword deletion
      keywordsToDelete = [keywords[indexOrKeywords]];
    } else {
      // Multiple keywords deletion
      keywordsToDelete = indexOrKeywords;
    }

    // Check if the node is saved or not
    if (data?.isSaved && allKeywords.includes(keywordsToDelete[0])) {
      // If node is saved, make API call to delete keywords
      const payload = {
        keywords: keywordsToDelete,
        workflowId: workflowId,
      };

      const response = await dispatch(deleteKeywordReactflow(payload));
      if (response.meta.requestStatus === "fulfilled") {
        // Update local state after successful API call
        if (typeof indexOrKeywords === "number") {
          // Single keyword removal
          handleKeywordsChange(
            keywords.filter((_, i) => i !== indexOrKeywords)
          );
        } else {
          // Multiple keywords removal
          handleKeywordsChange(
            keywords.filter((kw) => !keywordsToDelete.includes(kw))
          );
        }
        dispatch(getKeywords({ companyId: userData?.companyId }));
      } else {
        dispatch(
          toastActions.setToaster({
            message: response.payload.message,
            type: "error",
          })
        );
      }
    } else {
      // If node is not saved, delete keywords directly without API call
      if (typeof indexOrKeywords === "number") {
        // Single keyword removal
        handleKeywordsChange(keywords.filter((_, i) => i !== indexOrKeywords));
      } else {
        // Multiple keywords removal
        handleKeywordsChange(
          keywords.filter((kw) => !keywordsToDelete.includes(kw))
        );
      }
    }
  };

  // newLead: source and status Handlers
  const handleToggle = () => {
    setIsOpen((prev) => !prev);
    if (!isOpen) {
      setActiveSourceId(null);
    }
  };

  const getDisplayText = () => {
    const totalSelected = Object.values(newLead).reduce(
      (sum, items: any) => sum + items.length,
      0
    );

    // Count sources that exist in newLead (including those with empty arrays)
    const selectedSources = Object.keys(newLead);
    if (selectedSources.length === 0) return placeholder;

    if (selectedSources.length === 1) {
      const sourceId = selectedSources[0];
      const subSources = newLead[sourceId];

      // If source has no subsources (empty array), just show source name
      if (subSources.length === 0) {
        return sourceId;
      }

      // If source has one subsource selected
      if (subSources.length === 1) {
        return `${sourceId}: ${subSources[0]}`;
      }

      // If source has multiple subsources selected
      return `${sourceId}: ${subSources.length} sub-sources selected`;
    }

    // Multiple sources selected
    const totalSubSources = totalSelected;

    if (totalSubSources === 0) {
      return `${selectedSources.length} sources selected`;
    }

    return `${selectedSources.length} sources with ${totalSubSources} sub-sources selected`;
  };

  const isSourceFullySelected = (sourceId: string, subSources: string[]) => {
    if (subSources.length === 0) {
      return Object.prototype.hasOwnProperty.call(newLead, sourceId);
    }

    // Check if any subsource is already used in other workflows
    const usedSubSources = allLeadSources
      .filter((item: any) => item.source === sourceId)
      .flatMap((item: any) => item.subSource || []);

    // Get available subsources (excluding those already used)
    const availableSubSources = subSources.filter(
      (sub) => !usedSubSources.includes(sub)
    );

    // If no subsources are available, return false
    if (availableSubSources.length === 0) {
      return false;
    }

    const selectedSubSources = newLead[sourceId] || [];
    // Check if all available subsources are selected
    return availableSubSources.every((sub) => selectedSubSources.includes(sub));
  };

  const isSubSourceSelected = (sourceId: string, subSourceId: string) => {
    // Check if this subsource is already used in other workflows
    const isSubSourceUsed = allLeadSources.some(
      (item: any) =>
        item.source === sourceId &&
        item.subSource &&
        item.subSource.includes(subSourceId)
    );

    // If subsource is already used, return false
    if (isSubSourceUsed) {
      return false;
    }

    return (newLead[sourceId] || []).includes(subSourceId);
  };

  const handleSourceCheckboxToggle = (
    sourceId: string,
    allSubSources: string[]
  ) => {
    // For sources with no subsources
    if (allSubSources.length === 0) {
      // Check if source is already used in other workflows
      const isSourceUsed = allLeadSources.some(
        (item: any) =>
          item.source === sourceId &&
          (!item.subSource || item.subSource.length === 0)
      );

      if (isSourceUsed) {
        dispatch(
          toastActions.setToaster({
            message: `Source "${sourceId}" is already used in another workflow`,
            type: "error",
          })
        );
        return;
      }
    } else {
      // For sources with subsources, check if any subsource is already used
      const usedSubSources = allLeadSources
        .filter((item: any) => item.source === sourceId)
        .flatMap((item: any) => item.subSource || []);

      // Get available subsources (excluding those already used)
      const availableSubSources = allSubSources.filter(
        (sub) => !usedSubSources.includes(sub)
      );

      // If no subsources are available, show error and return
      if (availableSubSources.length === 0) {
        dispatch(
          toastActions.setToaster({
            message: `All subsources for "${sourceId}" are already used in other workflows`,
            type: "error",
          })
        );
        return;
      }
    }

    const currentSubSources = newLead[sourceId] || [];
    const isSourceFullySelected =
      allSubSources.length > 0
        ? allSubSources.every((subSource) =>
            currentSubSources.includes(subSource)
          )
        : Object.prototype.hasOwnProperty.call(newLead, sourceId);

    let updatedSubSources: string[];

    if (isSourceFullySelected) {
      updatedSubSources = [];
    } else {
      // Only include available subsources
      updatedSubSources =
        allSubSources.length > 0
          ? allSubSources.filter(
              (sub) =>
                !allLeadSources.some(
                  (item: any) =>
                    item.source === sourceId &&
                    item.subSource &&
                    item.subSource.includes(sub)
                )
            )
          : [];
    }

    const updatedLead = { ...newLead };

    if (allSubSources.length === 0) {
      if (isSourceFullySelected) {
        delete updatedLead[sourceId];
      } else {
        updatedLead[sourceId] = [];
      }
    } else {
      if (updatedSubSources.length === 0) {
        delete updatedLead[sourceId];
      } else {
        updatedLead[sourceId] = updatedSubSources;
      }
    }

    setNewLead({ ...updatedLead });
    onSubSourceChange({ ...updatedLead });

    if (allSubSources.length > 0 && !isSourceFullySelected) {
      setActiveSourceId(sourceId);
    }
  };

  const handleSubSourceToggle = (sourceId: string, subSource: string) => {
    // Check if this specific source-subsource combination is already used
    const isSubSourceUsed = allLeadSources.some(
      (item: any) =>
        item.source === sourceId &&
        item.subSource &&
        item.subSource.includes(subSource)
    );

    if (isSubSourceUsed) {
      dispatch(
        toastActions.setToaster({
          message: `Sub-source "${subSource}" for source "${sourceId}" is already used in another workflow`,
          type: "error",
        })
      );
      return;
    }

    const currentSubSources = newLead[sourceId] || [];
    const isSelected = currentSubSources.includes(subSource);

    let updatedSubSources: string[];

    if (isSelected) {
      updatedSubSources = currentSubSources.filter(
        (item) => item !== subSource
      );
    } else {
      updatedSubSources = [...currentSubSources, subSource];
    }

    const updatedLead = { ...newLead };
    if (updatedSubSources.length === 0) {
      delete updatedLead[sourceId];
    } else {
      updatedLead[sourceId] = updatedSubSources;
    }
    setNewLead({ ...updatedLead });
    onSubSourceChange({ ...updatedLead });
  };

  // newStatusWithSubStatus: status and subStatus Handlers
  const handleStatusToggle = () => {
    setIsStatusOpen((prev) => !prev);
    if (!isStatusOpen) {
      setActiveStatusId(null);
    }
  };

  const getStatusDisplayText = () => {
    const totalSelected = Object.values(newStatusWithSubStatus).reduce(
      (sum, items: any) => sum + items.length,
      0
    );
    const selectedStatus = Object.keys(newStatusWithSubStatus);
    if (selectedStatus.length === 0) return placeholder;
    if (selectedStatus.length === 1) {
      const statusId = selectedStatus[0];
      const subStatus = newStatusWithSubStatus[statusId];
      if (subStatus.length === 0) {
        return statusId;
      }
      if (subStatus.length === 1) {
        return `${statusId}: ${subStatus[0]}`;
      }
      return `${statusId}: ${subStatus.length} sub-statuses selected`;
    }
    const totalSubStatus = totalSelected;
    if (totalSubStatus === 0) {
      return `${selectedStatus.length} statuses selected`;
    }
    return `${selectedStatus.length} statuses with ${totalSubStatus} sub-statuses selected`;
  };

  const isStatusFullySelected = (statusId: string, subStatus: string[]) => {
    if (subStatus.length === 0) {
      return Object.prototype.hasOwnProperty.call(
        newStatusWithSubStatus,
        statusId
      );
    }

    // Check if any substatus is already used in other workflows
    const usedSubStatuses = allLeadStatusChanges
      .filter((item: any) => item.status === statusId)
      .flatMap((item: any) => item.subStatus || []);

    // Get available substatuses (excluding those already used)
    const availableSubStatuses = subStatus.filter(
      (sub) => !usedSubStatuses.includes(sub)
    );

    // If no substatuses are available, return false
    if (availableSubStatuses.length === 0) {
      return false;
    }

    const selectedSubStatus = newStatusWithSubStatus[statusId] || [];
    // Check if all available substatuses are selected
    return availableSubStatuses.every((sub) => selectedSubStatus.includes(sub));
  };

  const isSubStatusSelected = (statusId: string, subStatusId: string) => {
    // Check if this substatus is already used in other workflows
    const isSubStatusUsed = allLeadStatusChanges.some(
      (item: any) =>
        item.status === statusId &&
        item.subStatus &&
        item.subStatus.includes(subStatusId)
    );

    // If substatus is already used, return false
    if (isSubStatusUsed) {
      return false;
    }

    return (newStatusWithSubStatus[statusId] || []).includes(subStatusId);
  };

  const handleStatusCheckboxToggle = (
    statusId: string,
    allSubStatus: string[]
  ) => {
    // For statuses with no substatuses
    if (allSubStatus.length === 0) {
      // Check if status is already used in other workflows
      const isStatusUsed = allLeadStatusChanges.some(
        (item: any) =>
          item.status === statusId &&
          (!item.subStatus || item.subStatus.length === 0)
      );

      if (isStatusUsed) {
        dispatch(
          toastActions.setToaster({
            message: `Status "${statusId}" is already used in another workflow`,
            type: "error",
          })
        );
        return;
      }
    } else {
      // For statuses with substatuses, check if any substatus is already used
      const usedSubStatuses = allLeadStatusChanges
        .filter((item: any) => item.status === statusId)
        .flatMap((item: any) => item.subStatus || []);

      // Get available substatuses (excluding those already used)
      const availableSubStatuses = allSubStatus.filter(
        (sub) => !usedSubStatuses.includes(sub)
      );

      // If no substatuses are available, show error and return
      if (availableSubStatuses.length === 0) {
        dispatch(
          toastActions.setToaster({
            message: `All substatuses for "${statusId}" are already used in other workflows`,
            type: "error",
          })
        );
        return;
      }
    }

    const currentSubStatus = newStatusWithSubStatus[statusId] || [];
    const isStatusFullySelected =
      allSubStatus.length > 0
        ? allSubStatus.every((sub: string) => currentSubStatus.includes(sub))
        : Object.prototype.hasOwnProperty.call(
            newStatusWithSubStatus,
            statusId
          );

    let updatedSubStatus: string[];

    if (isStatusFullySelected) {
      updatedSubStatus = [];
    } else {
      // Only include available substatuses
      updatedSubStatus =
        allSubStatus.length > 0
          ? allSubStatus.filter(
              (sub) =>
                !allLeadStatusChanges.some(
                  (item: any) =>
                    item.status === statusId &&
                    item.subStatus &&
                    item.subStatus.includes(sub)
                )
            )
          : [];
    }

    const updatedStatusWithSubStatus = { ...newStatusWithSubStatus };
    if (allSubStatus.length === 0) {
      if (isStatusFullySelected) {
        delete updatedStatusWithSubStatus[statusId];
      } else {
        updatedStatusWithSubStatus[statusId] = [];
      }
    } else {
      if (updatedSubStatus.length === 0) {
        delete updatedStatusWithSubStatus[statusId];
      } else {
        updatedStatusWithSubStatus[statusId] = updatedSubStatus;
      }
    }

    setNewStatusWithSubStatus({ ...updatedStatusWithSubStatus });
    onSubStatusChange({ ...updatedStatusWithSubStatus });

    if (allSubStatus.length > 0 && !isStatusFullySelected) {
      setActiveStatusId(statusId);
    }
  };

  const handleSubStatusToggle = (statusId: string, subStatus: string) => {
    // Check if this specific status-substatus combination is already used
    const isSubStatusUsed = allLeadStatusChanges.some(
      (item: any) =>
        item.status === statusId &&
        item.subStatus &&
        item.subStatus.includes(subStatus)
    );

    if (isSubStatusUsed) {
      dispatch(
        toastActions.setToaster({
          message: `Sub-status "${subStatus}" for status "${statusId}" is already used in another workflow`,
          type: "error",
        })
      );
      return;
    }

    const currentSubStatus = newStatusWithSubStatus[statusId] || [];
    const isSelected = currentSubStatus.includes(subStatus);
    let updatedSubStatus: string[];
    if (isSelected) {
      updatedSubStatus = currentSubStatus.filter((item) => item !== subStatus);
    } else {
      updatedSubStatus = [...currentSubStatus, subStatus];
    }
    const updatedStatusWithSubStatus = { ...newStatusWithSubStatus };
    if (updatedSubStatus.length === 0) {
      delete updatedStatusWithSubStatus[statusId];
    } else {
      updatedStatusWithSubStatus[statusId] = updatedSubStatus;
    }
    setNewStatusWithSubStatus({ ...updatedStatusWithSubStatus });
    onSubStatusChange({ ...updatedStatusWithSubStatus });
  };

  // newProject: project Handlers
  const handleProjectToggle = () => {
    setIsProjectOpen((prev) => !prev);
    if (!isProjectOpen) {
      setActiveProjectId(null);
    }
  };

  const getProjectDisplayText = () => {
    const totalSelected = newProject.length;
    if (totalSelected === 0) return placeholder;
    if (totalSelected === 1) {
      return newProject[0];
    }
    return `${totalSelected} projects selected`;
  };

  const handleProjectCheckboxToggle = (projectId: string) => {
    // Check if project is already used in other workflows
    const isProjectUsed = allLeadProjects.includes(projectId);

    if (isProjectUsed) {
      dispatch(
        toastActions.setToaster({
          message: `Project "${projectId}" is already used in another workflow`,
          type: "error",
        })
      );
      return;
    }

    let updatedProjects: string[];
    if (newProject.includes(projectId)) {
      updatedProjects = newProject.filter((p) => p !== projectId);
    } else {
      updatedProjects = [...newProject, projectId];
    }
    setNewProject(updatedProjects);
  };

  const isProjectSelected = (projectId: string) => {
    // Check if project is already used in other workflows
    const isProjectUsed = allLeadProjects.includes(projectId);

    // If project is already used, return false
    if (isProjectUsed) {
      return false;
    }

    return newProject.includes(projectId);
  };

  const handleSelectAllProjects = () => {
    // Get available projects (not used in other workflows)
    const availableProjects = projectData.filter(
      (project: string) => !allLeadProjects.includes(project)
    );

    if (availableProjects.length === 0) {
      dispatch(
        toastActions.setToaster({
          message: "All projects are already used in other workflows",
          type: "error",
        })
      );
      return;
    }

    if (newProject.length === availableProjects.length) {
      setNewProject([]);
    } else {
      setNewProject(availableProjects);
    }
  };

  // Update local state when prop changes
  useEffect(() => {
    setLocalTriggerType(triggerType);
  }, [triggerType]);

  // Add effect to sync keywords from parent
  useEffect(() => {
    if (keywords) {
      handleKeywordsChange(keywords);
    }
  }, [keywords]);

  return (
    <Paper
      elevation={4}
      sx={{
        position: "absolute",
        right: -320,
        top: 0,
        width: 300,
        maxHeight: "80vh",
        height: isOpen ? "auto" : "auto",
        backgroundColor: "white",
        border: `1px solid ${bgColors.gray2}`,
        borderRadius: 2,
        boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
        transition: "transform 0.3s ease",
        zIndex: 10,
        display: "flex",
        flexDirection: "column",
      }}
      className="nowheel nopan nodrag"
    >
      <Box
        sx={{
          position: "sticky",
          top: 0,
          backgroundColor: bgColors.green,
          zIndex: 1000,
          padding: "8px 16px",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          borderBottom: `1px solid ${bgColors.gray2}`,
          borderTopLeftRadius: 6,
          borderTopRightRadius: 6,
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            gap: 1,
            width: "100%",
          }}
        >
          <Typography variant="h6" color="white">
            Flow Start
          </Typography>
          <IconButton
            aria-label="close"
            sx={{
              color: `${bgColors.red}`,
              width: 48,
              height: 48,
              "& svg": {
                width: 40,
                height: 40,
                fill: `${bgColors.red1}`,
              },
            }}
            onClick={() => handleEdit()}
          >
            <CloseIconSvg />
          </IconButton>
        </Box>
      </Box>
      <Box
        sx={{
          flexGrow: 1,
          padding: 2,
          maxHeight: "calc(80vh - 60px)",
        }}
      >
        <FormControl fullWidth sx={{ mb: 2 }}>
          <Typography variant="h6" sx={{ mb: 1 }} gutterBottom>
            Select the mode of Flow
          </Typography>
          <Select
            value={localTriggerType}
            onChange={(e) => handleTriggerTypeSelect(e.target.value)}
            sx={{
              "& .MuiSelect-select": {
                backgroundColor: "white",
                borderRadius: 1,
              },
            }}
          >
            <MenuItem value="keywords">Keywords</MenuItem>
            <MenuItem value="newLead">New Lead</MenuItem>
            <MenuItem value="statusChange">Lead Status</MenuItem>
            <MenuItem value="leadProject">Lead Project</MenuItem>
          </Select>
        </FormControl>

        <Divider sx={{ borderColor: "rgba(255,255,255,0.2)" }} />

        {localTriggerType === "keywords" ? (
          <Box>
            <Typography variant="h6" gutterBottom>
              Keywords
            </Typography>
            <Typography
              variant="subtitle2"
              gutterBottom
              sx={{ color: "#666", mb: 1, fontSize: "14px" }}
            >
              Enter up to 5 keywords that will trigger this flow
            </Typography>
            <Typography
              variant="caption"
              sx={{
                color: "#ff9800",
                display: "block",
                mb: 1,
                fontSize: "12px",
                fontStyle: "italic",
              }}
            >
              Note: Keywords are saved immediately and will persist even if you
              cancel the workflow
            </Typography>
            <Box sx={{ display: "flex", gap: 1 }}>
              <TextField
                fullWidth
                size="small"
                variant="outlined"
                placeholder="Add keyword"
                value={newKeyword}
                onChange={(e) => {
                  setNewKeyword(e.target.value);
                  setError("");
                }}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    e.preventDefault();
                    handleAddKeyword(
                      e as unknown as React.MouseEvent<HTMLButtonElement>
                    );
                  }
                }}
                error={!!error}
                helperText={
                  error ? (
                    <span style={{ whiteSpace: "nowrap" }}>{error}</span>
                  ) : null
                }
                sx={{ bgcolor: "white", borderRadius: 1, mb: 1 }}
              />
              <Button
                variant="contained"
                color="primary"
                onClick={handleAddKeyword}
                disabled={!newKeyword?.trim() || keywords.length >= 5}
                sx={{
                  mb: 2,
                  bgcolor: bgColors.green,
                  color: "white",
                  height: "40px",
                }}
              >
                {isLoading ? (
                  <CircularProgress size={20} color="primary" />
                ) : (
                  "Add"
                )}
              </Button>
            </Box>
            <Box display="flex" flexWrap="wrap" gap={1}>
              {keywords?.map((kw, idx) => (
                <Chip
                  key={idx}
                  label={kw}
                  onDelete={() => handleRemoveKeyword(idx)}
                  size="small"
                  color="secondary"
                  sx={{
                    bgcolor: bgColors.green1,
                    borderRadius: 3,
                    padding: "4px 10px",
                    fontSize: "14px",
                    color: "text.secondary",
                  }}
                />
              ))}
            </Box>
          </Box>
        ) : localTriggerType === "newLead" ? (
          <Box sx={{ position: "relative" }} ref={dropdownRef}>
            {/* Main trigger button */}
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                width: "100%",
                fontSize: "0.875rem",
                border: "1px solid #ccc",
                borderRadius: "6px",
                backgroundColor: "#fff",
                cursor: "pointer",
              }}
              onClick={handleToggle}
            >
              <Box
                sx={{
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                  flex: 1,
                  padding: "12px",
                }}
              >
                {getDisplayText()}
              </Box>
              <Box sx={{ marginLeft: "8px" }}>
                {isOpen ? (
                  <ExpandLess style={{ height: 16, width: 16 }} />
                ) : (
                  <ExpandMore style={{ height: 16, width: 16 }} />
                )}
              </Box>
            </Box>
            {/* Dropdown container */}
            {isOpen && (
              <Box
                sx={{
                  position: "relative",
                  zIndex: 10,
                  backgroundColor: "white",
                  border: `1px solid ${bgColors.gray2}`,
                  borderRadius: "6px",
                  boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
                  width: "100%",
                  maxHeight: "300px",
                  overflowY: "auto",
                }}
              >
                {sources.map((src: any, index: number) => {
                  const filteredSubSources = (src.subSources || []).filter(
                    (sub: string) => sub !== src.source
                  );
                  const hasSubSources = filteredSubSources.length > 0;
                  const isPartiallySelected =
                    hasSubSources &&
                    newLead[src.source] &&
                    newLead[src.source].length > 0 &&
                    newLead[src.source].length < filteredSubSources.length;
                  return (
                    <Box key={index}>
                      {/* Source row */}
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "space-between",
                          padding: "12px",
                          cursor: "pointer",
                          backgroundColor: "#f5f5f5",
                          "&:hover": { backgroundColor: "#e8e8e8" },
                          borderBottom: "1px solid #eee",
                        }}
                        onClick={() => {
                          if (!hasSubSources) {
                            handleSourceCheckboxToggle(src.source, []);
                          } else {
                            setActiveSourceId(
                              activeSourceId === src.source ? null : src.source
                            );
                          }
                        }}
                      >
                        <Box
                          sx={{ display: "flex", alignItems: "center", gap: 1 }}
                        >
                          <Box
                            onClick={(e) => {
                              e.stopPropagation();
                              handleSourceCheckboxToggle(
                                src.source,
                                filteredSubSources
                              );
                            }}
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                              width: 20,
                              height: 20,
                              border: "1px solid",
                              borderRadius: "4px",
                              cursor: "pointer",
                              backgroundColor:
                                isSourceFullySelected(
                                  src.source,
                                  filteredSubSources
                                ) &&
                                newLead[src.source] &&
                                newLead[src.source].length ===
                                  filteredSubSources.length
                                  ? bgColors.green
                                  : isPartiallySelected
                                  ? bgColors.green2
                                  : !!newLead[src.source]
                                  ? bgColors.green
                                  : "transparent",
                              borderColor:
                                isSourceFullySelected(
                                  src.source,
                                  filteredSubSources
                                ) &&
                                newLead[src.source] &&
                                newLead[src.source].length ===
                                  filteredSubSources.length
                                  ? bgColors.green
                                  : "grey.300",
                            }}
                          >
                            {(hasSubSources
                              ? isSourceFullySelected(
                                  src.source,
                                  filteredSubSources
                                ) &&
                                newLead[src.source] &&
                                newLead[src.source].length ===
                                  filteredSubSources.length
                              : !!newLead[src.source]) && (
                              <CheckIcon
                                sx={{ width: 12, height: 12, color: "#fff" }}
                              />
                            )}
                          </Box>
                          <Typography
                            component={"span"}
                            variant="body2"
                            sx={{
                              fontWeight: 600,
                              color: "#333",
                              fontSize: "0.9rem",
                            }}
                          >
                            {src.source}
                          </Typography>
                        </Box>
                        {hasSubSources &&
                          (activeSourceId === src.source ? (
                            <ExpandLess sx={{ width: 16, height: 16 }} />
                          ) : (
                            <ExpandMore sx={{ width: 16, height: 16 }} />
                          ))}
                      </Box>
                      {/* Subsources dropdown */}
                      {hasSubSources && activeSourceId === src.source && (
                        <Box>
                          {filteredSubSources.map(
                            (subSource: string, index: number) => (
                              <Box
                                key={index}
                                sx={{
                                  p: 1,
                                  display: "flex",
                                  alignItems: "center",
                                  borderRadius: 1,
                                  cursor: "pointer",
                                  pl: 3,
                                  backgroundColor: "#fafafa",
                                  "&:hover": { backgroundColor: "#f5f5f5" },
                                }}
                                onClick={() =>
                                  handleSubSourceToggle(src.source, subSource)
                                }
                              >
                                <Box
                                  sx={{
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "center",
                                    width: 20,
                                    height: 20,
                                    border: "1px solid",
                                    borderColor: isSubSourceSelected(
                                      src.source,
                                      subSource
                                    )
                                      ? bgColors.green
                                      : "grey.300",
                                    borderRadius: "4px",
                                    marginRight: "8px",
                                    backgroundColor: isSubSourceSelected(
                                      src.source,
                                      subSource
                                    )
                                      ? bgColors.green
                                      : "transparent",
                                  }}
                                >
                                  {isSubSourceSelected(
                                    src.source,
                                    subSource
                                  ) && (
                                    <CheckIcon
                                      sx={{
                                        width: 12,
                                        height: 12,
                                        color: "#fff",
                                      }}
                                    />
                                  )}
                                </Box>
                                <Typography component={"span"} variant="body2">
                                  {subSource}
                                </Typography>
                              </Box>
                            )
                          )}
                        </Box>
                      )}
                    </Box>
                  );
                })}
              </Box>
            )}
          </Box>
        ) : localTriggerType === "statusChange" ? (
          <Box sx={{ position: "relative" }} ref={dropdownRef}>
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                width: "100%",
                fontSize: "0.875rem",
                border: "1px solid #ccc",
                borderRadius: "6px",
                backgroundColor: "#fff",
                cursor: "pointer",
              }}
              onClick={handleStatusToggle}
            >
              <Box
                sx={{
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                  flex: 1,
                  padding: "12px",
                }}
              >
                {getStatusDisplayText()}
              </Box>
              <Box sx={{ marginLeft: "8px" }}>
                {isStatusOpen ? (
                  <ExpandLess style={{ height: 16, width: 16 }} />
                ) : (
                  <ExpandMore style={{ height: 16, width: 16 }} />
                )}
              </Box>
            </Box>
            {isStatusOpen && (
              <Box
                sx={{
                  position: "relative",
                  zIndex: 10,
                  backgroundColor: "white",
                  border: `1px solid ${bgColors.gray2}`,
                  borderRadius: "6px",
                  boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
                  width: "100%",
                  maxHeight: "300px",
                  overflowY: "auto",
                }}
              >
                {statusWithSubStatus?.map((status: any, index: number) => {
                  const statusId = status.status;
                  const filteredSubStatus = (status.subStatus || []).filter(
                    (sub: string) => sub !== status.status
                  );
                  const hasSubStatus = filteredSubStatus.length > 0;
                  const isPartiallySelected =
                    hasSubStatus &&
                    newStatusWithSubStatus[statusId] &&
                    newStatusWithSubStatus[statusId].length > 0 &&
                    newStatusWithSubStatus[statusId].length <
                      filteredSubStatus.length;
                  return (
                    <Box key={index}>
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "space-between",
                          padding: "12px",
                          cursor: "pointer",
                          backgroundColor: "#f5f5f5",
                          "&:hover": { backgroundColor: "#e8e8e8" },
                          borderBottom: "1px solid #eee",
                        }}
                        onClick={() => {
                          if (!hasSubStatus) {
                            handleStatusCheckboxToggle(statusId, []);
                          } else {
                            setActiveStatusId(
                              activeStatusId === statusId ? null : statusId
                            );
                          }
                        }}
                      >
                        <Box
                          sx={{ display: "flex", alignItems: "center", gap: 1 }}
                        >
                          <Box
                            onClick={(e) => {
                              e.stopPropagation();
                              handleStatusCheckboxToggle(
                                statusId,
                                filteredSubStatus
                              );
                            }}
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                              width: 20,
                              height: 20,
                              border: "1px solid",
                              borderRadius: "4px",
                              cursor: "pointer",
                              backgroundColor:
                                isStatusFullySelected(
                                  statusId,
                                  filteredSubStatus
                                ) &&
                                newStatusWithSubStatus[statusId] &&
                                newStatusWithSubStatus[statusId].length ===
                                  filteredSubStatus.length
                                  ? bgColors.green
                                  : isPartiallySelected
                                  ? bgColors.green2
                                  : !!newStatusWithSubStatus[statusId]
                                  ? bgColors.green
                                  : "transparent",
                              borderColor:
                                isStatusFullySelected(
                                  statusId,
                                  filteredSubStatus
                                ) &&
                                newStatusWithSubStatus[statusId] &&
                                newStatusWithSubStatus[statusId].length ===
                                  filteredSubStatus.length
                                  ? bgColors.green
                                  : "grey.300",
                            }}
                          >
                            {(hasSubStatus
                              ? isStatusFullySelected(
                                  statusId,
                                  filteredSubStatus
                                ) &&
                                newStatusWithSubStatus[statusId] &&
                                newStatusWithSubStatus[statusId].length ===
                                  filteredSubStatus.length
                              : !!newStatusWithSubStatus[statusId]) && (
                              <CheckIcon
                                sx={{
                                  width: 12,
                                  height: 12,
                                  color: "#fff",
                                }}
                              />
                            )}
                          </Box>
                          <Typography
                            component={"span"}
                            variant="body2"
                            sx={{
                              fontWeight: 600,
                              color: "#333",
                              fontSize: "0.9rem",
                            }}
                          >
                            {status.status}
                          </Typography>
                        </Box>
                        {hasSubStatus &&
                          (activeStatusId === statusId ? (
                            <ExpandLess sx={{ width: 16, height: 16 }} />
                          ) : (
                            <ExpandMore sx={{ width: 16, height: 16 }} />
                          ))}
                      </Box>
                      {hasSubStatus && activeStatusId === statusId && (
                        <Box>
                          {filteredSubStatus.map(
                            (subStatus: string, index: number) => (
                              <Box
                                key={index}
                                sx={{
                                  p: 1,
                                  display: "flex",
                                  alignItems: "center",
                                  borderRadius: 1,
                                  cursor: "pointer",
                                  pl: 3,
                                  backgroundColor: "#fafafa",
                                  "&:hover": { backgroundColor: "#f5f5f5" },
                                }}
                                onClick={() =>
                                  handleSubStatusToggle(statusId, subStatus)
                                }
                              >
                                <Box
                                  sx={{
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "center",
                                    width: 20,
                                    height: 20,
                                    border: "1px solid",
                                    borderColor: isSubStatusSelected(
                                      statusId,
                                      subStatus
                                    )
                                      ? bgColors.green
                                      : "grey.300",
                                    borderRadius: "4px",
                                    marginRight: "8px",
                                    backgroundColor: isSubStatusSelected(
                                      statusId,
                                      subStatus
                                    )
                                      ? bgColors.green
                                      : "transparent",
                                  }}
                                >
                                  {isSubStatusSelected(statusId, subStatus) && (
                                    <CheckIcon
                                      sx={{
                                        width: 12,
                                        height: 12,
                                        color: "#fff",
                                      }}
                                    />
                                  )}
                                </Box>
                                <Typography component={"span"} variant="body2">
                                  {subStatus}
                                </Typography>
                              </Box>
                            )
                          )}
                        </Box>
                      )}
                    </Box>
                  );
                })}
              </Box>
            )}
          </Box>
        ) : localTriggerType === "leadProject" ? (
          <Box sx={{ position: "relative" }} ref={dropdownRef}>
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                width: "100%",
                fontSize: "0.875rem",
                border: "1px solid #ccc",
                borderRadius: "6px",
                backgroundColor: "#fff",
                cursor: "pointer",
              }}
              onClick={handleProjectToggle}
            >
              <Box
                sx={{
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                  flex: 1,
                  padding: "12px",
                }}
              >
                {getProjectDisplayText()}
              </Box>
              <Box sx={{ marginLeft: "8px" }}>
                {isProjectOpen ? (
                  <ExpandLess style={{ height: 16, width: 16 }} />
                ) : (
                  <ExpandMore style={{ height: 16, width: 16 }} />
                )}
              </Box>
            </Box>
            {isProjectOpen && (
              <Box
                sx={{
                  position: "relative",
                  zIndex: 10,
                  backgroundColor: "white",
                  border: `1px solid ${bgColors.gray2}`,
                  borderRadius: "6px",
                  boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
                  width: "100%",
                  maxHeight: "300px",
                  overflowY: "auto",
                }}
              >
                {/* Select All Projects Option */}
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                    padding: "12px",
                    cursor: "pointer",
                    borderBottom: "1px solid #eee",
                    backgroundColor: "#f5f5f5",
                    "&:hover": { backgroundColor: "#e8e8e8" },
                  }}
                  onClick={handleSelectAllProjects}
                >
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      gap: 1,
                    }}
                  >
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        width: 20,
                        height: 20,
                        border: "1px solid",
                        borderRadius: "4px",
                        cursor: "pointer",
                        backgroundColor:
                          newProject.length === projectData.length
                            ? bgColors.green
                            : "transparent",
                        borderColor:
                          newProject.length === projectData.length
                            ? bgColors.green
                            : "grey.300",
                      }}
                    >
                      {newProject.length === projectData.length && (
                        <CheckIcon
                          sx={{ width: 12, height: 12, color: "#fff" }}
                        />
                      )}
                    </Box>
                    <Typography
                      component={"span"}
                      variant="body2"
                      sx={{
                        fontWeight: 600,
                        color: "#333",
                        fontSize: "0.9rem",
                      }}
                    >
                      Select All Projects
                    </Typography>
                  </Box>
                </Box>
                <Box sx={{ mt: 1 }} />
                {projectData?.map((proj: any, index: number) => {
                  const isSelected = newProject.includes(proj);
                  return (
                    <Box
                      key={proj}
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                        padding: "12px",
                        cursor: "pointer",
                        "&:hover": { backgroundColor: "#f8fafc" },
                      }}
                      onClick={() => handleProjectCheckboxToggle(proj)}
                    >
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          gap: 1,
                        }}
                      >
                        <Box
                          sx={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            width: 20,
                            height: 20,
                            border: "1px solid",
                            borderRadius: "4px",
                            cursor: "pointer",
                            backgroundColor: isSelected
                              ? bgColors.green
                              : "transparent",
                            borderColor: isSelected
                              ? bgColors.green
                              : "grey.300",
                          }}
                        >
                          {isSelected && (
                            <CheckIcon
                              sx={{ width: 12, height: 12, color: "#fff" }}
                            />
                          )}
                        </Box>
                        <Typography component={"span"} variant="body2">
                          {proj}
                        </Typography>
                      </Box>
                    </Box>
                  );
                })}
              </Box>
            )}
          </Box>
        ) : (
          <Box>
            <Typography variant="h6" gutterBottom>
              nothing selected
            </Typography>
          </Box>
        )}
      </Box>
    </Paper>
  );
};

export default MessageEditorPanelFlowStart;
