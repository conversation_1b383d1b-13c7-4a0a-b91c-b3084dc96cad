import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { INBOX_APIS } from "../../../Apis/Inbox/InboxApis";

export interface IData {
  status: "loading" | "succeeded" | "failed" | "idle";
  data: any;
}

const initialState: IData = {
  status: "idle",
  data: null,
};

export const getAllBlockedContacts = createAsyncThunk(
  "getAllBlockedContacts",
  async (data: any) => {
    const response = await INBOX_APIS.fetchBlockedContacts(data);
    return response?.data;
  }
);

export const GetAllBlockedContactsSlice = createSlice({
  name: "GetAllBlockedContactsSlice",
  initialState,
  reducers: {
    setData: (state, action) => {
      state.data = action.payload;
    },
  },
  extraReducers(builder) {
    builder
      .addCase(getAllBlockedContacts.pending, (state) => {
        state.status = "loading";
        //   state.error = ""
        state.data = [];
      })
      .addCase(getAllBlockedContacts.fulfilled, (state, action) => {
        state.status = "succeeded";
        //   state.error = ""
        state.data = action.payload;
      })
      .addCase(getAllBlockedContacts.rejected, (state) => {
        state.status = "failed";
        state.data = [];
        //   state.error = action.error.message || "";
      });
  },
});

export const getAllBlockedContactsActions = GetAllBlockedContactsSlice.actions;
export default GetAllBlockedContactsSlice.reducer;
