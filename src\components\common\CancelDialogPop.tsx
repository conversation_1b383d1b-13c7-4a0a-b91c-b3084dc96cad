import {
  <PERSON>,
  Button,
  Dialog,
  DialogContent,
  DialogTitle,
  IconButton,
  Typography,
} from "@mui/material";
import { makeStyles } from "@mui/styles";
import { bgColors } from "../../utils/bgColors";
import LoadingComponent from "./LoadingComponent";
import CloseSvg from "../../assets/svgs/CloseSvg";

const useStyles = makeStyles({
  signOutButtonStyles: {
    backgroundColor: "#3C3C3C",
    color: "#ffffff",
    height: "40px",
    borderRadius: "8px",
    width: "190%",
    padding: "8px",
    fontWeight: "600",
    cursor: "pointer",
  },
  cancelButtonStyles: {
    backgroundColor: "#ffffff",
    color: "#000000",
    height: "40px",
    borderRadius: "8px",
    border: `1px solid ${bgColors.gray3}`,
    width: "180%",
    padding: "8px",
    fontWeight: "600",
    cursor: "pointer",
  },
  grayColor: {
    color: `${bgColors.black1} !important`,
    opacity: "60% !important",
    fontWeight: "600 !important",
  },
});

const CancelDialogPopup = ({
  open,
  handleSave,
  handleClose,
  handleDiscardChanges,
  title,
  handleLoad,
}: any) => {
  const classes = useStyles();
  return (
    <Dialog
      open={open}
      onClose={handleClose}
      PaperProps={{
        style: { minWidth: "18%", borderRadius: "20px", padding: "0px 40px" },
      }}
      disableEscapeKeyDown
      disablePortal
    >
      <IconButton
        onClick={handleClose}
        sx={{ position: "absolute", right: 8, top: 8 }}
      >
        <CloseSvg />
      </IconButton>
      <DialogTitle>
        <Box>
          <Typography textAlign="center" variant="h6">
            Are you sure?
          </Typography>
        </Box>
      </DialogTitle>
      <DialogContent>
        <Box>
          <Box textAlign="center">
            <Typography variant="body2" className={classes.grayColor}>
              Do you really want to disgard the changes for <br />
              <Typography component={"span"} fontWeight={"bold"}>
                {title?.toLowerCase()}
              </Typography>
              ?
            </Typography>
          </Box>
        </Box>
      </DialogContent>

      <Box
        mb={3}
        display="flex"
        flexDirection="row"
        gap={2}
        justifyContent={"center"}
      >
        {handleLoad ? (
          <LoadingComponent height="auto" color={bgColors?.blue} />
        ) : (
          <>
            <Button
              variant="contained"
              size="small"
              onClick={handleDiscardChanges}
              sx={{
                textTransform: "none",
                backgroundColor: "#e5e7eb",
                "&:hover": { backgroundColor: "#d1d5db" },
                width: { xs: "100%", md: "auto" },
                whiteSpace: "nowrap",
                color: "#374151",
              }}
            >
              Discard
            </Button>
            <Button
              variant="contained"
              onClick={handleSave}
              size="small"
              sx={{
                minWidth: "70px",
                fontSize: { xs: "12px", sm: "11px", md: "12px" },
                backgroundColor: bgColors.green,
              }}
            >
              Save
            </Button>
          </>
        )}
      </Box>
    </Dialog>
  );
};

export default CancelDialogPopup;
