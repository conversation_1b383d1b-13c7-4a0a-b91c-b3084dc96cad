import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { CAMPAIGN_API } from "../../../Apis/Campaign/Campaign";


export interface IData {
    status: "loading" | "succeeded" | "failed" | "idle";
    data: any;
  }

  const initialState: IData = {
    status: "idle",
    data: null,
  };

  export const getCampaignById= createAsyncThunk(
    "getCampaignById",
    async (data: any) => {
      const response = await CAMPAIGN_API.getCampaignByDetails(data);
      return response?.data;
    }
  );


  export const getCampaignByIdSlice = createSlice({
    name: "getCampaignByIdSlice",
    initialState,
    reducers: {},
    extraReducers: (builder) => {
      builder
        .addCase(getCampaignById.pending, (state) => {
          state.status = "loading";
        })
        .addCase(getCampaignById.fulfilled, (state, action) => {
          state.status = "succeeded";
          state.data = action.payload;
        })
        .addCase(getCampaignById.rejected, (state) => {
          state.status = "failed";
        });
    },
  });

  export const GetSceduledCampaignActions = getCampaignByIdSlice.actions
  export default getCampaignByIdSlice.reducer
