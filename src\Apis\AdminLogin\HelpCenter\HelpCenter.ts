/* global process */

import axios from "axios";
import { getStoredTokens } from "../../../utils/authUtils";

const getAuthHeader = () => {
  const tokens = getStoredTokens();
  return tokens?.token ? `Bearer ${tokens?.token}` : "";
};

const USER_API_URL = process.env.REACT_APP_BASE_URL;

const getHelpCenterData = () => {
  return axios({
    url: `${USER_API_URL}/HelpCenter/get-all-faq's`,

    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
  });
};

const postHelpCenterData = (data: any) => {
  return axios({
    url: `${USER_API_URL}/HelpCenter/create-helpcenter`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    // data: JSON.stringify({ data: data }),
    data: JSON.stringify(data),
  });
};

export const HELP_CENTER_APIS = {
  getHelpCenterData,
  postHelpCenterData,
};
