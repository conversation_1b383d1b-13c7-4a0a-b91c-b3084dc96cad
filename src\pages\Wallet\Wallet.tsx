/* global process */

import {
  Box,
  Grid,
  Tooltip,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  IconButton,
  Button,
} from "@mui/material";
import { makeStyles } from "@mui/styles";
import React, { useEffect, useState } from "react";
import { bgColors } from "../../utils/bgColors";
import { TabContext, TabList, TabPanel } from "@mui/lab";
import BalanceIconSvg from "../../assets/svgs/BalanceIconSvg";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import FileDownloadOutlinedIcon from "@mui/icons-material/FileDownloadOutlined";
import { useAppDispatch, useAppSelector } from "../../utils/redux-hooks";
import { toastActions } from "../../utils/toastSlice";
import {
  billingDetails,
  getExpectedWalletDetails,
  getWalletDetails,
  walletInvoice,
  walletTransactions,
} from "../../redux/slices/Wallet/WalletSlice";
import LoadingComponent from "../../components/common/LoadingComponent";
import { format, parseISO } from "date-fns";
import {
  fetchCreateOrder,
  fetchSubscriptionDeductions,
  fetchVerifyPaymentOrder,
  subscriptionInvoice,
} from "../../redux/slices/Subscription/SubscriptionSlice";
import AddWalletBalancePopup from "../../components/WalletComponents/PaymentPopover";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import AddIcon from "@mui/icons-material/Add";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import DateRangePicker from "../../components/WalletComponents/DatePickerComponent";
import PaymentsIcon from "@mui/icons-material/Payments";
import TransactionHistory from "../../components/WalletComponents/WalletTransactionsPopup";

import { Currency } from "../../utils/enums";
import CommonTable, { TableColumn } from "../../components/common/CommonTable";
import CommonHeader from "../../components/common/CommonHeader";
import CommonButton from "../../components/common/CommonButton";

const useStyles = makeStyles(() => ({
  mainContainer: {
    backgroundColor: bgColors.white1,
    height: "100vh",
    width: "100%",
  },
  bgContainer: {
    backgroundColor: bgColors.white,
  },
  headerContainer: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    borderBottom: "1px solid #f0f0f0",
    width: "100%",
    paddingLeft: "24px",
    paddingRight: "24px",
    paddingTop: "8px",
    paddingBottom: "8px",
  },
  planButton: {
    backgroundColor: bgColors.green1,
    color: bgColors.green,
    fontWeight: "700",
    padding: "6px 22px",
    borderRadius: 25,
  },
  blackColor: {
    // color: `${bgColors.black1} !important`,
    // fontWeight: "600 !important",
  },
  grayColor: {
    fontWeight: "600 !important",
    opacity: "60%",
  },
  updateButtonStyles: {
    color: bgColors.green,
    border: `1px solid ${bgColors.green}`,
    height: "32px",
    borderRadius: "8px",
    fontWeight: "20px",
    cursor: "pointer",
    display: "flex",
    alignItems: "center",
    gap: "10px",
  },
  makePayment: {
    color: "#3C3C3C !important",
    fontWeight: "600 !important",
    border: `2px solid ${bgColors.black1}`,
    padding: "10px 40px",
    borderRadius: "14px",
    background: "#3C3C3C1A",
  },
  fileContainer: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    borderBottom: "1px solid #838383",
  },
  fileNameContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  },
  popoverContent: {
    padding: "18px",
    display: "flex",
    flexDirection: "column",
  },
  subscriptionContainer: {
    border: `2px solid ${bgColors.gray5}`,
    borderRadius: "18px",
    padding: "10px",
  },
  justifySpaceBetween: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    gap: "100px",
  },
  flexRow: {
    display: "flex",
    flexDirection: "row",
  },
  RsStyles: {
    fontSize: "16px",
    fontWeight: "500",
    fontFamily: "inter",
    color: "gray",
  },
  planAmout: {
    fontSize: "33px",
    margin: 0,
  },
  alignCenter: {
    display: "flex",
    alignItems: "center",
  },
  tabsContainer: {
    width: "100%",
  },
  account: {
    display: "flex",
    flexDirection: "column",
    justifyContent: "space-between",
    padding: "5px",
  },
  switch: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    fontSize: "18px !important",
    color: `${bgColors.black1} !important`,
  },
  lineStyles: {
    border: "none",
    borderTop: `1px solid ${bgColors.gray2}`,
    width: "100%",
    margin: "0 auto",
  },
  table: {
    borderCollapse: "separate",
    minWidth: "500px",
    overflow: "auto",
    borderSpacing: "0 8px",
    borderColor: "lightgray",
    "& th, & td": {
      padding: "3px",
      borderBottom: "1px solid #f0f0f0",
    },
    "& th:last-child, & td:last-child": {},
  },
  teamProfileContainer: {
    display: "flex",
    alignItems: "flex-start",
    flexDirection: "column",
    justifyContent: "flex-start",
  },
  threedots: {
    border: "2px solid #F2F2F2",
    padding: "10px",
    borderRadius: "12px",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    cursor: "pointer",
  },
  editButtonContainer: {
    border: "2px solid #DBDBDB",
    padding: "8px",
    borderRadius: "12px",
    backgroundColor: "#F4F4F4",
    width: "50px",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    cursor: "pointer",
  },
}));

const capitalizeFirstLetter = (string: any) => {
  return string.charAt(0).toUpperCase() + string.slice(1);
};

const Wallet = () => {
  const classes = useStyles();

  const dispatch = useAppDispatch();

  const loginData = useAppSelector((state: any) => state.adminLogin.data);
  const isPlatform = loginData?.isPlatform;
  const companyId = loginData?.companyId;

  // const userData = localStorage.getItem();

  const accountDetails = useAppSelector((state: any) => state.accountData);
  const [upiNumberError, setUpiNumberError] = useState("");

  const isVerifiedWithMeta = accountDetails?.data?.companyVerificationStatus;

  const [currentUpiPhoneNumber, setCurrentUpiPhoneNumber] = useState(
    accountDetails?.data?.phoneNumber || ""
  );
  const transactionData = useAppSelector(
    (state: any) => state?.wallet?.transactionData?.data?.transactions
  );

  const expectedAmountDetails = useAppSelector(
    (state: any) => state?.wallet?.expectedAmount?.data
  );
  const expectedWalletstatus = useAppSelector(
    (state: any) => state?.wallet?.expectedWalletstatus
  );

  const walletDetails = useAppSelector(
    (state: any) => state?.wallet?.amount?.data?.wallet
  );

  const [expectedAmount, setExpectedAmount] = useState(
    expectedAmountDetails ? expectedAmountDetails?.expectedWalletBalance : 0
  );
  const [totalAmount, setTotalAmount] = useState(
    expectedAmountDetails ? expectedAmountDetails?.walletBalance : 0
  );

  const walletTransactionsHistory = useAppSelector(
    (state: any) => state?.wallet?.transactionData?.data?.transactionsHistory
  );
  const subscriptionData = useAppSelector(
    (state: any) => state?.Subscription?.subscriptionDeductions?.data
  );
  const transactionDataStatus = useAppSelector(
    (state: any) => state?.wallet?.transactionDataStatus
  );
  const subscriptionDataStatus = useAppSelector(
    (state: any) => state?.Subscription?.subscriptionDeductionsStatus
  );
  const paymentStatus = useAppSelector((state: any) => state?.wallet?.isStatus);
  const getuserPermissionData = useAppSelector(
    (state: any) => state.getUserPermissions?.data
  );
  const walletPermissionsArray = getuserPermissionData?.wallet;
  const walletTransactionsPermissionsObject = walletPermissionsArray?.find(
    (item: any) =>
      Object.prototype.hasOwnProperty.call(item, "walletTransactions")
  );

  const subscriptionTransactionsPermissionsObject =
    walletPermissionsArray?.find((item: any) =>
      Object.prototype.hasOwnProperty.call(item, "subscriptionDeductions")
    );

  const walletTransactionsPermissoinsActions =
    walletTransactionsPermissionsObject
      ? walletTransactionsPermissionsObject.walletTransactions
      : [];

  const subscriptionTransactionsPermissoinsActions =
    subscriptionTransactionsPermissionsObject
      ? subscriptionTransactionsPermissionsObject.subscriptionDeductions
      : [];

  const [walletInvoiceTooltipOpen, setWalletInvoiceTooltipOpen] = useState("");
  const [subscriptionInvoiceTooltipOpen, setSubscriptionInvoiceTooltipOpen] =
    useState("");
  const [subscriptionTooltipMessage, setSubscriptionTooltipMessage] =
    useState("");
  const [addBalanceTooltip, setAddBalanceTooltip] = useState(false);
  const [isMetaVerifiedToolTip, setIsMetaVerifiedToolTip] = useState(false);
  const [tooltipView, setTooltipView] = useState("");
  const [open, setOpen] = useState(false);

  // const [formData, setFormData] = useState<any>({
  //   selectedDate: getCurrentDate(),
  // });
  const [value, setValue] = React.useState("1");
  const [hoveredRow, setHoveredRow] = useState(null);
  const [startCalenderDate, setStartCalenderDate] = useState<any>(null);
  const [endCalenderDate, setEndCalenderDate] = useState<any>(null);
  const [isDateRangeApplied, setIsDateRangeApplied] = useState(false);
  const [transactionHistoryOpen, setTransactionHistoryOpen] = useState(false);

  // const handleDateChange = (event: any) => {
  //   const newDate = event.target.value;

  //   setFormData({ ...formData, selectedDate: newDate });
  // };

  const handleClose = () => {
    setOpen(false);
  };

  const handleChangeUpiPhoneNumber = (e: any) => {
    setCurrentUpiPhoneNumber(e.target.value);
    setUpiNumberError("");
  };

  const hasAccessToDownloadWalletTransactionInvoice = (permission: any) => {
    if (walletTransactionsPermissoinsActions?.includes(permission)) {
      return true;
    }
    return false;
  };

  const hasAccessToDownloadSubscriptionTransactionInvoice = (
    permission: any
  ) => {
    if (subscriptionTransactionsPermissoinsActions?.includes(permission)) {
      return true;
    }
    return false;
  };

  const hasWalletPermissions = (permissions: any) => {
    for (const walletItem of permissions) {
      if (
        Object.prototype.hasOwnProperty.call(walletItem, "walletTransactions")
      ) {
        return true;
      }
    }
    return false;
  };

  const hasSubscriptionPermissions = (permissions: any) => {
    for (const subscriptionItem of permissions) {
      if (
        Object.prototype.hasOwnProperty.call(
          subscriptionItem,
          "subscriptionDeductions"
        )
      ) {
        return true;
      }
    }
    return false;
  };

  const hasAddBalancePermissions = (permissions: any) => {
    for (const addBalanceItem of permissions) {
      if (Object.prototype.hasOwnProperty.call(addBalanceItem, "addBalance")) {
        return true;
      }
    }
    return false;
  };

  const getWalletPermissions = hasWalletPermissions(
    getuserPermissionData?.wallet || ""
  );
  const getSubscriptionPermissions = hasSubscriptionPermissions(
    getuserPermissionData?.wallet || ""
  );
  const getAddBalancePermissions = hasAddBalancePermissions(
    getuserPermissionData?.wallet || ""
  );
  const loadScript = (src: string) => {
    return new Promise((resolve) => {
      const script = document.createElement("script");
      script.src = src;
      script.onload = () => resolve(true);
      script.onerror = () => resolve(false);
      document.body.appendChild(script);
    });
  };

  async function getSessionId(data: any): Promise<
    | {
        session_Id: string;
        order_ID: string;
      }
    | undefined
  > {
    try {
      const res: any = await dispatch(fetchCreateOrder(data));

      if (res?.payload?.data) {
        // Return session_Id and order_ID
        return {
          session_Id: res.payload.data.paymentSessionId,
          order_ID: res.payload.data.orderId,
        };
      } else {
        throw new Error("Invalid response data");
      }
    } catch (err: any) {
      // Dispatch an error message using toastActions
      dispatch(
        toastActions.setToaster({
          type: "error",
          message: `${
            err.message || "An error occurred while fetching session ID"
          }`,
        })
      );

      return undefined; // Explicitly return undefined in case of an error
    }
  }

  const verifyPayment = async (order_Id: string | undefined) => {
    try {
      //do an api call and verify the payment by passing the orderId
      const response = await dispatch(fetchVerifyPaymentOrder({ order_Id }));

      if (!response?.payload?.success) {
        throw new Error("Order was not successfull");
      }
      const { balance, expectedWalletBallance } = response?.payload?.data;
      setExpectedAmount(expectedWalletBallance);
      setTotalAmount(balance);
      if (response?.payload?.success) {
        dispatch(
          toastActions.setToaster({
            type: "success",
            message: "Payment successful",
          })
        );
      }
    } catch (err) {
      dispatch(
        toastActions.setToaster({
          type: "error",
          message: `${err}`,
        })
      );
    }
  };

  const handleUpgrade = async (amount: any, totalAmount: any) => {
    if (currentUpiPhoneNumber.length !== 10) {
      setUpiNumberError("Please enter valid mobile Number");
      return;
    } else {
      setUpiNumberError("");
    }
    try {
      if (getAddBalancePermissions) {
        //@ts-ignore
        let cashfree = Cashfree({
          mode: "production",
        });
        let planId = amount;

        const amountInPaise = parseFloat(planId.toString()); // Convert to paise
        // const amountInPaise = parseFloat(planId.toString()).toFixed(1); // Convert to paise
        // const amountInPaise = parseFloat(planId); // Convert to paise and ensure it's a number
        // const formattedAmount = (amountInPaise / 100).toFixed(1); // Format to 70.00
        // const amountInPaise = parseFloat(planId.toString()); // Convert to paise
        const data = {
          amount: amountInPaise,
          totalAmount: totalAmount,
          currency: Currency.INR,
          planId: null,
          phoneNumber: currentUpiPhoneNumber,
          walletId: walletDetails?.walletId,
          discountId: null,
          type: null,
        };

        let session_Id: string | undefined;
        let order_ID: string | undefined;

        const sessionData = await getSessionId(data);

        if (sessionData) {
          ({ session_Id, order_ID } = sessionData); // Destructure and assign to already declared variables
        } else {
          console.error("Failed to get session data.");
        }

        let checkoutOptions = {
          paymentSessionId: session_Id,
          redirectTarget: "_modal",
        };

        cashfree.checkout(checkoutOptions).then((result: any) => {
          if (result.error) {
            verifyPayment(order_ID);
            handleClose();
          }

          if (result?.paymentDetails) {
            verifyPayment(order_ID);
            handleClose();
          }
        });
      } else {
        setAddBalanceTooltip(true);
        setTimeout(() => {
          setAddBalanceTooltip(false);
        }, 2000);
      }
    } catch (err) {
      dispatch(
        toastActions.setToaster({
          type: "error",
          message: `${err}`,
        })
      );
    }
  };

  const handleChange = (event: React.SyntheticEvent, newValue: string) => {
    setValue(newValue);
  };

  const handlePopoverOpen = (event: any, date: any) => {
    const hasPermission = hasAccessToDownloadWalletTransactionInvoice(
      "downloadWalletTransactionInvoice"
    );
    if (hasPermission) {
      if (event) {
        downloadInvoice(event, date);
      }
    } else {
      setWalletInvoiceTooltipOpen(event);
      setTimeout(() => {
        setWalletInvoiceTooltipOpen("");
      }, 2000);
    }
  };

  const SubscriptionPopoverOpen = (orderAmount: any, event: any, date: any) => {
    const hasPermission = hasAccessToDownloadSubscriptionTransactionInvoice(
      "downloadSubscriptionDeductionInvoice"
    );
    if (hasPermission) {
      if (event && orderAmount !== 0) {
        downloadSubscriptionInvoice(event, date);
      } else {
        setSubscriptionTooltipMessage("Not allowed for 0 amount");
        setSubscriptionInvoiceTooltipOpen(event);
      }
    } else {
      setSubscriptionInvoiceTooltipOpen(event);
      setTimeout(() => {
        setSubscriptionInvoiceTooltipOpen("");
      }, 2000);
    }
  };
  const formatDate = (dateString: any) => {
    const date = parseISO(dateString);
    return format(date, "hh : mm a dd MMM yyyy")
      .replace("AM", "am")
      .replace("PM", "pm");
  };

  // Function to get the start and end dates for the last 30 days
  const getLast30DaysDates = () => {
    const endDate = new Date(); // Today's date
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30); // Subtract 30 days from today

    const formatDate = (date: any) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0"); // Months are 0-based
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    };

    return {
      startDate: formatDate(startDate),
      endDate: formatDate(endDate),
    };
  };

  useEffect(() => {
    if (getSubscriptionPermissions && getWalletPermissions) {
      setValue("1");
    } else if (getWalletPermissions) {
      setValue("1");
    } else if (getSubscriptionPermissions) {
      setValue("2");
    }
  }, [getSubscriptionPermissions, getWalletPermissions]);

  useEffect(() => {
    const { startDate, endDate } = getLast30DaysDates();
    dispatch(getWalletDetails());
    dispatch(walletTransactions({ startDate: startDate, endDate: endDate }));
    dispatch(
      fetchSubscriptionDeductions()
      // { startDate: startDate, endDate: endDate }
    );
    dispatch(billingDetails(companyId));
    dispatch(getExpectedWalletDetails(companyId));
  }, [dispatch, loginData, expectedAmount]);
  const [isHovered, setIsHovered] = useState(false);

  const downloadInvoice = async (id: string, date: any) => {
    // Decode the base64 string
    const response = await dispatch(walletInvoice(id));
    const data = response.payload;
    if (data) {
      const byteCharacters = atob(data?.data);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      // Create a Blob from the byte array
      const blob = new Blob([byteArray], { type: "application/pdf" });
      // Create a URL for the Blob
      const url = URL.createObjectURL(blob);
      // Create a link element to trigger the download
      const link = document.createElement("a");
      link.href = url;
      link.download = `Invoice ${date}`;
      document.body.appendChild(link);
      link.click();
      // Clean up and remove the link

      // link.parentNode.removeChild(link);
      if (link.parentNode) {
        link.parentNode.removeChild(link);
      } else {
        console.warn("Link element has no parent node, unable to remove it.");
      }
      URL.revokeObjectURL(url);

      dispatch(
        toastActions.setToaster({
          type: "success",
          message: "Successfully downloaded",
        })
      );
    } else {
      dispatch(
        toastActions.setToaster({
          type: "error",
          message: "Unable to download",
        })
      );
    }
  };
  const downloadSubscriptionInvoice = async (id: string, date: any) => {
    // Decode the base64 string
    const response = await dispatch(subscriptionInvoice(id));
    const data = response.payload;
    if (data) {
      const byteCharacters = atob(data?.data?.fileContent);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      // Create a Blob from the byte array
      const blob = new Blob([byteArray], { type: "application/pdf" });
      // Create a URL for the Blob
      const url = URL.createObjectURL(blob);
      // Create a link element to trigger the download
      const link = document.createElement("a");
      link.href = url;
      link.download = `Invoice ${date}`;
      document.body.appendChild(link);
      link.click();
      // Clean up and remove the link

      // link.parentNode.removeChild(link);
      if (link.parentNode) {
        link.parentNode.removeChild(link);
      } else {
        console.warn("Link element has no parent node, unable to remove it.");
      }
      URL.revokeObjectURL(url);

      dispatch(
        toastActions.setToaster({
          type: "success",
          message: "Successfully downloaded",
        })
      );
    } else {
      dispatch(
        toastActions.setToaster({
          type: "error",
          message: "Unable to download",
        })
      );
    }
  };

  const handleDateRangeChange = ({ startDate, endDate }: any) => {
    setStartCalenderDate(startDate);
    setEndCalenderDate(endDate);
    setIsDateRangeApplied(false);
  };

  const handleApplyDateRange = () => {
    if (isDateRangeApplied) {
      const { startDate, endDate } = getLast30DaysDates();

      // Reset the date range

      setStartCalenderDate(null);
      setEndCalenderDate(null);
      setIsDateRangeApplied(false);

      dispatch(walletTransactions({ startDate: startDate, endDate: endDate }));
    } else {
      // Apply the date range
      setIsDateRangeApplied(true);

      dispatch(
        walletTransactions({
          startDate: startCalenderDate,
          endDate: endCalenderDate,
        })
      );
      // Call the API to fetch data within the selected date range
    }
  };

  const handleAddBalance = () => {
    if (isVerifiedWithMeta) {
      setOpen(true);
    } else {
      setIsMetaVerifiedToolTip(true);
      setTimeout(() => {
        setIsMetaVerifiedToolTip(false);
      }, 4000);
    }
  };

  const renderInvoiceColumn = (row: any) => {
    return walletInvoiceTooltipOpen === row?.id ? (
      <Tooltip
        title="Access Denied"
        placement="left"
        open={walletInvoiceTooltipOpen === row?.id}
        onClose={() => setWalletInvoiceTooltipOpen("")}
      >
        <Box>
          <IconButton>
            <FileDownloadOutlinedIcon />
          </IconButton>
        </Box>
      </Tooltip>
    ) : (
      <Tooltip title="Download" placement="bottom">
        <Box>
          <IconButton onClick={() => handlePopoverOpen(row.id, row.date)}>
            <FileDownloadOutlinedIcon />
          </IconButton>
        </Box>
      </Tooltip>
    );
  };

  const walletTransactionColumns: TableColumn[] = [
    {
      id: "date",
      label: "Date",
      width: "80px",
      format: (value: string) => (
        <Tooltip title={value} placement="top" arrow>
          <Box sx={{ display: "flex", alignItems: "center" }}>{value}</Box>
        </Tooltip>
      ),
      align: "left",
    },
    {
      id: "transactions",
      label: "Transactions",
      width: "200px",
      format: (value: string) => (
        <Tooltip title={value} placement="top" arrow>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              textOverflow: "ellipsis",
              overflow: "hidden",
              whiteSpace: "nowrap",
              cursor: "pointer",
            }}
          >
            {value}
          </Box>
        </Tooltip>
      ),
      align: "left",
    },
    {
      id: "amount",
      label: "Amount",
      width: "120px",
      format: (value: string) => (
        <Tooltip title={value} placement="top" arrow>
          <Box sx={{ display: "flex", alignItems: "center" }}>{value}</Box>
        </Tooltip>
      ),
      align: "left",
    },
  ];

  const subscriptionTransactionColumns: TableColumn[] = [
    {
      id: "date",
      label: "Date",
      width: "120px",
      format: (value: string) => (
        <Box sx={{ display: "flex", alignItems: "center" }}>{value}</Box>
      ),
    },
    {
      id: "transactions",
      label: "Transactions",
      width: "200px",
      format: (value: string) => (
        <Box sx={{ display: "flex", alignItems: "center" }}>{value}</Box>
      ),
    },
    {
      id: "amount",
      label: "Amount",
      width: "120px",
      format: (value: string) => (
        <Box sx={{ display: "flex", alignItems: "center" }}>{value}</Box>
      ),
    },
    {
      id: "status",
      label: "Status",
      width: "120px",
      format: (value: string) => (
        <Box sx={{ display: "flex", alignItems: "center" }}>{value}</Box>
      ),
    },
    {
      id: "invoice",
      label: "Invoice",
      width: "120px",
      format: (value: string) => (
        <Box sx={{ display: "flex", alignItems: "center" }}>{value}</Box>
      ),
    },
  ];

  const transformedTransactionData = transactionData?.map((item: any) => ({
    ...item,
    date: formatDate(item.endDate),
    transactions: item.description,
    amount: `-₹ ${item.cost}/ Updated balance ₹ ${item.currentBalance}`,
  }));

  const defaultSubscriptionRow = {
    date: "-",
    transactions: "Subscription Charges-renewal",
    amount: "-",
    status: "Unlimited",
    invoice: "-",
  };

  const transformedSubscriptionData = [
    defaultSubscriptionRow,
    ...(subscriptionData?.map((item: any) => ({
      ...item,
      date: formatDate(item.updatedAt),
      transactions: item.description,
      amount: `₹ ${parseFloat(item.orderAmount).toLocaleString("en-IN")}`,
      status: capitalizeFirstLetter(item.status),
      invoice: item.invoice,
    })) || []),
  ];

  const renderWalletTransactionOnMobile = (row: any) => {
    // Split date string into time and date
    // Example: "05 : 30 pm 21 May 2025"
    let time = "",
      date = "";
    if (row.date) {
      const match = row.date.match(/^(\d{2} ?: ?\d{2} ?[ap]m) (.+)$/i);
      if (match) {
        time = match[1];
        date = match[2];
      } else {
        date = row.date;
      }
    }
    return (
      <Box
        sx={{
          backgroundColor: "white",
          borderRadius: "12px",
          padding: "16px",
          margin: "8px 0",
          boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.05)",
          border: "1px solid #f0f0f0",
        }}
      >
        {/* Date & Time */}
        <Box sx={{ display: "flex", justifyContent: "space-between" }}>
          <Box sx={{ mb: 1 }}>
            <Typography
              variant="caption"
              sx={{ fontWeight: 600, color: "#757575", fontSize: "12px" }}
            >
              Date
            </Typography>
            <Typography
              variant="body2"
              sx={{ color: "#757575", fontSize: "13px", lineHeight: 1 }}
            >
              {time} {date}
            </Typography>
          </Box>
          {row.id && (
            <IconButton
              size="small"
              onClick={() => handlePopoverOpen(row.id, row.date)}
            >
              <FileDownloadOutlinedIcon fontSize="small" color="inherit" />
            </IconButton>
          )}
        </Box>
        {/* Amount */}
        <Box sx={{ mb: 1 }}>
          <Typography
            variant="caption"
            sx={{ fontWeight: 600, color: "#757575", fontSize: "12px" }}
          >
            Amount
          </Typography>
          <Typography
            variant="body1"
            sx={{
              fontWeight: 600,
              fontSize: "14px",
              color: "#212121",
              lineHeight: 1.2,
            }}
          >
            {row.amount}
          </Typography>
        </Box>
        {/* Transaction */}
        <Box>
          <Typography
            variant="caption"
            sx={{ fontWeight: 600, color: "#757575", fontSize: "12px" }}
          >
            Transaction
          </Typography>
          <Typography
            variant="body1"
            sx={{
              fontWeight: 500,
              fontSize: "14px",
              color: "#212121",
              mb: 1,
              wordBreak: "break-word",
              whiteSpace: "pre-line",
            }}
          >
            {row.transactions}
          </Typography>
        </Box>
      </Box>
    );
  };

  const renderSubscriptionTransactionOnMobile = (row: any) => {
    // Split date string into time and date
    let time = "",
      date = "";
    if (row.date) {
      const match = row.date.match(/^(\d{2} ?: ?\d{2} ?[ap]m) (.+)$/i);
      if (match) {
        time = match[1];
        date = match[2];
      } else {
        date = row.date;
      }
    }
    return (
      <Box
        sx={{
          backgroundColor: "white",
          borderRadius: "12px",
          padding: "16px",
          margin: "8px 0",
          boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.05)",
          border: "1px solid #f0f0f0",
        }}
      >
        {/* Date & Time */}
        <Box sx={{ mb: 1 }}>
          <Typography
            variant="caption"
            sx={{ fontWeight: 600, color: "#757575", fontSize: "12px" }}
          >
            Date
          </Typography>
          <Typography
            variant="body2"
            sx={{ color: "#757575", fontSize: "13px", lineHeight: 1 }}
          >
            {time}
          </Typography>
          <Typography
            variant="body2"
            sx={{ color: "#757575", fontSize: "13px", lineHeight: 1 }}
          >
            {date}
          </Typography>
        </Box>
        {/* Amount */}
        <Box sx={{ mb: 1 }}>
          <Typography
            variant="caption"
            sx={{ fontWeight: 600, color: "#757575", fontSize: "12px" }}
          >
            Amount
          </Typography>
          <Typography
            variant="body1"
            sx={{
              fontWeight: 600,
              fontSize: "14px",
              color: "#4caf50",
              lineHeight: 1.2,
            }}
          >
            {row.amount}
          </Typography>
        </Box>
        {/* Transaction */}
        <Box sx={{ mb: 1 }}>
          <Typography
            variant="caption"
            sx={{ fontWeight: 600, color: "#757575", fontSize: "12px" }}
          >
            Transaction
          </Typography>
          <Typography
            variant="body1"
            sx={{
              fontWeight: 500,
              fontSize: "14px",
              color: "#212121",
              mb: 1,
              wordBreak: "break-word",
              whiteSpace: "pre-line",
            }}
          >
            {row.transactions}
          </Typography>
        </Box>
        {/* Status & Invoice */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mt: 1,
          }}
        >
          <Box>
            <Typography
              variant="caption"
              sx={{ fontWeight: 600, color: "#757575", fontSize: "12px" }}
            >
              Status
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: row.status === "Success" ? "#4caf50" : "#ff9800",
                fontWeight: 500,
                fontSize: "13px",
                lineHeight: 1,
              }}
            >
              {row.status}
            </Typography>
          </Box>
        </Box>
      </Box>
    );
  };

  return (
    <Grid className={classes.mainContainer}>
      <Grid className={classes.bgContainer}>
        <Box className={classes.headerContainer}>
          <CommonHeader title="Wallet" />
        </Box>
        {transactionDataStatus !== "loading" ||
        expectedWalletstatus !== "loading" ? (
          <>
            <Box sx={{ flexGrow: 1 }} mx={2}>
              <Grid container spacing={2} sx={{ alignItems: "stretch" }}>
                {/* Left side - Wallet Balance */}
                <Grid item xs={12} md={12} sx={{ display: "flex" }}>
                  <Box
                    sx={{
                      mt: 2,
                      padding: 2,
                      borderRadius: 2,
                      border: "1px solid #e0e0e0",
                      flex: 1,
                      display: "flex",
                      flexDirection: "column",
                      justifyContent: "space-between",
                    }}
                  >
                    {paymentStatus === "loading" ? (
                      <LoadingComponent height="auto" color="#1976d2" />
                    ) : (
                      <Box>
                        <Box
                          sx={{
                            display: "flex",
                            justifyContent: "space-between",
                            flexDirection: { xs: "column", sm: "row" },
                            gap: { xs: 2, sm: 0 },
                            alignItems: "center",
                          }}
                        >
                          <Box
                            sx={{
                              display: "flex",
                              flexDirection: { xs: "column", sm: "row" },
                              justifyContent: "space-evenly",
                              gap: { xs: "25px", sm: "50px" },
                            }}
                          >
                            <Box
                              sx={{
                                display: "flex",
                                flexDirection: "column",
                                justifyContent: "space-evenly",
                              }}
                            >
                              <Typography
                                variant="body2"
                                // sx={{ color: "#757575" }}
                                className={classes.grayColor}
                              >
                                Available Balance
                              </Typography>
                              <Typography
                                sx={{
                                  fontSize: {
                                    xs: "1.5rem",
                                    sm: "2rem",
                                    md: "1.5rem",
                                  },

                                  mt: 1.5,
                                  display: "flex",
                                  alignItems: "center",
                                  gap: 1,
                                }}
                              >
                                <span className={classes.RsStyles}>Rs </span>
                                {totalAmount?.toLocaleString()}
                              </Typography>
                            </Box>
                            <Box
                              sx={{
                                display: "flex",
                                flexDirection: "column",
                                justifyContent: "space-evenly",
                              }}
                            >
                              <Typography
                                variant="body2"
                                // sx={{ color: "#757575" }}
                                className={classes.grayColor}
                              >
                                Expected Balance
                              </Typography>
                              <Typography
                                sx={{
                                  fontSize: {
                                    xs: "1.5rem",
                                    sm: "2rem",
                                    md: "1.5rem",
                                  },

                                  mt: 1.5,
                                  display: "flex",
                                  alignItems: "center",
                                  gap: 1,
                                }}
                              >
                                <span className={classes.RsStyles}>Rs </span>
                                {expectedAmount?.toLocaleString()}
                              </Typography>
                            </Box>
                          </Box>

                          <Box
                            ml={4}
                            mr={4}
                            // style={{ textAlign: "center" }}
                          >
                            <Tooltip
                              title="Your account has not been verified with meta.
                            Please link with meta to add wallet amount."
                              placement="top"
                              open={
                                isMetaVerifiedToolTip && tooltipView === "add"
                              }
                              onClose={() => setIsMetaVerifiedToolTip(false)}
                            >
                              <CommonButton
                                primaryAction={{
                                  label: "Add Balance",
                                  icon: <BalanceIconSvg />,
                                  onClick: () => {
                                    handleAddBalance();
                                    setTooltipView("add");
                                  },
                                }}
                              />
                            </Tooltip>

                            <AddWalletBalancePopup
                              open={open}
                              addBalanceTooltip={addBalanceTooltip}
                              currentUpiPhoneNumber={currentUpiPhoneNumber}
                              handleChangeUpiPhoneNumber={
                                handleChangeUpiPhoneNumber
                              }
                              upiNumberError={upiNumberError}
                              handleClose={handleClose}
                              handleUpgrade={handleUpgrade}
                            />
                          </Box>
                        </Box>
                      </Box>
                    )}
                  </Box>
                </Grid>
                <Typography
                  variant="body2"
                  sx={{ color: "#757575", mt: 1, ml: 2 }}
                >
                  <Box component="span" sx={{ color: "red" }}>
                    Note:
                  </Box>
                  The expected balance shows pending amount at the current time.
                  The available balance may update after all transactions have
                  been fully processed.
                </Typography>
                {/* Right side - Low Balance Message */}
                <Grid item xs={12} md={12} sx={{ display: "flex" }}>
                  {totalAmount >= 1000 && expectedWalletstatus !== "loading" ? (
                    <Box
                      sx={{
                        flex: 1,
                        display: "flex",
                        flexDirection: "column",
                        justifyContent: "center",
                        backgroundColor: "rgba(232, 245, 233, 0.5)", // Light green background
                        borderRadius: 2,
                        padding: 2,
                      }}
                    >
                      <Typography
                        variant="h6"
                        color="success.main"
                        gutterBottom
                        sx={{ display: "flex", alignItems: "center" }}
                      >
                        <CheckCircleOutlineIcon sx={{ mr: 1 }} /> Healthy
                        Balance
                      </Typography>
                      <Typography variant="body1">
                        Your wallet balance is in good standing. You have
                        sufficient funds for upcoming services.
                      </Typography>
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                          mt: 2,
                        }}
                      >
                        <Button
                          variant="outlined"
                          color="success"
                          startIcon={<AddIcon />}
                          onClick={() => setOpen(true)}
                          sx={{ borderRadius: "12px" }}
                        >
                          Add More Funds
                        </Button>

                        <Button
                          variant="text"
                          color="primary"
                          endIcon={<ArrowForwardIcon />}
                          onClick={() => setTransactionHistoryOpen(true)}
                        >
                          View Transactions
                        </Button>
                        <TransactionHistory
                          open={transactionHistoryOpen}
                          onClose={() => setTransactionHistoryOpen(false)}
                          transactions={
                            walletTransactionsHistory &&
                            walletTransactionsHistory
                          }
                          handleUpgrade={handleUpgrade}
                          currentUpiPhoneNumber={currentUpiPhoneNumber}
                          handleChangeUpiPhoneNumber={
                            handleChangeUpiPhoneNumber
                          }
                          upiNumberError={upiNumberError}
                        />
                      </Box>
                    </Box>
                  ) : (
                    (totalAmount ||
                      (totalAmount === 0 &&
                        expectedWalletstatus !== "loading")) && (
                      <Box
                        sx={{
                          flex: 1,
                          display: "flex",
                          flexDirection: "column",
                          justifyContent: "center",
                          backgroundColor: "rgba(255, 235, 238, 0.5)",
                          borderRadius: 2,
                          padding: 2,
                          // border: '1px solid #e0e0e0',
                        }}
                      >
                        <Typography
                          variant="h6"
                          color="error"
                          gutterBottom
                          sx={{ display: "flex", alignItems: "center" }}
                        >
                          <ErrorOutlineIcon sx={{ mr: 1 }} /> Low Balance Alert
                        </Typography>
                        <Typography variant="body1">
                          Your wallet balance is running low. Consider adding
                          funds to avoid any interruptions in service.
                        </Typography>
                        <Typography variant="body1" sx={{ mt: 1 }}>
                          The minimum balance required to maintain is 1000 Rs.
                        </Typography>
                        <Box
                          sx={{
                            display: "flex",
                            justifyContent: "space-between",
                            flexDirection: { xs: "column", sm: "row" },
                            gap: { xs: 1, sm: 0 },
                            //  mt: 2
                          }}
                        >
                          <Tooltip
                            title="Your account has not been verified with meta.
                            Please link with meta to add wallet amount."
                            placement="right"
                            open={
                              isMetaVerifiedToolTip && tooltipView === "alert"
                            }
                            onClose={() => setIsMetaVerifiedToolTip(false)}
                          >
                            <Button
                              variant="contained"
                              color="primary"
                              sx={{ mt: 2, alignSelf: "flex-start" }}
                              // onClick={() => setOpen(true)}
                              onClick={() => {
                                handleAddBalance();
                                setTooltipView("alert");
                              }}
                            >
                              Top Up Now
                            </Button>
                          </Tooltip>{" "}
                          <Button
                            variant="text"
                            color="primary"
                            endIcon={<ArrowForwardIcon />}
                            onClick={() => setTransactionHistoryOpen(true)}
                          >
                            View Transactions
                          </Button>
                          <TransactionHistory
                            open={transactionHistoryOpen}
                            onClose={() => setTransactionHistoryOpen(false)}
                            transactions={
                              walletTransactionsHistory &&
                              walletTransactionsHistory
                            }
                            handleUpgrade={handleUpgrade}
                            currentUpiPhoneNumber={currentUpiPhoneNumber}
                            handleChangeUpiPhoneNumber={
                              handleChangeUpiPhoneNumber
                            }
                            upiNumberError={upiNumberError}
                          />
                        </Box>
                      </Box>
                    )
                  )}
                </Grid>
              </Grid>
            </Box>
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                justifyContent: "space-between",
                gap: "30px",
              }}
            >
              <Box ml={2}>
                <Typography
                  variant="body2"
                  className={classes.blackColor}
                  style={{ marginTop: "30px", fontWeight: "bold" }}
                >
                  Transactions
                </Typography>
                <Typography
                  variant="body2"
                  className={classes.grayColor}
                  style={{ marginTop: "10px" }}
                >
                  Find all your transactions happening through your wallet and
                  card (for subscription fee) here.
                </Typography>
              </Box>
              {/* {transactionData && transactionData.length > 0 && value === "1" && */}
              {value === "1" && getWalletPermissions && (
                <Box
                  mx={2}
                  sx={{
                    display: "flex",
                    // justifyContent: "space-between"
                    flexDirection: { xs: "column", sm: "row" },
                    gap: "20px",
                  }}
                >
                  <DateRangePicker
                    onDateRangeChange={handleDateRangeChange}
                    disabled={transactionData && transactionData?.length < 1}
                    startCalenderDate={startCalenderDate}
                    endCalenderDate={endCalenderDate}
                  />
                  <Button
                    disabled={!startCalenderDate || !endCalenderDate}
                    onClick={handleApplyDateRange}
                    sx={{
                      border: "1px solid green",
                      borderRadius: "12px",
                      color: "green",
                      height: "35px",
                      fontSize: "12px",
                    }}
                  >
                    {isDateRangeApplied ? "Reset" : "Apply"}
                  </Button>
                </Box>
              )}
            </Box>

            <Box
              style={{
                marginTop: "10px",
              }}
            >
              <TabContext value={value}>
                <Box px={2}>
                  <TabList
                    onChange={handleChange}
                    indicatorColor="secondary"
                    aria-label="simple tabs example"
                    sx={{
                      overflow: "auto",
                      "& .MuiTabs-indicator": {
                        backgroundColor: bgColors.green,
                        height: "2px",
                      },
                      "& .Mui-selected": {
                        color: `${bgColors.black} !important`,
                        fontWeight: "600 !important",
                      },
                      // Add this to adjust padding for all tabs
                      "& .MuiTab-root": {
                        paddingRight: "10px",
                        paddingLeft: "0px",
                      },
                    }}
                  >
                    {getWalletPermissions && (
                      <Tab
                        label="Wallet Transactions"
                        value="1"
                        sx={{
                          textTransform: "none",
                          paddingRight: "10px", // Specific to this tab
                        }}
                      />
                    )}
                    {/* { allowAccessToSubscriptionDetails&&  */}
                    {getSubscriptionPermissions && (
                      <Tab
                        label="Subscription Deductions"
                        value="2"
                        sx={{
                          textTransform: "none",
                          paddingRight: "10px",
                          marginLeft: "20px",
                        }}
                      />
                    )}
                    {/* } */}
                  </TabList>
                </Box>
                <hr className={classes.lineStyles} />
                {getWalletPermissions && (
                  <TabPanel sx={{ padding: 0 }} value="1">
                    <CommonTable
                      columns={walletTransactionColumns}
                      data={transformedTransactionData}
                      actions={renderInvoiceColumn}
                      isLoading={transactionDataStatus === "loading"}
                      actionsLabel="Invoice"
                      showPagination={false}
                      renderOnMobile={renderWalletTransactionOnMobile}
                    />
                  </TabPanel>
                )}
                {getSubscriptionPermissions && (
                  <TabPanel sx={{ padding: 0 }} value="2">
                    <Box>
                      <CommonTable
                        columns={subscriptionTransactionColumns}
                        data={transformedSubscriptionData}
                        isLoading={subscriptionDataStatus === "loading"}
                        heightOfTable="auto"
                        showPagination={false}
                        renderOnMobile={renderSubscriptionTransactionOnMobile}
                      />
                    </Box>
                  </TabPanel>
                )}
                <br />
              </TabContext>
            </Box>
          </>
        ) : (
          <LoadingComponent height="520px" color={bgColors.blue} />
        )}
      </Grid>
    </Grid>
  );
};

export default Wallet;
