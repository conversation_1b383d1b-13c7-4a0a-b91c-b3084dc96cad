/* global process */

import axios from "axios";
import { getStoredTokens } from "../../utils/authUtils";

const TEMPLATES_API_URL = process.env.REACT_APP_BASE_URL;

const getAuthHeader = () => {
  const tokens = getStoredTokens();
  return tokens?.token ? `Bearer ${tokens.token}` : "";
};

const previewAuthTemplate = (data: any) => {
  return axios({
    url: `${TEMPLATES_API_URL}/Template/auth-template-preview`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    params: data,
  });
};

const createAuthTemplate = (data: any) => {
  return axios({
    url: `${TEMPLATES_API_URL}/Template/create-auth-template?businessId=${data.businessId}`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: data,
  });
};

const createCarouselTemplate = (data: any) => {
  return axios({
    url: `${TEMPLATES_API_URL}/Template/CreateCarouselTemplate?Draft=${data?.draft}`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: data.payload,
  });
};

const createTemplate = (data: any) => {
  return axios({
    url: `${TEMPLATES_API_URL}/Template/CreateTemplate?Draft=${data?.draft}`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: data,
  });
};

const editTemplate = (data: any) => {
  return axios({
    url: `${TEMPLATES_API_URL}/Template/EditTemplate`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: data,
  });
};

const getTemplatesByCompanyId = (data: any) => {
  return axios({
    url: `${TEMPLATES_API_URL}/Template/TemplateFilters?BusinessId=${data?.businessId}&UserId=${data?.userId}&page=${data?.pageNumber}&per_page=${data?.per_page}`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: data?.filters,
  });
};

const getTemplateById = (data: any) => {
  return axios({
    url: `${TEMPLATES_API_URL}/Template/TemplatebyId?BusinessId=${data?.businessId}&UserId=${data?.userId}&id=${data?.templateId}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
  });
};

const deleteTemplate = (data: any) => {
  return axios({
    url: `${TEMPLATES_API_URL}/Template/Delete-TemplateById`,
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: data,
  });
};

const restoreTemplate = (data: any) => {
  return axios({
    url: `${TEMPLATES_API_URL}/Template/restored-TemplateById`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: data,
  });
};

const uploadFile = (data: any) => {
  return axios({
    url: `${TEMPLATES_API_URL}/Template/uploadFile`,
    method: "POST",
    headers: {
      "Content-Type": "multipart/form-data",
      Authorization: getAuthHeader(),
    },
    data: data,
  });
};

const checkTemplateName = (data: any) => {
  return axios({
    url: `${TEMPLATES_API_URL}/Template/check-template-name/${data?.businessId}/${data?.templateName}`,
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
  });
};

export const TEMPLATE_APIS = {
  previewAuthTemplate,
  createAuthTemplate,
  createTemplate,
  editTemplate,
  getTemplatesByCompanyId,
  getTemplateById,
  deleteTemplate,
  restoreTemplate,
  uploadFile,
  checkTemplateName,
  createCarouselTemplate,
};
