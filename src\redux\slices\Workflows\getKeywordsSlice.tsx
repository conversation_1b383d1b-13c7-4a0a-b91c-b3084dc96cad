import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { WORKFLOW_API } from "../../../Apis/AdminLogin/Automation/Workflows";

export interface IData {
    getKeywordsStatus: "loading" | "succeeded" | "failed" | "idle";
    getKeywordsData: any;
  }

  const initialState: IData = {
    getKeywordsStatus: "idle",
    getKeywordsData: null,
  };

  export const getKeywords = createAsyncThunk(
    "workflow/getKeywords",
    async (data: any, { rejectWithValue }) => {
      try {
        const response = await WORKFLOW_API.getKeywords(data);
        return response?.data;
      } catch (error: any) {
        // Provide a descriptive error message
        return rejectWithValue(error.response || 'An error occurred');
      }
    }
  );


  export const getKeywordsSlice = createSlice({
    name: "getKeywordsSlice",
    initialState,
    reducers: {
       
    },
    extraReducers: (builder) => {
      builder
        .addCase(getKeywords.pending, (state) => {
          state.getKeywordsStatus = "loading";
          state.getKeywordsData = null;
        })
        .addCase(getKeywords.fulfilled, (state, action) => {
          state.getKeywordsStatus = "succeeded";
          state.getKeywordsData = action.payload;
        })
        .addCase(getKeywords.rejected, (state) => {
          state.getKeywordsStatus = "failed";
        });
    },
  });


export const getKeywordsActions = getKeywordsSlice.actions;
export default getKeywordsSlice.reducer;
