import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { WORKFLOW_API } from "../../../Apis/AdminLogin/Automation/Workflows";

export interface IData {
  createKeywordReactflowStatus: "loading" | "succeeded" | "failed" | "idle";
  createKeywordReactflowData: any;
}

const initialState: IData = {
  createKeywordReactflowStatus: "idle",
  createKeywordReactflowData: null,
};

export const createKeywordReactflow = createAsyncThunk(
  "createKeywordReactflow",
  async (data: any, { rejectWithValue }) => {
    try {
      const response = await WORKFLOW_API.createKeywordReactflow(data);
      return response?.data;
    } catch (error: any) {
      // Provide a descriptive error message
      return rejectWithValue(error.response || "An error occurred");
    }
  }
) as any;

export const createKeywordReactflowSlice = createSlice({
  name: "createKeywordReactflowSlice",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(createKeywordReactflow.pending, (state) => {
        state.createKeywordReactflowStatus = "loading";
        state.createKeywordReactflowData = null;
      })
      .addCase(createKeywordReactflow.fulfilled, (state, action) => {
        state.createKeywordReactflowStatus = "succeeded";
        state.createKeywordReactflowData = action.payload;
      })
      .addCase(createKeywordReactflow.rejected, (state) => {
        state.createKeywordReactflowStatus = "failed";
      });
  },
});

export const createKeywordActions = createKeywordReactflowSlice.actions;
export default createKeywordReactflowSlice.reducer;
