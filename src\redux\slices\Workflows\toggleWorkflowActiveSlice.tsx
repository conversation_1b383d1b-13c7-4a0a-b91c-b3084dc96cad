import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { WORKFLOW_API } from "../../../Apis/AdminLogin/Automation/Workflows";

export interface IData {
    toggleWorkflowActiveStatus: "loading" | "succeeded" | "failed" | "idle";
    toggleWorkflowActiveData: any;
  }

  const initialState: IData = {
    toggleWorkflowActiveStatus: "idle",
    toggleWorkflowActiveData: null,
  };

  export const toggleWorkflowActive = createAsyncThunk(
    "workflow/toggleWorkflowActive",
    async (data: any, { rejectWithValue }) => {
      try {
        const response = await WORKFLOW_API.toggleWorkflowActive(data);
        return response?.data;
      } catch (error: any) {
        // Provide a descriptive error message
        return rejectWithValue(error.response || 'An error occurred');
      }
    }
  );


  export const toggleWorkflowActiveSlice = createSlice({
    name: "toggleWorkflowActiveSlice",
    initialState,
    reducers: {
       
    },
    extraReducers: (builder) => {
      builder
        .addCase(toggleWorkflowActive.pending, (state) => {
          state.toggleWorkflowActiveStatus = "loading";
          state.toggleWorkflowActiveData = null;
        })
        .addCase(toggleWorkflowActive.fulfilled, (state, action) => {
          state.toggleWorkflowActiveStatus = "succeeded";
          state.toggleWorkflowActiveData = action.payload;
        })
        .addCase(toggleWorkflowActive.rejected, (state) => {
          state.toggleWorkflowActiveStatus = "failed";
        });
    },
  });


export const toggleWorkflowActiveActions = toggleWorkflowActiveSlice.actions;
export default toggleWorkflowActiveSlice.reducer;
