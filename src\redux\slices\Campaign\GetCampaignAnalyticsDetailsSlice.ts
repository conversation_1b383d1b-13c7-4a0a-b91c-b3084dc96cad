import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { CAMPAIGN_API } from "../../../Apis/Campaign/Campaign";


export interface IData {
    status: "loading" | "succeeded" | "failed" | "idle";
    data: any;
  }

  const initialState: IData = {
    status: "idle",
    data: null,
  };

  export const getCampaignAnalyticsDetails = createAsyncThunk(
    "getCampaignAnalyticsDetails",
    async (data: any) => {
      const response = await CAMPAIGN_API.getCampaignById(data);
      return response?.data;
    }
  );


  export const getCampaignAnalyticsDetailsSlice = createSlice({
    name: "getCampaignAnalyticsDetailsSlice",
    initialState,
    reducers: {},
    extraReducers: (builder) => {
      builder
        .addCase(getCampaignAnalyticsDetails.pending, (state) => {
          state.status = "loading";
        })
        .addCase(getCampaignAnalyticsDetails.fulfilled, (state, action) => {
          state.status = "succeeded";
          state.data = action.payload;
        })
        .addCase(getCampaignAnalyticsDetails.rejected, (state) => {
          state.status = "failed";
        });
    },
  });

  export const GetCampaignAnalyticsDetailsActions = getCampaignAnalyticsDetailsSlice.actions
  export default getCampaignAnalyticsDetailsSlice.reducer
