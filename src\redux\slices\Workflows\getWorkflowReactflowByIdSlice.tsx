import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { WORKFLOW_API } from "../../../Apis/AdminLogin/Automation/Workflows";

export interface IData {
  getWorkflowReactflowByIdStatus: "loading" | "succeeded" | "failed" | "idle";
  getWorkflowReactflowByIdData: any;
}

const initialState: IData = {
  getWorkflowReactflowByIdStatus: "idle",
  getWorkflowReactflowByIdData: null,
};

export const getWorkflowReactflowById = createAsyncThunk(
  "getWorkflowReactflowById",
  async (data: any, { rejectWithValue }) => {
    try {
      const response = await WORKFLOW_API.getWorkflowReactflowById(data);
      return response?.data;
    } catch (error: any) {
      // Provide a descriptive error message
      return rejectWithValue(error.response || "An error occurred");
    }
  }
) as any;

export const getWorkflowReactflowByIdSlice = createSlice({
  name: "getWorkflowReactflowByIdSlice",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getWorkflowReactflowById.pending, (state) => {
        state.getWorkflowReactflowByIdStatus = "loading";
        state.getWorkflowReactflowByIdData = null;
      })
      .addCase(getWorkflowReactflowById.fulfilled, (state, action) => {
        state.getWorkflowReactflowByIdStatus = "succeeded";
        state.getWorkflowReactflowByIdData = action.payload;
      })
      .addCase(getWorkflowReactflowById.rejected, (state) => {
        state.getWorkflowReactflowByIdStatus = "failed";
      });
  },
});

export const getWorkflowReactflowByIdActions = getWorkflowReactflowByIdSlice.actions;
export default getWorkflowReactflowByIdSlice.reducer;
