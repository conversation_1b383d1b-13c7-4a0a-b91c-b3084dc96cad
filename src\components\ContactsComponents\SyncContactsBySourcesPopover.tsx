import { List, ListItem, Popover } from "@mui/material";
import { SourceType } from "../../utils/constants";
import { CONTACTS_APIS } from "../../Apis/Contacts/ContactsApis";
import { useAppDispatch, useAppSelector } from "../../utils/redux-hooks";
import { toastActions } from "../../utils/toastSlice";

const excluded = ["None", "LeadRat"];
function SyncContactsBySourcesPopover({ anchorEl, handleClose }: any) {
  const userData = useAppSelector((state: any) => state?.adminLogin?.data);
  const dispatch = useAppDispatch();
  const handleSyncBySources = async (source: any) => {
    try {
      const response = await CONTACTS_APIS.syncContactsBySource({
        businessId: userData?.companyId,
        source: source.value,
      });

      if (response?.status === 200) {
        handleClose();
        dispatch(
          toastActions.setToaster({
            type: "success",
            message: `${response?.data?.message}`,
          })
        );
      } else {
        dispatch(
          toastActions.setToaster({
            type: "error",
            message: `${response?.data?.message}`,
          })
        );
      }
    } catch (error) {
      dispatch(
        toastActions.setToaster({
          type: "error",
          message: `${error}`,
        })
      );
    }
  };
  const sourceTypeArray = Object.keys(SourceType)
    .filter((key) => isNaN(Number(key)) && !excluded.includes(key))
    .map((key) => ({
      label: key,
      value: SourceType[key as keyof typeof SourceType],
    }));
  return (
    <Popover
      open={Boolean(anchorEl)}
      anchorEl={anchorEl}
      onClose={handleClose}
      anchorOrigin={{
        vertical: "bottom",
        horizontal: "left",
      }}
      transformOrigin={{
        vertical: "top",
        horizontal: "left",
      }}
    >
      <List>
        {sourceTypeArray?.map((source) => (
          <ListItem
            sx={{
              cursor: "pointer",
              fontSize: "14px",
              "&:hover": { backgroundColor: "#f0f0f0" },
            }}
            key={source.value}
            onClick={() => handleSyncBySources(source)}
          >
            {source.label}
          </ListItem>
        ))}
      </List>
    </Popover>
  );
}

export default SyncContactsBySourcesPopover;
