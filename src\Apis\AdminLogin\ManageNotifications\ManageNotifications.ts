/* global process */

import axios from "axios";
import { getStoredTokens } from "../../../utils/authUtils";

const getAuthHeader = () => {
  const tokens = getStoredTokens();
  return tokens?.token ? `Bearer ${tokens?.token}` : "";
};

const USER_API_URL = process.env.REACT_APP_BASE_URL;

const getAllNotifications = (data: any) => {
  return axios({
    url: `${USER_API_URL}/NotificationPreference/get-notificationsList?userId=${data?.userId}&companyId=${data?.companyId}`,

    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
  });
};

const postAllNotifications = (data: any) => {
  return axios({
    url: `${USER_API_URL}/NotificationPreference/update-notifications?userId=${data?.userId}&companyId=${data?.companyId}&notificationName=${data?.notificationName}&isActive=${data?.isActive}`,

    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    // data: JSON.stringify({ data: data }),
    // data: JSON.stringify(data),
    // data: data,
  });
};

export const MANAGE_NOTIFICATIONS_APIS = {
  getAllNotifications,
  postAllNotifications,
};
