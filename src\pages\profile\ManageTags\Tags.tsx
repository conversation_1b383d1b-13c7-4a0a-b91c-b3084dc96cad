import {
  Autocomplete,
  Box,
  Button,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
} from "@mui/material";
import React, { ChangeEvent, useState } from "react";
import { bgColors } from "../../../utils/bgColors";
import { makeStyles } from "@mui/styles";
import { useAppDispatch, useAppSelector } from "../../../utils/redux-hooks";
import NewTagMember from "../../../components/ContactsComponents/NewTagMember";
import LocalOfferIcon from "@mui/icons-material/LocalOffer";
import TagsTableData from "../../../components/TagComponents/TableData";
import SearchIconSvg2 from "../../../assets/svgs/SearchIconSvg2";
import CommonHeader from "../../../components/common/CommonHeader";

const useStyles = makeStyles({
  mainContainer: {
    // backgroundColor: bgColors.white1,
    width: "100%",
    height: "100vh",
  },
  bgContainer: {
    backgroundColor: bgColors.white,
  },
  headerContainer: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    borderBottom: "1px solid #f0f0f0",
    width: "100%",
    paddingLeft: "24px",
    paddingRight: "24px",
    paddingTop: "8px",
    paddingBottom: "8px",
  },
  tabsContainer: {
    width: "100%",
  },
  account: {
    display: "flex",
    flexDirection: "column",
    justifyContent: "space-between",
    padding: "8px 0 0 0",
  },
  switch: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    fontSize: "14px !important",
    color: `${bgColors.black1} !important`,
    // opacity: "60% !important",
  },
  lineStyles: {
    border: "none",
    borderTop: `1px solid ${bgColors.gray2}`,
    width: "100%",
    margin: "0 auto",
  },
  blackColor: {
    color: `${bgColors.black1} !important`,
    fontWeight: "600 !important",
  },
  SaveChangesButton: {
    // backgroundColor: bgColors.green,
    // color: bgColors.white,
    color: bgColors.green,
    border: `1px solid ${bgColors.green}`,
    borderRadius: "8px",
    // width: "fit-content",
    width: "100px",
    height: "32px",
    // padding: "10px",
    cursor: "pointer",
    // marginLeft: "160px",
  },
  searchField: {
    width: "100%",
    borderRadius: "12px",
    // height: "38px",
    // backgroundColor: bgColors.white2,
    backgroundColor: "white",
    "& input::placeholder": {
      textAlign: "left",
      fontSize: "14px",
      fontFamily: "inter",
      color: "#000000 !important",
    },
  },
});

const Tags = () => {
  const classes = useStyles();
  const dispatch = useAppDispatch();
  const getContactTag = useAppSelector(
    (state: any) => state?.getContactTagsData?.data
  );
  const listOfOptions = [
    {
      name: "Tags",
    },
    {
      name: "Attributes",
    },
  ];

  // const [selected, setSelected] = useState<string>("Tags");
  const options = ["Tags"];
  const [value, setValue] = React.useState<any>(options[0]);
  const [newTagDialog, setNewTagDialog] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [searchQuery, setSearchQuery] = useState<string>("");

  const openNewTagPopover = () => {
    setNewTagDialog(true);
  };
  const handleCloseTagPopover = () => {
    setNewTagDialog(false);
  };
  const handleFieldChange = (value: string) => {
    setValue(value);
  };

  const handleSearchContacts = (value: string) => {
    setSearchQuery(value);
  };

  const filteredTags = getContactTag?.filter((tag: any) =>
    tag?.tag?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <Grid className={classes.mainContainer}>
      <Grid className={classes.bgContainer}>
        {" "}
        <Box className={classes.headerContainer}>
          <CommonHeader
            title="Manage Tags"
            searchProps={{
              value: searchQuery,
              onChange: handleSearchContacts,
            }}
            primaryAction={{
              label: "Add Tag",
              onClick: openNewTagPopover,
            }}
          />
        </Box>
        
        <TagsTableData
          data={value}
          newTagDialog={newTagDialog}
          filteredTags={filteredTags}
        />
        <NewTagMember open={newTagDialog} handleClose={handleCloseTagPopover} />
      </Grid>
    </Grid>
  );
};

export default Tags;
