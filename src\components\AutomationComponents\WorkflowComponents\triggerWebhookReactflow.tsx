import React, { useEffect, useRef, useState } from "react";
import { useAppDispatch, useAppSelector } from "../../../utils/redux-hooks";
import { getVariableNames } from "../../../redux/slices/Workflows/getVariableNamesSlice";
import { bgColors } from "../../../utils/bgColors";
import {
  getNextVariableCount,
  validateVariables,
  parseCurlCommand,
} from "./functions";
import { validateUrl } from "../../../utils/functions";
import { validateJson } from "../../../utils/functions";
import { VariableType } from "../../../utils/enums";
import WebhookAccordionReactflow from "./webhookAccordionReactflow";
import { Box, IconButton, Paper, Typography } from "@mui/material";
import TextFieldWithBorderComponent from "../../common/TextFieldWithBorderComponent";
import CloseIcon from "@mui/icons-material/Close";
import CloseIconSvg from "../../../assets/svgs/CloseIconSvg";
import { toastActions } from "../../../utils/toastSlice";

interface Header {
  key: string;
  value: string;
}

const TriggerWebhookPageReactflow = ({
  webhookTriggerHttpMethod,
  webhookTriggerUrl,
  webhookTriggerHeader,
  webhookTriggerBody,
  handleWebhookTriggerUpdateAll,
  saveResponseType,
  handleSaveResponseType,
  defaultErrorResponse,
  handleDefaultErrorResponse,
  handleSelectedListChange,
  handleButtonsChange,
  variables,
  handleEditorStateChange,
  handleSave,
  isWorkflowEditing,
  handleVariablesChange,
  handleVariablesValueChange,
  handleVariablesFallbackValueChange,
  handleEdit,
}: any) => {
  const dispatch = useAppDispatch();
  const requestUrlInputRef = useRef<HTMLInputElement>(null);
  const bodyDescriptionInputRef = useRef<HTMLInputElement>(null);

  const userProfileSlice = useAppSelector((state: any) => state?.adminLogin);
  const userData = userProfileSlice?.data;

  const [isAddNewPopupOpen, setIsAddNewPopupOpen] = useState(false);
  const [newVariableName, setNewVariableName] = useState("");
  const [requestType, setRequestType] = useState(
    webhookTriggerHttpMethod || "Select HTTP Method"
  );
  const [requestUrl, setRequestUrl] = useState(webhookTriggerUrl || "");
  const [headers, setHeaders] = useState<Header[]>(
    webhookTriggerHeader || [{ key: "Content-Type", value: "application/json" }]
  );

  const [errorDescription, setErrorDescription] = useState(
    defaultErrorResponse ||
      "We are sorry. Unable to process your request at this time. Please try again later."
  );
  const [bodyDescription, setBodyDescription] = useState(
    webhookTriggerBody || "{}"
  );

  const [showSaveUserResponse, setShowSaveUserResponse] = useState(false);
  const [selectedVariable, setSelectedVariable] = useState(null);
  const [urlError, setUrlError] = useState(false);
  const [jsonError, setJsonError] = useState(false);
  const [variablesError, setVariablesError] = useState(false);
  const [textFieldValue, setTextFieldValue] = useState<string>("");
  const [curlInput, setCurlInput] = useState("");
  const [isCurlMode, setIsCurlMode] = useState(false);
  const [curlBodyProperties, setCurlBodyProperties] = useState<any[]>([]);
  const { data } = useAppSelector((state: any) => state.getVariableNames);

  const contentRef = useRef<HTMLDivElement>(null);
  const [dynamicHeight, setDynamicHeight] = useState(500);
  const [isAnyAccordionExpanded, setIsAnyAccordionExpanded] = useState(false);
  // Update useEffect to measure content height
  useEffect(() => {
    if (contentRef.current) {
      const height = contentRef.current.scrollHeight;
      // When no accordions are expanded, use minimal height
      if (!isAnyAccordionExpanded) {
        setDynamicHeight(370); // Minimal height when collapsed
      } else {
        // When expanded, use content height with limits
        const newHeight = Math.min(Math.max(height + 60, 370), 500);
        setDynamicHeight(newHeight);
      }
    }
  }, [
    curlInput,
    headers,
    bodyDescription,
    curlBodyProperties,
    requestType,
    requestUrl,
    isAnyAccordionExpanded,
  ]);

  const handleAddNewClick = () => {
    setIsAddNewPopupOpen(true);
  };
  const handlePopupClose = () => {
    setIsAddNewPopupOpen(false);
    setNewVariableName("");
  };
  const handlePopupSuccess = () => {
    // Refresh the variable list
    // fetchWorkflowVariableNames();
  };
  // Handle request URL changes
  const handleRequestTypeChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setRequestType(event.target.value);
    handleWebhookTriggerUpdateAll({ method: event.target.value });
  };

  const handleRequestUrlChange = (value: string) => {
    if (value === "" || value === null || value === undefined) {
      setUrlError(true);
    } else if (validateUrl(value)) {
      setUrlError(false); // No error if the URL is valid or empty
    } else {
      setUrlError(true); // Set error if URL is invalid
    }

    // Update requestUrl
    setRequestUrl(value);
    handleWebhookTriggerUpdateAll({ url: value });
  };

  // Handle header changes
  const handleHeaderChange = (index: number, field: string, value: string) => {
    const updatedHeaders = headers.map((header, i) =>
      i === index ? { ...header, [field]: value } : header
    );
    setHeaders(updatedHeaders);
    handleWebhookTriggerUpdateAll({ headers: updatedHeaders });
  };

  // Add a new header field
  const handleAddHeader = () => {
    setHeaders([...headers, { key: "", value: "" }]);
  };

  // Remove a header
  const handleRemoveHeader = (index: number) => {
    // Get the header value that's being removed
    const removedHeaderValue = headers[index]?.value || "";

    // Remove the header from headers array
    const updatedHeaders = headers.filter((_, i) => i !== index);
    setHeaders(updatedHeaders);

    // Update headers in stepData state
    handleWebhookTriggerUpdateAll({ headers: updatedHeaders });
  };

  const toggleSaveUserResponse = () => {
    setShowSaveUserResponse(!showSaveUserResponse);
  };

  const handleErrorDescriptionChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setErrorDescription(event.target.value);

    // Update headers in stepData state
    handleDefaultErrorResponse(event.target.value);
  };

  const handleBodyDescriptionChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    try {
      const variableMatches = event.target.value?.match(/\{\{(\d+)\}\}/g) || [];
      const variableNumbers = variableMatches?.map((match) =>
        parseInt(match.replace(/\{\{|\}\}/g, ""), 10)
      );
      const currentVariables = variables?.webhookBodyVariables || [];

      if (variableNumbers.length > currentVariables.length) {
        const updatedVariables = [...currentVariables];
        const existingIndexes = currentVariables.map(
          (variable: any) => variable.index
        );
        // Add all missing variables
        variableNumbers.forEach((number) => {
          if (!existingIndexes.includes(number - 1)) {
            const variableText = `"{{${number}}}"`;
            const newVariable = {
              index: number - 1,
              veriable: variableText,
              value: "",
              type: VariableType.WebhookTriggerBody,
              fallbackValue: "",
              referenceTableType: 0,
            };
            updatedVariables.push(newVariable);
          }
        });
        handleVariablesChange(updatedVariables);
        // Handle variables array update
      } else if (variableNumbers.length < currentVariables.length) {
        const updatedVariables = currentVariables?.filter((variable: any) => {
          return variableNumbers.includes(variable.index + 1);
        });

        const reindexedVariables = rearrangeVariableIndexes(updatedVariables);
        handleVariablesChange(reindexedVariables);
      }

      if (event.target.value === "") {
        setJsonError(true);
      }
      validateJson(event.target.value);
      const validation = validateVariables(event.target.value);
      if (!validation) {
        setVariablesError(true);
      } else if (validation) {
        setVariablesError(false);
      }
      setJsonError(false);
    } catch (error) {
      setJsonError(true);
    }
    setBodyDescription(event.target.value);

    // Update body in Node data
    handleWebhookTriggerUpdateAll({ body: event.target.value });
  };

  const handleSaveUserVariableChange = (event: any, newValue: any) => {
    if (newValue && newValue.id !== "add-new") {
      setSelectedVariable(newValue);
      // Update the workflow data with the selected variable
      handleSaveResponseType("variable");
    }
  };

  const handleAddVariable = () => {
    // Get the next variable count
    const nextCount = getNextVariableCount(bodyDescription, "text");

    // Construct the variable text
    const variableText = `"{{${nextCount}}}"`;

    // Get cursor position from the input ref
    const cursorPosition = bodyDescriptionInputRef?.current?.selectionStart;

    // If cursor position is undefined or 0, add at the end
    const insertPosition =
      cursorPosition === undefined || cursorPosition === 0
        ? bodyDescription.length
        : cursorPosition;

    // Insert the variableText at the determined position in the bodyDescription
    const updatedBodyDescription = [
      bodyDescription.slice(0, insertPosition),
      variableText,
      bodyDescription.slice(insertPosition),
    ].join("");

    // Update the body description with the new variable
    setBodyDescription(updatedBodyDescription);
    handleEditorStateChange(updatedBodyDescription, "bodyDescription");
    handleWebhookTriggerUpdateAll({ body: updatedBodyDescription });

    // Handle variables array update
    const currentVariables = variables?.webhookBodyVariables || [];
    const newVariable = {
      index: nextCount - 1,
      veriable: variableText,
      value: "",
      type: VariableType.WebhookTriggerBody,
      fallbackValue: "",
      referenceTableType: 0,
    };
    const updatedVariables = [...currentVariables, newVariable];
    handleVariablesChange(updatedVariables);

    // Set focus back to the input and place cursor after the inserted variable
    setTimeout(() => {
      if (bodyDescriptionInputRef.current) {
        bodyDescriptionInputRef.current.focus();
        const newCursorPosition = insertPosition + variableText.length;
        bodyDescriptionInputRef.current.setSelectionRange(
          newCursorPosition,
          newCursorPosition
        );
      }
    }, 0);
  };

  const rearrangeVariableIndexes = (variables: any[]) => {
    return variables.map((variable, index) => ({
      ...variable,
      index: index,
    }));
  };

  const handleRemoveVariable = (index: number) => {
    // Get the variable to remove
    const variableToRemove = variables?.webhookBodyVariables[index];

    if (!variableToRemove) {
      return;
    }

    // Create a new array without the removed variable
    const updatedVariables = variables?.webhookBodyVariables?.filter(
      (v: any, i: number) => i !== index
    );

    // Keep original indices for remaining variables
    const reindexedVariables = updatedVariables.map((v: any) => ({
      ...v,
      veriable: v.veriable, // Keep the original variable string
    }));

    // Update the variables in the node data
    handleVariablesChange(reindexedVariables);

    // Remove the variable text from the body description
    const textContent = bodyDescription
      .replace(variableToRemove.veriable, "")
      .trim();

    // Update body description
    if (textContent !== "") {
      setBodyDescription(textContent);
      handleEditorStateChange(textContent, "bodyDescription");
      handleWebhookTriggerUpdateAll({ body: textContent });
    } else {
      setBodyDescription("{}");
      handleEditorStateChange("{}", "bodyDescription");
      handleWebhookTriggerUpdateAll({ body: "{}" });
    }

    // Force update the node data
    setTimeout(() => {
      handleVariablesChange(reindexedVariables);
    }, 0);
  };

  // Utility to get the next available variable index (not reusing deleted ones)
  const getNextVariableIndex = () => {
    if (curlBodyProperties.length === 0) return 1;
    const indices = curlBodyProperties.map((p) => p.variableIndex);
    let i = 1;
    while (indices.includes(i)) i++;
    return i;
  };
  function flattenObject(
    obj: Record<string, any>,
    parentKey = "",
    result: Record<string, any> = {}
  ): Record<string, any> {
    for (const key in obj) {
      const value = obj[key];
      const newKey = parentKey ? `${parentKey}.${key}` : key;

      if (value && typeof value === "object" && !Array.isArray(value)) {
        flattenObject(value, newKey, result);
      } else {
        result[newKey] = value;
      }
    }
    return result;
  }

  // Handle cURL input changes
  const handleCurlInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const curl = e.target.value;
    setCurlInput(curl);

    if (!curl.trim().startsWith("curl")) {
      setIsCurlMode(false);
      setCurlBodyProperties([]);
      return;
    }

    const parsed = parseCurlCommand(curl);
    if (parsed) {
      const { url, method, headers, body } = parsed;

      let properties: any[] = [];
      let updatedBody = "{}";
      let webhookBodyVars: any[] = [];

      if (body) {
        try {
          let varCounter = 1;
          const flattened = flattenObject(JSON.parse(body));

          properties = Object.entries(flattened).map(
            ([key, value]: [string, any]) => ({
              propertyName: key,
              fallback: value,
              variable: `{{${varCounter}}}`,
              variableIndex: varCounter++,
              attribute: "",
            })
          );

          // Create webhook body variables for each property
          webhookBodyVars = properties.map((prop, index) => ({
            index: index,
            veriable: prop.variable,
            value: "", // Leave the value empty initially, will be updated when attribute is selected
            type: 3, // VariableType.WebhookTriggerBody
            fallbackValue:
              typeof prop.fallback === "string"
                ? prop.fallback
                : JSON.stringify(prop.fallback),
            referenceTableType: 0,
          }));

          updatedBody = JSON.stringify(
            Object.fromEntries(
              properties.map((prop) => [prop.propertyName, prop.variable])
            ),
            null,
            2
          );
        } catch (error) {
          console.error("Error parsing curl body:", error);
          properties = [];
          updatedBody = "{}";
          webhookBodyVars = [];
        }
      }

      // Update all local state first to ensure UI consistency
      setIsCurlMode(true);
      setCurlBodyProperties(properties);
      setRequestType(method);
      setRequestUrl(url);
      setHeaders(headers);
      setBodyDescription(updatedBody);

      // Then update the node data in a single batch to avoid race conditions
      // This ensures all data is updated atomically
      updateNodeDataInBatch(method, url, headers, updatedBody, webhookBodyVars);
    } else {
      dispatch(
        toastActions.setToaster({
          type: "error",
          message: "Invalid curl command",
        })
      );
    }
  };

  // Helper function to update all node data in a single batch
  const updateNodeDataInBatch = (
    method: string,
    url: string,
    headers: any[],
    body: string,
    webhookBodyVars: any[]
  ) => {
    // First update the variables to ensure they're available when the body is processed
    handleVariablesChange(webhookBodyVars);

    // Then update all other node data
    handleWebhookTriggerUpdateAll({
      method,
      url,
      headers,
      body,
    });
  };

  // Handlers for cURL body properties
  const handleAddCurlBodyProperty = () => {
    const nextIndex = getNextVariableIndex();
    setCurlBodyProperties((prev) => {
      const newProp = {
        propertyName: "",
        fallback: "",
        variable: `{{${nextIndex}}}`,
        variableIndex: nextIndex,
        attribute: "",
      };

      const newProps = [...prev, newProp];

      // Update the body with the new property
      // Only update the body if the property has a name
      if (newProps.some((p) => p.propertyName)) {
        const newBody = newProps.reduce((acc, prop) => {
          if (prop.propertyName) {
            acc[prop.propertyName] = prop.variable;
          }
          return acc;
        }, {} as Record<string, any>);

        // Update the body description
        const bodyString = JSON.stringify(newBody, null, 2);
        setBodyDescription(bodyString);

        // Ensure the webhookTriggerBody is updated in the node data
        handleWebhookTriggerUpdateAll({ body: bodyString });

        // Force a re-render to ensure the UI is updated
        setTimeout(() => {
          // Call handleWebhookTriggerUpdateAll again to ensure the body is updated
          handleWebhookTriggerUpdateAll({ body: bodyString });
        }, 0);
      }

      // Add the new variable to the webhookBodyVariables
      if (variables?.webhookBodyVariables) {
        const newVariable = {
          index: variables.webhookBodyVariables.length,
          veriable: newProp.variable,
          value: "", // Leave the value empty initially, will be updated when attribute is selected
          type: 3, // VariableType.WebhookTriggerBody
          fallbackValue: "", // Initially empty fallback value since it's a new property
          referenceTableType: 0,
        };

        const updatedWebhookBodyVars = [
          ...variables.webhookBodyVariables,
          newVariable,
        ];
        handleVariablesChange(updatedWebhookBodyVars);
      }

      return newProps;
    });
  };

  const handleUpdateCurlBodyProperty = (
    index: number,
    field: string,
    value: string
  ) => {
    // Update the curlBodyProperties state and get the updated properties
    setCurlBodyProperties((prev) => {
      // Create updated properties array
      const updatedProps = prev.map((prop, i) => {
        if (i === index) {
          // If updating property name, keep the same fallback value
          if (field === "propertyName") {
            return { ...prop, propertyName: value };
          } else if (field === "attribute") {
            return { ...prop, attribute: value };
          } else if (field === "fallback") {
            return { ...prop, fallback: value };
          }
        }
        return prop;
      });

      // Create a new body object from the updated properties
      const newBody = updatedProps.reduce((acc, prop) => {
        if (prop.propertyName) {
          // Only add properties that have names
          acc[prop.propertyName] = prop.variable;
        }
        return acc;
      }, {} as Record<string, any>);

      // Update the body description and node data
      const bodyString = JSON.stringify(newBody, null, 2);
      setBodyDescription(bodyString);

      // Ensure the webhookTriggerBody is updated in the node data
      handleWebhookTriggerUpdateAll({ body: bodyString });

      // Force a re-render to ensure the UI is updated
      setTimeout(() => {
        // Call handleWebhookTriggerUpdateAll again to ensure the body is updated
        handleWebhookTriggerUpdateAll({ body: bodyString });
      }, 0);

      // Update the variables based on the updated properties
      const updatedWebhookBodyVars = updatedProps.map((prop, i) => {
        // Find existing variable if it exists
        const existingVar = variables?.webhookBodyVariables?.find(
          (v: any) => v.veriable === prop.variable
        );

        return {
          index: i,
          veriable: prop.variable,
          // Update the value when attribute is selected
          value:
            field === "attribute" && i === index
              ? value // Set the value to the selected attribute
              : existingVar?.value || "", // Otherwise keep existing value or empty
          type: 3, // VariableType.WebhookTriggerBody
          fallbackValue:
            field === "fallback" && i === index
              ? value // Use the new value for the fallback if this is the property being updated
              : existingVar?.fallbackValue ||
                (typeof prop.fallback === "string"
                  ? prop.fallback
                  : JSON.stringify(prop.fallback)),
          referenceTableType: 0,
        };
      });

      // Update the variables in the node data
      handleVariablesChange(updatedWebhookBodyVars);

      return updatedProps;
    });
  };
  const handleRemoveCurlBodyProperty = (index: number) => {
    // Remove the property and update the body
    setCurlBodyProperties((prev) => {
      // Create updated properties with continuous variable indices
      const updatedProps = prev
        .filter((_, i) => i !== index)
        .map((prop, newIndex) => {
          // Update the variable index and variable name to be continuous (1, 2, 3, ...)
          const newVarIndex = newIndex + 1;
          const newVariable = `{{${newVarIndex}}}`;

          return {
            ...prop,
            variable: newVariable,
            variableIndex: newVarIndex,
          };
        });

      // Create a new body object from the updated properties
      const newBody = updatedProps.reduce((acc, prop) => {
        if (prop.propertyName) {
          // Only add properties with names
          acc[prop.propertyName] = prop.variable;
        }
        return acc;
      }, {} as Record<string, any>);

      // Update the body description
      const bodyString = JSON.stringify(newBody, null, 2);
      setBodyDescription(bodyString);

      // Ensure the webhookTriggerBody is updated in the node data
      // This is crucial for the node to display the correct body
      handleWebhookTriggerUpdateAll({
        body: bodyString,
      });

      // Update the variables based on the updated properties
      if (variables?.webhookBodyVariables?.length > 0) {
        // Create new webhook body variables with continuous indices
        const updatedWebhookBodyVars = updatedProps.map((prop, i) => {
          // Find the corresponding old variable to preserve its values
          const oldVar = variables.webhookBodyVariables.find(
            (v: any) =>
              v.veriable ===
              prev.find(
                (p, idx) =>
                  idx !== index && p.propertyName === prop.propertyName
              )?.variable
          );

          return {
            index: i,
            veriable: prop.variable,
            value: oldVar?.value || "",
            type: 3, // VariableType.WebhookTriggerBody
            fallbackValue: oldVar?.fallbackValue || prop.fallback || "",
            referenceTableType: 0,
          };
        });

        // Update the variables in the node data
        handleVariablesChange(updatedWebhookBodyVars);

        // Force a re-render to ensure the UI is updated
        setTimeout(() => {
          // Call handleWebhookTriggerUpdateAll again to ensure the body is updated
          handleWebhookTriggerUpdateAll({
            body: bodyString,
          });
        }, 0);
      }

      return updatedProps;
    });
  };

  const handleClearCurl = () => {
    // Default headers
    const defaultHeaders = [{ key: "Content-Type", value: "application/json" }];

    // Update all local state first to ensure UI consistency
    setCurlInput("");
    setIsCurlMode(false);
    setCurlBodyProperties([]);
    setRequestType("Select HTTP Method");
    setRequestUrl("");
    setBodyDescription("{}");
    setHeaders(defaultHeaders);

    // Reset error states
    setUrlError(false);
    setJsonError(false);
    setVariablesError(false);

    // Then update the node data in a single batch to avoid race conditions
    // First clear variables
    handleVariablesChange([]);

    // Then update all other node data in a single operation
    handleWebhookTriggerUpdateAll({
      body: "{}",
      method: "",
      url: "",
      headers: defaultHeaders,
    });
  };

  useEffect(() => {
    const payload = {
      companyId: userData?.companyId,
    };
    dispatch(getVariableNames(payload));
  }, [dispatch]);

  // Add handler for accordion expansion
  const handleAccordionChange = (expanded: boolean) => {
    setIsAnyAccordionExpanded(expanded);
  };

  return (
    <Paper
      elevation={4}
      sx={{
        position: "absolute",
        right: -360,
        top: 0,
        width: 350,
        height: dynamicHeight,
        display: "flex",
        flexDirection: "column",
        backgroundColor: "white",
        border: `1px solid ${bgColors.gray2}`,
        borderRadius: 2,
        boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
        transition: "all 0.3s ease",
        zIndex: 10,
      }}
      className="nowheel nopan nodrag"
    >
      <Box
        sx={{
          position: "sticky",
          top: 0,
          backgroundColor: bgColors.green,
          zIndex: 1000,
          padding: "8px 16px",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          borderBottom: `1px solid ${bgColors.gray2}`,
          flexShrink: 0,
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            gap: 1,
            width: "100%",
          }}
        >
          <Typography variant="h6" color="white">
            Configure Condition
          </Typography>
          <IconButton
            aria-label="close"
            sx={{
              color: `${bgColors.red}`,
              width: 48,
              height: 48,
              "& svg": {
                width: 40,
                height: 40,
                fill: `${bgColors.red1}`,
              },
            }}
            onClick={() => handleEdit()}
          >
            <CloseIconSvg />
          </IconButton>
        </Box>
      </Box>
      <Box
        ref={contentRef}
        p={2}
        sx={{
          flex: 1,
          overflowY: "auto",
          minHeight: 0,
          "&::-webkit-scrollbar": {
            width: "8px",
          },
          "&::-webkit-scrollbar-track": {
            background: "#f1f1f1",
            borderRadius: "4px",
          },
          "&::-webkit-scrollbar-thumb": {
            background: "#888",
            borderRadius: "4px",
            "&:hover": {
              background: "#555",
            },
          },
        }}
      >
        {/* cURL Input Field */}
        <Box sx={{ marginBottom: 2, position: "relative" }}>
          <TextFieldWithBorderComponent
            label="Paste cURL here"
            name="curl"
            placeholder="Paste a cURL command here (e.g. curl -X POST https://api.example.com ...)"
            value={curlInput}
            onChange={(e: any) => handleCurlInputChange(e)}
            sx={{
              "& .MuiInputBase-root": {
                paddingRight: "40px", // Add padding for the icon
              },
            }}
          />
          {curlInput && (
            <IconButton
              onClick={handleClearCurl}
              sx={{
                position: "absolute",
                right: 8,
                top: "50%",
                transform: "translateY(-50%)",
                padding: "4px",
                "&:hover": {
                  backgroundColor: "rgba(0, 0, 0, 0.04)",
                },
                zIndex: 1,
              }}
            >
              <CloseIcon fontSize="small" />
            </IconButton>
          )}
        </Box>

        {/* OR Separator */}
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            margin: "16px 0",
            "&::before, &::after": {
              content: '""',
              flex: 1,
              borderBottom: "1px solid #e0e0e0",
            },
          }}
        >
          <Typography
            sx={{
              px: 2,
              color: "text.secondary",
              fontSize: "14px",
            }}
          >
            OR
          </Typography>
        </Box>

        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            gap: 2,
            pb: 2,
          }}
        >
          <WebhookAccordionReactflow
            requestType={requestType}
            requestUrl={requestUrl}
            setRequestUrl={setRequestUrl}
            handleRequestTypeChange={handleRequestTypeChange}
            handleRequestUrlChange={handleRequestUrlChange}
            handleAddVariable={handleAddVariable}
            headers={headers}
            setHeaders={setHeaders}
            handleHeaderChange={handleHeaderChange}
            handleRemoveHeader={handleRemoveHeader}
            handleAddHeader={handleAddHeader}
            handleAddNewClick={handleAddNewClick}
            handlePopupClose={handlePopupClose}
            handlePopupSuccess={handlePopupSuccess}
            handleVariablesChange={handleVariablesChange}
            handleEditorStateChange={handleEditorStateChange}
            requestUrlInputRef={requestUrlInputRef}
            bodyDescriptionInputRef={bodyDescriptionInputRef}
            handleBodyDescriptionChange={handleBodyDescriptionChange}
            bodyDescription={bodyDescription}
            setBodyDescription={setBodyDescription}
            handleSaveUserVariableChange={handleSaveUserVariableChange}
            selectedVariable={selectedVariable}
            saveResponseType={saveResponseType}
            handleSaveResponseType={handleSaveResponseType}
            variableNames={data}
            isAddNewPopupOpen={isAddNewPopupOpen}
            setNewVariableName={setNewVariableName}
            newVariableName={newVariableName}
            errorDescription={errorDescription}
            handleErrorDescriptionChange={handleErrorDescriptionChange}
            handleSelectedListChange={handleSelectedListChange}
            handleButtonsChange={handleButtonsChange}
            showSaveUserResponse={showSaveUserResponse}
            setShowSaveUserResponse={setShowSaveUserResponse}
            toggleSaveUserResponse={toggleSaveUserResponse}
            handleSave={handleSave}
            isWorkflowEditing={isWorkflowEditing}
            textFieldValue={textFieldValue}
            setTextFieldValue={setTextFieldValue}
            jsonError={jsonError}
            urlError={urlError}
            variablesError={variablesError}
            variables={variables}
            handleRemoveVariable={handleRemoveVariable}
            handleVariablesValueChange={handleVariablesValueChange}
            handleVariablesFallbackValueChange={
              handleVariablesFallbackValueChange
            }
            isCurlMode={isCurlMode}
            curlBodyProperties={curlBodyProperties}
            handleAddCurlBodyProperty={handleAddCurlBodyProperty}
            handleUpdateCurlBodyProperty={handleUpdateCurlBodyProperty}
            handleRemoveCurlBodyProperty={handleRemoveCurlBodyProperty}
            onAccordionChange={handleAccordionChange}
          />
        </Box>
      </Box>
    </Paper>
  );
};

export default TriggerWebhookPageReactflow;
