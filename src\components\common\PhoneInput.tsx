import React, { useState, useEffect } from "react";
import { createTheme, ThemeProvider } from "@mui/material/styles";
import {
  Grid,
  FormControl,
  Select,
  MenuItem,
  Box,
  InputBaseProps,
} from "@mui/material";

import TextFieldWithBorderComponent from "./TextFieldWithBorderComponent";

interface CountryType {
  countryCode?: string;
  countryImage?: string;
}

interface Props {
  countryCodeArray: CountryType[];
  name: string;
  onChange?: (value: any) => void; // Callback function to handle value change
  onCountryCodeChange?: (value: any) => void; // Callback function to handle value change
  propsValue?: string;
  countryCode?: string;
  error?: boolean;
  helperText?: string;
  width: string;
  gapRequired: string;
  phoneNumWidth: string;
  fontweight?: string;
  disabled?: boolean;
  cntyPhnWidth?: any;
  selectStyles?: any;
  menuItemStyles?: any;
  muiSelectNoBlue?: any;
  textFieldStyles?: any;
}

const PhoneInput: React.FC<Props> = ({
  countryCodeArray,
  onChange,
  onCountryCodeChange,
  propsValue = "",
  countryCode = "",
  error = false,
  fontweight = "",
  disabled = false,
  cntyPhnWidth = {},
  selectStyles,
  menuItemStyles,
  muiSelectNoBlue,
  textFieldStyles,
}) => {
  // const location = useLocation();

  const defaultCountryCode = countryCode;
  const defaultPhoneNumber = propsValue;
  const [countryCodeValue, setCountryCodeValue] = useState<string | null>(
    defaultCountryCode || "+ 91"
  );
  const [phoneNumberValue, setPhoneNumberValue] =
    useState<string>(defaultPhoneNumber);
  let defValueWidth: any = Object.entries(cntyPhnWidth).length
    ? cntyPhnWidth
    : { cntyWidth: 3.5, phnWidth: 8 };

  useEffect(() => {
    if (defaultPhoneNumber && defaultCountryCode) {
      setPhoneNumberValue(defaultPhoneNumber);
      setCountryCodeValue(defaultCountryCode);
    }
  }, [defaultPhoneNumber, defaultCountryCode]);

  const handlePhoneChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.value.replace(/\s/g, "");
    if (onChange) {
      onChange(newValue); // Combine countryCode with new phoneNumber
    }
    setPhoneNumberValue(newValue);
  };

  const handleCountryCodeChange = (event: any) => {
    const newValue = event.target.value;
    if (onCountryCodeChange) {
      onCountryCodeChange(newValue);
    }
    setCountryCodeValue(newValue);
  };

  const theme = createTheme({
    components: {
      MuiSelect: {
        styleOverrides: {
          select: {
            "&:focus": {
              backgroundColor: "transparent",
            },
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            fontSize: "14px",
            fontWeight: fontweight,
          },
          icon: {
            right: "0.5px",
          },
          outlined: {
            paddingLeft: "7px",
          },
        },
      },
    },
  });

  const inputProps: InputBaseProps = {
    style: {
      // borderRadius: "8px",
      fontSize: "14px",
      fontWeight: fontweight,
    },
    inputProps: {
      pattern: "[0-9]*",
      inputMode: "numeric",
      onKeyPress: (event) => {
        const pattern = /[0-9]/;
        const inputChar = String.fromCharCode(event.charCode);
        if (!pattern.test(inputChar)) {
          event.preventDefault();
        }
      },
    },
  };

  return (
    <>
      {/* <style>
        {`
          ::-webkit-scrollbar {
            width: 6px;
          }

          ::-webkit-scrollbar-track {
            background: #f1f1f1;
          }

          ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 10px;
          }

          ::-webkit-scrollbar-thumb:hover {
            background: #555;
          }
        `}
      </style> */}
      <Grid
        container
        item
        xs={12}
        display="flex"
        justifyContent="space-between"
        flexDirection="row"
        sx={{ alignItems: "center", width: "100%" }}
      >
        <Grid item xs={4.5} sm={2.5} md={4} lg={3} mt={0}>
          <FormControl fullWidth>
            <ThemeProvider theme={theme}>
              <Select
                id="phoneNumber-Prefix"
                value={countryCodeValue}
                disabled={disabled}
                onChange={handleCountryCodeChange}
                size="small"
                inputProps={{
                  style: {
                    fontSize: "14px",
                  },
                }}
                sx={selectStyles}
                MenuProps={{
                  PaperProps: {
                    sx: {
                      "& .MuiMenuItem-root": { menuItemStyles },
                    },
                  },
                }}
                error={error}
                renderValue={(selected) => {
                  return (
                    <Box sx={{ display: "flex", alignItems: "center" }}>
                      <img
                        src={
                          countryCodeArray?.find(
                            (country) => country?.countryCode === selected
                          )?.countryImage ||
                          "https://dev-engageto.s3.ap-south-1.amazonaws.com/e8915291-d8f3-4bd6-8fbf-3304dbb91f4e_11/07/2024 11:21:59.png"
                        }
                        alt="Country Flag"
                        style={{
                          height: "20px",
                          marginRight: "5px",
                        }}
                      />
                      <span>{selected ? "" : "+91"}</span>
                      {selected}
                    </Box>
                  );
                }}
              >
                {countryCodeArray?.map((option) => (
                  <MenuItem
                    key={option.countryCode}
                    value={option.countryCode}
                    sx={menuItemStyles}
                  >
                    <img
                      src={option?.countryImage}
                      style={{ marginRight: "5px", height: 20, width: 20 }}
                    />
                    {option.countryCode}
                  </MenuItem>
                ))}
              </Select>
            </ThemeProvider>
          </FormControl>
        </Grid>

        <Grid item xs={7} sm={9} md={7.5} lg={8.5} ml="auto">
          <TextFieldWithBorderComponent
            label="Enter phone number"
            name="phoneNumber"
            disabled={disabled}
            placeholder=""
            error={error}
            value={phoneNumberValue}
            onChange={handlePhoneChange}
            size="small"
            InputProps={inputProps}
            InputLabelProps={{
              style: {
                fontSize: 14,
                marginLeft: "1.5px",
              },
            }}
            textFieldStyles={textFieldStyles}
          />
        </Grid>
      </Grid>
    </>
  );
};

export default PhoneInput;
