import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { WORKFLOW_API } from "../../../Apis/AdminLogin/Automation/Workflows";

export interface IData {
    getFlowstartNodesStatus: "loading" | "succeeded" | "failed" | "idle";
    getFlowstartNodesData: any;
  }

  const initialState: IData = {
    getFlowstartNodesStatus: "idle",
    getFlowstartNodesData: null,
  };

  export const getFlowstartNodes = createAsyncThunk(
    "workflow/getFlowstartNodes",
    async (data: any, { rejectWithValue }) => {
      try {
        const response = await WORKFLOW_API.getFlowstartNodes(data);
        return response?.data;
      } catch (error: any) {
        // Provide a descriptive error message
        return rejectWithValue(error.response || 'An error occurred');
      }
    }
  );


  export const getFlowstartNodesSlice = createSlice({
    name: "getFlowstartNodes",
    initialState,
    reducers: {
       
    },
    extraReducers: (builder) => {
      builder
        .addCase(getFlowstartNodes.pending, (state) => {
          state.getFlowstartNodesStatus = "loading";
          state.getFlowstartNodesData = null;
        })
        .addCase(getFlowstartNodes.fulfilled, (state, action) => {
          state.getFlowstartNodesStatus = "succeeded";
          state.getFlowstartNodesData = action.payload;
        })
        .addCase(getFlowstartNodes.rejected, (state) => {
          state.getFlowstartNodesStatus = "failed";
        });
    },
  });


export const getFlowstartNodesActions = getFlowstartNodesSlice.actions;
export default getFlowstartNodesSlice.reducer;
