import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { INBOX_APIS } from "../../../Apis/Inbox/InboxApis";

export interface IData {
  status: "loading" | "succeeded" | "failed" | "idle";
  data: any;
}

const initialState: IData = {
  status: "idle",
  data: null,
};

export const blockContact = createAsyncThunk(
  "blockContact",
  async (data: any) => {
    const response = await INBOX_APIS.blockContact(data);
    return response?.data;
  }
);

export const BlockContactSlice = createSlice({
  name: "BlockContactSlice",
  initialState,
  reducers: {
    setData: (state, action) => {
      state.data = action.payload;
    },
  },
  extraReducers(builder) {
    builder
      .addCase(blockContact.pending, (state) => {
        state.status = "loading";
        //   state.error = ""
        state.data = [];
      })
      .addCase(blockContact.fulfilled, (state, action) => {
        state.status = "succeeded";
        //   state.error = ""
        state.data = action.payload;
      })
      .addCase(blockContact.rejected, (state) => {
        state.status = "failed";
        state.data = [];
        //   state.error = action.error.message || "";
      });
  },
});

export const blockContactActions = BlockContactSlice.actions;
export default BlockContactSlice.reducer;
