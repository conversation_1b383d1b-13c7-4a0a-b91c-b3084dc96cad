import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { INBOX_APIS } from "../../../Apis/Inbox/InboxApis";

export interface IData {
  status: "loading" | "succeeded" | "failed" | "idle";
  data: any;
}

const initialState: IData = {
  status: "idle",
  data: null,
};

export const unblockContact = createAsyncThunk(
  "unblockContact",
  async (data: any) => {
    const response = await INBOX_APIS.unblockContact(data);
    return response?.data;
  }
);

export const UnBlockContactSlice = createSlice({
  name: "UnBlockContactSlice",
  initialState,
  reducers: {
    setData: (state, action) => {
      state.data = action.payload;
    },
  },
  extraReducers(builder) {
    builder
      .addCase(unblockContact.pending, (state) => {
        state.status = "loading";
        //   state.error = ""
        state.data = [];
      })
      .addCase(unblockContact.fulfilled, (state, action) => {
        state.status = "succeeded";
        //   state.error = ""
        state.data = action.payload;
      })
      .addCase(unblockContact.rejected, (state) => {
        state.status = "failed";
        state.data = [];
        //   state.error = action.error.message || "";
      });
  },
});

export const unblockContactActions = UnBlockContactSlice.actions;
export default UnBlockContactSlice.reducer;
