import React, { useEffect, useState } from "react";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import PhoneIcon from "@mui/icons-material/Phone";
import LaunchIcon from "@mui/icons-material/Launch";
import ReplyAllIcon from "@mui/icons-material/ReplyAll";
import Button from "@mui/material/Button";
import { parseTextToHtml } from "../../../utils/functions";
import { bgColors } from "../../../utils/bgColors";

import { Tabs, Tab } from "@mui/material";
interface WhatsAppMessageProps {
  header?: string;
  body?: any;
  footer?: string;
  mediaType?: any;
  mediaFile?: string | File;
  buttons?: {
    buttonType: string;
    buttonValue?: string;
    buttonName?: string;
  }[];
  carouselCards?: any;
}

const TemplatePreviewLayout: React.FC<WhatsAppMessageProps> = ({
  header,
  body,
  footer,
  mediaType,
  mediaFile,
  buttons,
  carouselCards,
}) => {
  const [bodyText, setBodyText] = useState("");

  // const parseTextToDraft:any = (text: any) => {
  //   // Replace markdown-like symbols with HTML tags
  //   const htmlText = text
  //     .replace(/\*(.*?)\*/g, '<b>$1</b>')
  //     .replace(/_(.*?)_/g, '<i>$1</i>')
  //     .replace(/~(.*?)~/g, '<strike>$1</strike>');

  //   return htmlText;
  // };
  // const parseTextToDraft = (text: string) => {
  //   // Replace markdown-like symbols with HTML tags for inline styles
  //   let htmlText = text
  //     ?.replace(/\*(.*?)\*/g, "<b>$1</b>")
  //     ?.replace(/_(.*?)_/g, "<i>$1</i>")
  //     ?.replace(/~(.*?)~/g, "<strike>$1</strike>");

  //   // Replace number points with HTML list items for ordered list
  //   htmlText = htmlText?.replace(/^(\d+\.\s.*)$/gm, "<li>$1</li>");
  //   htmlText = htmlText?.replace(/<li>(\d+\.\s)/g, "<li>"); // Remove the numbers inside the <li> tags
  //   if (htmlText?.includes("<li>")) {
  //     htmlText = `<ol>${htmlText}</ol>`;
  //   }

  //   // Replace bullet points with HTML list items for unordered list
  //   htmlText = htmlText?.replace(/^-\s+(.*)$/gm, "<li>$1</li>");
  //   if (htmlText?.includes("<li>") && !htmlText?.includes("<ol>")) {
  //     htmlText = `<ul>${htmlText}</ul>`;
  //   }

  //   // Preserve line breaks in paragraphs by replacing new lines with <br> tags
  //   htmlText = htmlText.replace(/\n/g, "<br>");

  //   return htmlText;
  // };

  useEffect(() => {
    const htmlText: any = parseTextToHtml(body && body);
    setBodyText(htmlText);
  }, [body]);

  return (
    <Box
      sx={{
        width: "100%",
        maxWidth: "400px",
        minWidth: "200px",
        margin: "0 auto",
        textAlign: "center",
        // height: mediaType !== 6 ? "calc(100vh - 60px)" : "auto",
        // border: "1px solid #cdcdcd",
        borderRadius: "4px",
        overflow: "hidden",
        backgroundColor: bgColors?.white,
      }}
    >
      {header ? (
        <Typography
          mx={1}
          sx={{
            fontSize: 16,
            fontWeight: "bold",
            color: "#075e54",
            textAlign: "justify",
            padding: "10px 6px",
          }}
        >
          {header}
        </Typography>
      ) : mediaType === 3 ? (
        <img
          src={
            mediaFile instanceof File
              ? URL.createObjectURL(mediaFile)
              : mediaFile
          }
          alt="Template Preview"
          style={{
            width: "100%",
            height: "200px",
            // maxHeight: "400px",
            objectFit: "fill",
          }}
        />
      ) : mediaType === 4 ? (
        <video
          style={{
            width: "100%",
            height: "auto",
            // maxHeight: "400px",
            objectFit: "contain",
          }}
          src={
            mediaFile instanceof File
              ? URL.createObjectURL(mediaFile)
              : mediaFile
          }
          controls={true}
        />
      ) : mediaType === 5 ? (
        <iframe
          style={{
            width: "100%",
            height: "300px",
            maxHeight: "300px",
            objectFit: "contain",
          }}
          src={
            mediaFile instanceof File
              ? URL.createObjectURL(mediaFile)
              : mediaFile
          }
        ></iframe>
      ) : (
        ""
      )}

      <Box
        sx={{
          p: "6px",
          width: "100%",
        }}
      >
        <Typography
          mx={1}
          sx={{
            textAlign: "start",
            fontSize: 14,
            color: "#000",
            wordBreak: "break-word",
            overflowWrap: "anywhere",
            whiteSpace: "pre-line",
          }}
          dangerouslySetInnerHTML={{ __html: bodyText || "" }}
        />
      </Box>
      {footer && (
        <Box style={{ padding: "10px 6px", width: "100%" }}>
          <Typography
            mx={1}
            sx={{ textAlign: "start", fontSize: 14, color: "#888" }}
          >
            {footer && footer}
          </Typography>
        </Box>
      )}
      {buttons?.length !== 0 && (
        <Box
        sx={{
          borderTop: "1px solid #E4E6EB",
          width: "100%",
          marginTop: "auto",
          flex: "0 0 auto",
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          alignItems: "center",
        }}
        >
          {buttons?.map((button, index) => (
            <Button
              key={index}
              startIcon={
                button?.buttonType === "PHONE_NUMBER" ? (
                  <PhoneIcon />
                ) : button?.buttonType === "URL" ? (
                  <LaunchIcon />
                ) : button?.buttonType === "QUICK_REPLY" ? (
                  <ReplyAllIcon />
                ) : undefined
              }
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                padding: "8px 12px",
                fontSize: "12px",
                color: "#4CAF50",
                borderBottom: index < buttons.length - 1
                  ? "1px solid #E4E6EB"
                  : "none",
                "&:hover": {
                  backgroundColor: "rgba(76, 175, 80, 0.05)",
                },
              }}
            >
              {button?.buttonType === "PHONE_NUMBER"
                ? button?.buttonName
                : button?.buttonType === "URL"
                ? button?.buttonName
                : button?.buttonType === "QUICK_REPLY"
                ? button?.buttonValue || button?.buttonName
                : button.buttonValue}
            </Button>
          ))}
        </Box>
      )}
    </Box>
  );
};

export default TemplatePreviewLayout;
