import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Grid,
  Tooltip,
  Typography,
} from "@mui/material";
import { makeStyles } from "@mui/styles";
import { useEffect, useState } from "react";
import { bgColors } from "../../../utils/bgColors";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import NewFaqPopup from "../../../components/HelpCenterComponents/NewFaqPopup";
import { useAppDispatch, useAppSelector } from "../../../utils/redux-hooks";
import { fetchHelpCenter } from "../../../redux/slices/HelpCenter/HelpCenterSlice";
import LoadingComponent from "../../../components/common/LoadingComponent";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import HelpIcon from "@mui/icons-material/Help";
import React from "react";
import CommonHeader from "../../../components/common/CommonHeader";

const useStyles = makeStyles({
  mainContainer: {
    backgroundColor: bgColors.white1,
    height: "100vh",
    width: "100%",
  },
  bgContainer: {
    backgroundColor: bgColors.white,
    height: "100%",
  },
  headerContainer: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    borderBottom: "1px solid #f0f0f0",
    width: "100%",
    paddingLeft: "24px",
    paddingRight: "24px",
    paddingTop: "8px",
    paddingBottom: "8px",
  },

  blackColor: {
    color: `${bgColors.black1} !important`,
    fontWeight: "600 !important",
  },
  customParagraph: {
    margin: 0,
  },
  grayColor: {
    color: `${bgColors.gray1} !important`,
    // opacity: "60% !important",
    // fontSize: "20px !important",
  },
  SaveChangesButton: {
    // backgroundColor: bgColors.green,
    border: `1px solid ${bgColors.green}`,
    color: bgColors.green,
    borderRadius: "8px",
    width: "90px",
    height: "32px",
    padding: "6px",
    cursor: "pointer",
  },
});

const HelpCenter = () => {
  const classes = useStyles();
  const dispatch = useAppDispatch();
  const [openDialog, setOpenDialog] = useState(false);
  const [addFaqTooltip, setFaqTooltip] = useState(false);
  // const loginData = useAppSelector((state) => state?.adminLogin?.data);
  const getuserPermissionData = useAppSelector(
    (state: any) => state.getUserPermissions?.data
  );
  const manageCompanyObject = getuserPermissionData?.profile?.find(
    (item: any) => Object?.prototype?.hasOwnProperty?.call(item, "helpCenter")
  );
  const manageCompanyActions = manageCompanyObject
    ? manageCompanyObject?.helpCenter
    : [];

  const hasAccess = (permission: any) => {
    if (manageCompanyActions?.includes(permission)) {
      return true;
    }
    return false;
  };

  const handleOpenDialog = () => {
    const hasPermission = hasAccess("addNewFaq");
    if (hasPermission) {
      setOpenDialog(true);
    } else {
      setFaqTooltip(true);
      setTimeout(() => {
        setFaqTooltip(false);
      }, 2000);
    }
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const helpCenterData = useAppSelector((state: any) => state?.helpCenter);
  const helpCenter = helpCenterData?.data;
  // const helpCenter:any = [];
  const helpCenterStatus = useAppSelector(
    (state: any) => state?.helpCenter?.dataStatus
  );
  useEffect(() => {
    dispatch(fetchHelpCenter());
  }, []);
  const [isHovered, setIsHovered] = useState(false);

  return (
    <Grid
      className={classes.mainContainer}
      sx={{ height: helpCenterStatus !== "loading" ? "auto" : "100%" }}
    >
      {/* {helpCenterStatus === "loading" ? */}
      <Grid className={classes.bgContainer}>
        <Box className={classes.headerContainer}>
          <CommonHeader
            title="Frequently Asked Questions"
            primaryAction={{
              label: "Add FAQ",
              onClick: handleOpenDialog,
              disabled: !hasAccess("addNewFaq"),
            }}
          />

          <NewFaqPopup open={openDialog} handleClose={handleCloseDialog} />
        </Box>
        {helpCenterStatus !== "loading" ? (
          <Box
            mt={2}
            mb={2}
            sx={{
              flex: "1",
            }}
          >
            {helpCenter?.length ? (
              helpCenter !== null &&
              Array.isArray(helpCenter) &&
              helpCenter &&
              helpCenter?.map((faq: any, index: any) => (
                <Box key={index} px={2} py={1}>
                  <Accordion
                    style={{
                      border: `1px solid ${bgColors.gray5}`,
                      borderRadius: "10px",
                    }}
                  >
                    <AccordionSummary
                      expandIcon={<ExpandMoreIcon />}
                      aria-controls={`panel${index}-content`}
                      id={`panel${index}-header`}
                    >
                      <Typography
                        sx={{
                          fontSize: 14,
                          width: "100%",
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "normal",
                          wordBreak: "break-word",
                        }}
                        className={classes.blackColor}
                      >
                        {/* {faq?.question} */}
                        <ReactMarkdown
                          remarkPlugins={[remarkGfm]}
                          components={{
                            p: ({
                              //  node,
                              ...props
                            }) => (
                              <p
                                {...props}
                                className={classes.customParagraph}
                              />
                            ),
                          }}
                          rehypePlugins={[rehypeRaw]}
                        >
                          {faq.question}
                        </ReactMarkdown>
                      </Typography>
                    </AccordionSummary>
                    <AccordionDetails
                      sx={{
                        paddingTop: "0px",
                        lineHeight: 0,
                      }}
                    >
                      <Typography
                        component="div"
                        sx={{
                          fontSize: 14,
                          width: "100%",
                          wordBreak: "break-word",
                        }}
                        className={classes.grayColor}
                      >
                        {/* <div dangerouslySetInnerHTML={{ __html: faq.answer }} /> */}
                        <ReactMarkdown
                          remarkPlugins={[remarkGfm]}
                          rehypePlugins={[rehypeRaw]}
                          components={{
                            p: ({
                              // node,
                              ...props
                            }) => (
                              <p
                                {...props}
                                className={classes.customParagraph}
                              />
                            ),
                          }}
                        >
                          {faq.answer}
                        </ReactMarkdown>
                      </Typography>
                    </AccordionDetails>
                  </Accordion>
                </Box>
              ))
            ) : (
              <>
                <Typography
                  sx={{
                    height: "100%",
                    width: "100%",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  {/* No FAQ's added */}
                  No FAQ&apos;s added
                </Typography>
              </>
            )}
          </Box>
        ) : (
          <LoadingComponent height="100%" color={bgColors.blue} />
        )}
      </Grid>
      {/* : <LoadingComponent height="100%" color={bgColors.blue} />} */}
    </Grid>
  );
};

export default HelpCenter;
