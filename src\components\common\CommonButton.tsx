import { Box, Button, Tooltip } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";

interface CommonButtonProps {
  onLoading?: string | boolean;
  primaryAction?: {
    label: string;
    processingLabel?: string;
    icon?: React.ReactNode | null;
    tooltip?: string;
    disabled?: boolean;
    show?: boolean;
    onClick?: () => void;  
  };
}

const CommonButton = ({ onLoading, primaryAction }: CommonButtonProps) => {
  return (
    <Box sx={{ pr: 2 }}>
      {primaryAction && primaryAction?.show !== false && (
        <Box sx={{ ml: 2}}>
          <Tooltip title={primaryAction.tooltip || ""}>
            <Button
              variant="contained"
              size="small"
              startIcon={!onLoading && primaryAction.icon ? primaryAction.icon : null}
              onClick={primaryAction.onClick}
              disabled={primaryAction.disabled}
              sx={{
                textTransform: "none",
                backgroundColor: "#22c55e",
                "&:hover": { backgroundColor: "#16a34a" },
                whiteSpace: "nowrap",
              }}
            >
              {onLoading ? primaryAction.processingLabel : primaryAction.label}
            </Button>
          </Tooltip>
        </Box>
      )}
    </Box>
  );
};

export default CommonButton;
