import {
  Box,
  FormControl,
  FormHelperText,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  Tooltip,
  Typography,
  Button,
} from "@mui/material";
import React, { useEffect, useRef, useState } from "react";
import { makeStyles } from "@mui/styles";
import InfoIcon from "@mui/icons-material/Info";
import { bgColors } from "../../utils/bgColors";
import TextFieldWithBorderComponent from "../common/TextFieldWithBorderComponent";
import { getWorkflowNames } from "../../redux/slices/Workflows/getWorkflowNamesSlice";
import { toastActions } from "../../utils/toastSlice";
import { useAppDispatch } from "../../utils/redux-hooks";
import WorkflowLibraryPopup from "../../pages/Automation/workflowLibraryPopup";
import { CustomerResponse } from "./EditCampaign";
import WorkflowReactFlow from "../../pages/Automation/WorkflowReactFlow";

const useStyles = makeStyles({
  blackColor: {
    color: "#303030 !important",
    fontWeight: "500 !important",
  },
  variable: {
    color: `${bgColors.green} !important`,
    fontWeight: "500 !important",
    fontSize: "14px !important",
    cursor: "pointer",
  },
  formControl: {
    display: "flex",
    alignItems: "center",
  },
  searchField: {
    width: "100%",
    borderRadius: "12px",
    // height: "38px",
    // backgroundColor: bgColors.white2,
    backgroundColor: "white",
    "& input::placeholder": {
      textAlign: "left",
      fontSize: "14px",
      fontFamily: "inter",
      color: "#000000 !important",
    },
  },
});

interface WorkFlowPopUpCampaignDialogContentProps {
  // selectedButton: string;
  triggerType: any;
  setTriggerType: any;
  selectedButton: any;
  setSelectedButton: any;
  typedReply: string;
  setTypedReply: any;
  campaignState: any;
  setCampaignState: any;
  customReplyLimit: number;
  handleSelectedWorkflow: any;
  userData: any;
  templateData: any;
  error: any;
  setError: any;
  workflowLibraryOpen: any;
  setWorkflowLibraryOpen: any;
  modalSelectedWorkflow: any;
  setModalSelectedWorkflow: any;
}

const WorkFlowPopUpCampaignDialogContent: React.FC<
  WorkFlowPopUpCampaignDialogContentProps
> = ({
  // selectedButton,
  triggerType,
  setTriggerType,
  campaignState,
  setCampaignState,
  typedReply,
  setTypedReply,
  customReplyLimit,
  userData,
  templateData,
  selectedButton,
  setSelectedButton,
  error,
  setError,
  workflowLibraryOpen,
  setWorkflowLibraryOpen,
  modalSelectedWorkflow,
  setModalSelectedWorkflow,
}) => {
  //Dispatches
  const classes = useStyles();
  const dispatch = useAppDispatch();
  const chatAreaRef = useRef<HTMLDivElement>(null);
  const [isOpenWorkflowDialog, setOpenWorkflowDialog] = useState(false);
  const [convertedWorkFlowData, setConvertedWorkFlowData] = useState<any>(null);
  const [isWorkflowEditing, setIsWorkflowEditing] = useState<boolean>(false);

  // all functionalities
  const handleViewWorkflowLibrary = () => {
    setWorkflowLibraryOpen(true);
  };

  const handleWorkLibraryClose = () => {
    if (workflowLibraryOpen) {
      setModalSelectedWorkflow(modalSelectedWorkflow);
    }
    setWorkflowLibraryOpen(false);
  };

  const handleSaveWithWorkflow = (selectedWorkflow: any) => {
    setModalSelectedWorkflow(selectedWorkflow);
  };

  const handleChooseButtonChange = (event: any) => {
    const newValue = event.target.value as string;

    setSelectedButton(newValue);
  };

  const handleTriggerTypeChange = (event: any) => {
    const value = event.target.value;
    setTriggerType(value);

    if (value === CustomerResponse.OnButtonClick) {
      setTypedReply("");
      // setTemplateData((prev:any) => ({
      //   ...prev,
      //   buttons: null,
      // }));
      setError("");
    } else if (value === CustomerResponse.TypedOutReply) {
      // setTemplateData(null);
      setTypedReply("");
      setError("");
    }
  };

  const handleTypedReplyChange = (event: any) => {
    const input = event.target.value.trim();
    const targetWord = campaignState?.autoCustomAutomation?.input;

    const regex = new RegExp(`^${targetWord}$`, "i");

    if (regex.test(input)) {
      setError("Reply should not be the same as the previous input.");
    } else {
      setError("");
    }

    if (input.length <= customReplyLimit) {
      setTypedReply(input);
    }
  };

  const renderButtonOptions = () => {
    if (
      !templateData ||
      !templateData.buttons ||
      templateData.buttons.length === 0
    ) {
      return (
        <MenuItem sx={{ fontSize: { xs: 10, md: 12 } }} disabled>
          No buttons available
        </MenuItem>
      );
    }

    const quickReplyButtons = templateData.buttons.filter(
      (button: any) => button.buttonType === "QUICK_REPLY"
    );

    const uniquebuttons = quickReplyButtons?.filter((uniquebutton: any) => {
      return (
        uniquebutton.buttonValue.toLowerCase() !==
        campaignState?.autoCustomAutomation?.input?.toLowerCase()
      );
    });

    if (uniquebuttons?.length === 0) {
      return (
        <MenuItem sx={{ fontSize: { xs: 10, md: 12 } }} disabled>
          No QUICK_REPLY buttons available
        </MenuItem>
      );
    }

    return uniquebuttons?.map((button: any) => (
      <MenuItem
        key={button.id}
        value={button.buttonValue.toLowerCase()}
        sx={{ fontSize: { xs: 10, md: 12 } }}
      >
        {button.buttonValue}
      </MenuItem>
    ));
  };

  useEffect(() => {
    setTypedReply(campaignState?.workflowAutomation?.input || "");
    setSelectedButton(campaignState?.workflowAutomation?.input || "");
    setTriggerType(campaignState?.workflowAutomation?.customerResponse || 1);
    setModalSelectedWorkflow(campaignState?.workflowAutomation);

    return () => {
      setTypedReply("");
      setSelectedButton("");
      setTriggerType(CustomerResponse.OnButtonClick);
      setModalSelectedWorkflow(null);
    };
  }, [campaignState?.workflowAutomation?.workflowName]);

  useEffect(() => {
    if (campaignState?.autoCustomAutomation?.input !== "") {
      setTriggerType(
        campaignState?.workflowAutomation?.customerResponse ||
          CustomerResponse.OnButtonClick
      );
      setTypedReply(campaignState?.workflowAutomation?.input || "");
      setSelectedButton(campaignState?.workflowAutomation?.input || "");
      setModalSelectedWorkflow(campaignState?.workflowAutomation);
      setError("");
    }

    return () => {
      setTriggerType(CustomerResponse.OnButtonClick);
      setTypedReply("");
      setSelectedButton("");
      setModalSelectedWorkflow(
        campaignState?.workflowAutomation?.workflowName || null
      );
      setError("");
    };
  }, [campaignState?.workflowAutomation?.workflowName]);

  useEffect(() => {
    const fetchWorkflowNames = async () => {
      const payload = {
        companyId: userData?.companyId,
      };

      try {
        const result = await dispatch(getWorkflowNames(payload)).unwrap();
        if (!result.success) {
          dispatch(
            toastActions.setToaster({
              message: result.message || "Failed to fetch workflow names",
              type: "error",
            })
          );
        }
      } catch (error: any) {
        dispatch(
          toastActions.setToaster({
            message: error.message || "Error fetching workflow names",
            type: "error",
          })
        );
      }
    };

    if (userData?.companyId) {
      fetchWorkflowNames();
    }
  }, [userData?.companyId, dispatch]);

  return (
    <Grid
      container
      // justifyContent={{ md: "space-around" }}
      flexDirection={{ xs: "column", md: "row" }}
      gap={{ xs: "20px", md: "0" }}
      ref={chatAreaRef}
      sx={{ p: 2 }}
    >
      <Box display="flex" flexDirection="column">
        <Typography variant="body1" sx={{ mb: 2 }}>
          If customer's response matches, the associated business response will
          be sent to the customer automatically
        </Typography>
        <Typography variant="h6" sx={{ mb: 2, fontWeight: "bold" }}>
          Customer Response
        </Typography>
      </Box>
      <Grid item xs={12} md={6} mt={0}>
        <Box
          display="flex"
          flexDirection="column"
          width="110%"
          mb={{ xs: 1, md: 2 }}
        >
          <Box display="flex">
            <Box width="100%">
              {/* <InputLabel
                id="response-type-label"
                className={classes.blackColor}
                sx={{ fontSize: { xs: 12, md: 14 }, mb: 2 }}
              >
                Choose customer response type*
                <Tooltip
                  title={
                    <>
                      <div>
                        1. Marketing conversation: Business-initiated
                        conversations to market a product or service to
                        customers, such as relevant offers to customers who have
                        opted in.
                      </div>
                      <div>
                        2. Utility conversations: Business-initiated
                        conversations relating to a transaction, including
                        post-purchase notifications and recurring billing
                        statements to customers who have opted in.
                      </div>
                    </>
                  }
                  arrow
                >
                  <IconButton size="small">
                    <InfoIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </InputLabel> */}
              <FormControl fullWidth size="small">
                <InputLabel
                  id="response-type-select-label"
                  sx={{ fontSize: { xs: 12, md: 14 } }}
                >
                  Choose customer response type
                </InputLabel>
                <Select
                  id="response-type-select"
                  labelId="response-type-select-label"
                  value={triggerType}
                  name="Trigger Type"
                  label="Choose customer response type"
                  inputProps={{ style: { fontSize: 14 } }}
                  onChange={handleTriggerTypeChange}
                  sx={{
                    fontSize: 14,
                    borderRadius: "8px",
                    // fontWeight: "600",
                  }}
                >
                  <MenuItem
                    value={CustomerResponse.OnButtonClick}
                    sx={{ fontSize: { xs: 10, md: 12 } }}
                  >
                    On Button Click
                  </MenuItem>
                  <MenuItem
                    value={CustomerResponse.TypedOutReply}
                    sx={{ fontSize: { xs: 10, md: 12 } }}
                  >
                    Typed Out Reply
                  </MenuItem>
                  <MenuItem
                    value={CustomerResponse.AnyCustomerResponse}
                    sx={{ fontSize: { xs: 10, md: 12 } }}
                  >
                    Any Custom Response
                  </MenuItem>
                </Select>
                {/* {formErrors?.category && (
                // <FormHelperText>{formErrors?.category}</FormHelperText>
              )} */}
              </FormControl>
            </Box>

            <Box width="100%" ml={1}>
              {triggerType === CustomerResponse.OnButtonClick && (
                <Box>
                  {/* <InputLabel
                    className={classes.blackColor}
                    sx={{ fontSize: { xs: 12, md: 14 }, mb: 2 }}
                  >
                    Choose Button*
                    <Tooltip
                      title={
                        <>
                          <div>
                            1. Choose relevant language to reduce the
                            probability of rejection for your template.
                          </div>
                        </>
                      }
                      arrow
                    >
                      <IconButton size="small">
                        <InfoIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </InputLabel> */}

                  <FormControl fullWidth size="small">
                    <InputLabel
                      id="button-options-label"
                      sx={{ fontSize: { xs: 12, md: 14 } }}
                    >
                      Choose Button
                    </InputLabel>
                    <Select
                      id="button-select-label"
                      labelId="button-options-label"
                      label="Choose Button"
                      name="Choose Button"
                      value={selectedButton}
                      onChange={handleChooseButtonChange}
                      inputProps={{ style: { fontSize: 14 } }}
                      sx={{
                        fontSize: 14,
                        borderRadius: "8px",
                        // fontWeight: "600",
                      }}
                    >
                      {renderButtonOptions()}
                    </Select>
                    {/* <FormHelperText>
                      {templateData.buttons?.length > 0
                        ? "Select a button from the template"
                        : "No buttons available"}
                    </FormHelperText> */}
                  </FormControl>
                </Box>
              )}

              {triggerType === CustomerResponse.TypedOutReply && (
                <Box width="100%">
                  {/* <InputLabel
                    className={classes.blackColor}
                    sx={{ fontSize: { xs: 12, md: 14 }, mb: 1, mr: 0 }}
                  >
                    Enter Reply*
                    <Tooltip
                      title={
                        <div>
                          E The template name should be unique from the existing
                          templatesedit
                        </div>
                      }
                      arrow
                    >
                      <IconButton size="small">
                        <InfoIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </InputLabel> */}
                  <FormControl
                    sx={{ mt: -1, position: "relative" }}
                    fullWidth
                    size="small"
                  >
                    <TextFieldWithBorderComponent
                      label="Enter Reply"
                      name="Enter Reply"
                      placeholder="Enter Input Here"
                      value={typedReply}
                      onChange={handleTypedReplyChange}
                    />
                    {error && typedReply.length !== 0 && (
                      <FormHelperText error>{error}</FormHelperText>
                    )}

                    <Typography
                      style={{ color: `${bgColors.gray3}` }}
                      variant="body2"
                      sx={{
                        position: "absolute",
                        color:
                          typedReply.length > customReplyLimit
                            ? "error.main"
                            : "inherit",
                        right: 8,
                        top: 8,
                        bottom: error ? 36 : 8,
                        transition: "bottom 0.2s ease-in-out",
                      }}
                    >
                      {`${typedReply.length}/${customReplyLimit}`}
                    </Typography>
                  </FormControl>
                </Box>
              )}
            </Box>
          </Box>
          <Box width="80%" mt={2}>
            {/* Response Label */}
            <Typography
              sx={{
                fontSize: { xs: 14, md: 16 },
                mb: 1,
                fontWeight: "bold",
              }}
            >
              Response*
            </Typography>

            <Box width="120%" gap={2} sx={{ display: "flex" }}>
              {/* View Workflow Library Button */}
              <Button
                variant="outlined"
                onClick={handleViewWorkflowLibrary}
                style={{
                  color: "green",
                  fontWeight: "600",
                  border: "1px solid green",
                  borderRadius: "8px",
                  textTransform: "capitalize",
                  fontSize: "14px",
                  cursor: "pointer",
                  marginTop: "10px",
                }}
              >
                View Workflow Library
              </Button>
              <Button
                variant="outlined"
                onClick={() => setOpenWorkflowDialog(true)}
                style={{
                  color: "green",
                  fontWeight: "600",
                  border: "1px solid green",
                  borderRadius: "8px",
                  textTransform: "capitalize",
                  fontSize: "14px",
                  cursor: "pointer",
                  marginTop: "10px",
                }}
              >
                Add new Workflow
              </Button>
            </Box>
            <Box>
              {modalSelectedWorkflow?.workflowName ? (
                <Typography
                  sx={{
                    fontWeight: 500,
                    fontSize: "16px",
                    color: "black",
                    margin: "8px 0",
                  }}
                >
                  Selected workflow:{" "}
                  <Box
                    component="span"
                    sx={{
                      fontWeight: 600,
                      fontSize: "16px",
                      color: "gray",
                    }}
                  >
                    {modalSelectedWorkflow?.workflowName}
                  </Box>
                </Typography>
              ) : (
                <></>
              )}
            </Box>

            <WorkflowLibraryPopup
              open={workflowLibraryOpen}
              onClose={handleWorkLibraryClose}
              setWorkflowLibraryOpen={setWorkflowLibraryOpen}
              onSave={handleSaveWithWorkflow}
              setSelectedWorkflow={setModalSelectedWorkflow}
              selectedWorkflow={modalSelectedWorkflow}
            />

            <WorkflowReactFlow />
          </Box>
        </Box>
      </Grid>
    </Grid>
  );
};

export default WorkFlowPopUpCampaignDialogContent;
