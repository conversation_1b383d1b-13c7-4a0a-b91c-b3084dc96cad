import {
  <PERSON>,
  Card,
  CardContent,
  Grid,
  <PERSON><PERSON><PERSON><PERSON>on,
  Tooltip,
  Typography,
} from "@mui/material";
import { makeStyles } from "@mui/styles";
import React, { useCallback, useEffect, useState } from "react";
import { bgColors } from "../../utils/bgColors";
import CampaignFilterPopOvers from "../../components/CampaingnsComponents/CampaignFilterPopovers";
import DeletePopUp from "../../components/common/DeletePopup";
import EditIconSvg from "../../assets/svgs/EditIconSvg";
import DeleteIconSvg from "../../assets/svgs/DeleteIconSvg";
import { useAppDispatch, useAppSelector } from "../../utils/redux-hooks";
import { CAMPAIGN_API } from "../../Apis/Campaign/Campaign";
import { getScheduledCampaign } from "../../redux/slices/Campaign/GetScheduledCampaignSlice";
import { toastActions } from "../../utils/toastSlice";
import { checkSceduledCampaignsPermission } from "../../utils/permissions";
import NoAccessPage from "../../components/common/NoAccess";
import { getCampaignById } from "../../redux/slices/Campaign/GetCampaignByIdSlice";
import { CampaignStatusEnum } from "./Campaigns";
import { getCampaignCount } from "../../redux/slices/Campaign/GetCampaignCountSlice";
import CommonTable, { TableColumn } from "../../components/common/CommonTable";
import { Add } from "@mui/icons-material";
import { debounce } from "lodash";
import EditCampaign from "../../components/ScheduledComponents/EditCampaign";

const useStyles = makeStyles({
  mainContainer: {
    backgroundColor: bgColors.white1,
    height: "100vh",
    width: "100%",
    overFlow: "hidden !important",
  },
  searchField: {
    width: "90%",
    backgroundColor: "white",
    "& input::placeholder": {
      textAlign: "left",
      fontSize: "14px",
      fontFamily: "inter",
      color: "#000000 !important",
    },
  },
  bgContainer: {
    backgroundColor: bgColors.white,

    height: "100%",
    width: "100%",
    overFlow: "hidden !important",
    display: "flex",
    flexDirection: "column",
  },
  manageTeamContainer: {
    display: "flex",

    width: "full",
  },
  blackColor: {
    color: `${bgColors.black1} !important`,
    // fontWeight: "600 !important",
  },
  SaveChangesButton: {
    color: bgColors.green,
    border: `1px solid ${bgColors.green}`,
    // backgroundColor: bgColors.green,
    borderRadius: "8px",
    width: "120px",
    height: "32px",
    // padding: "10px",
    cursor: "pointer",
  },
  messageInnerContainer: {
    border: "2px solid #F2F2F2",
    borderRadius: "6px",
    paddingInline: "4px",
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  grayColor: {
    color: "#303030",
    opacity: "60%",
    fontSize: "20px",
    // padding:"5px"
  },
  spaceBetween: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  iconStyles: {
    cursor: "pointer",
    paddingLeft: "5px",
  },
  table: {
    minWidth: "500px",

    overflow: "auto",
    // borderCollapse: "separate",
    // borderSpacing: "0",
    textAlign: "center",
    borderColor: "lightgray",
    "& th, & td": {
      // borderTop: '1px solid gray',
      borderBottom: "1px solid #f0f0f0",
      height: "35.8px",
    },
    "& th:first-child, & td:first-child": {
      borderLeft: "none",
    },
    "& th:last-child, & td:last-child": {
      borderRight: "none",
    },
  },
  teamProfileContainer: {
    display: "flex",
    alignItems: "center",
  },
  threedots: {
    border: "2px solid #F2F2F2",
    padding: "10px",
    borderRadius: "12px",
    // paddingBottom: 0,
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  },
  editButtonContainer: {
    border: "2px solid #DBDBDB",
    padding: "8px",
    borderRadius: "12px",
    backgroundColor: "#F4F4F4",
    width: "50px",
    // paddingBottom: 0,
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    cursor: "pointer",
  },
});

const options = [
  {
    id: "1",
    option: "allschedule",
  },
  {
    id: "2",
    option: "next7days",
  },
  {
    id: "3",
    option: "next14days",
  },
  {
    id: "4",
    option: "next30days",
  },
];

const options1 = [
  {
    id: "1",
    option: "All",
  },
  {
    id: "2",
    option: "next7days",
  },
  {
    id: "3",
    option: "next14days",
  },
  {
    id: "4",
    option: "next30days",
  },
];

const Scheduled = () => {
  const classes = useStyles();
  const dispatch = useAppDispatch();
  const userInfoSlice = useAppSelector((state: any) => state.adminLogin);
  const accountInfo = useAppSelector((state: any) => state?.accountData?.data);
  const userInfo = userInfoSlice?.data;
  const getuserPermissionData = useAppSelector(
    (state: any) => state.getUserPermissions?.data
  );
  const getMc = getuserPermissionData?.campaigns;
  const scheduledObject = getMc?.find((item: any) =>
    Object?.prototype?.hasOwnProperty?.call(item, "scheduled")
  );
  const scheduledActions = scheduledObject ? scheduledObject.scheduled : [];
  const getCampaignSlice = useAppSelector(
    (state: any) => state.getScheduledCampaign
  );
  const getCampaignData = getCampaignSlice?.data?.data;
  const hasScheduledCampaignsPermission =
    checkSceduledCampaignsPermission(getMc);
  const campaignsCount = useAppSelector(
    (state: any) => state?.getCampaignCount?.data?.data
  );
  const [isDeleteLoading, setIsDeleteLoading] = useState(false);
  const [searchCampaignQuery, setSearchCampaignQuery] = useState<string>("");

  const [page, setPage] = React.useState(1);
  const [anchorEl1, setAnchorEl1] = React.useState(null);
  const [selectedFilter1, setSelectedFilter1] = React.useState({
    id: "2",
    value: "next7days",
  });

  const [openEditDialog, setOpenEditDialog] = React.useState(false);

  const [deleteDialog, setDeleteDialog] = React.useState(false);
  const [title, setTitle] = useState("");
  const [addNewCampaignTooltip, setAddNewCampaignTooltip] = useState(false);
  const [editScheduledCampaignTooltip, setEditScheduledCampaignTooltip] =
    useState("");

  const [deleteScheduledCampaignTooltip, setDeleteScheduledCampaignTooltip] =
    useState("");
  const [scheduledCampaignTooltipMesage, setScheduledCampaignTooltipMesage] =
    useState("");
  const [editData, setEditData] = useState<any>(null);
  const [getCampaignId, setGetCampaignId] = useState<any>(null);
  const [pageData, setPageData] = useState(getCampaignData || []);
  const [rowsPerPage] = useState(20);

  const hasAcess = (permission: any) => {
    if (scheduledActions?.includes(permission)) {
      return true;
    }
    return false;
  };

  const debouncedFetchCampaignCount = useCallback(
    debounce(async (data) => {
      dispatch(getCampaignCount(data));
    }, 500),
    []
  );
  const debouncedFetchCampaignData = useCallback(
    debounce(async (data) => {
      dispatch(getScheduledCampaign(data));
    }, 500),
    []
  );

  const handleDeleteDialog = (campaignId: string, createdBy: string) => {
    const hasPermission =
      userInfo?.roleName === "Owner" || userInfo?.roleName === "Admin"
        ? hasAcess("deleteCampaign")
        : hasAcess("deleteCampaign") && createdBy === accountInfo?.name;
    if (hasPermission) {
      setDeleteDialog(true);
      setGetCampaignId(campaignId);
    } else {
      if (createdBy !== accountInfo?.name) {
        setScheduledCampaignTooltipMesage("You are not authorized");
      } else {
        setScheduledCampaignTooltipMesage("Access Denied");
      }
      setDeleteScheduledCampaignTooltip(campaignId);
      setTimeout(() => {
        setDeleteScheduledCampaignTooltip("");
      }, 2000);
    }
  };

  const handleDeleteCloseDialog = () => {
    setDeleteDialog(false);
  };
  const handleClose1 = () => {
    setAnchorEl1(null);
  };
  const handleFilterClick1 = (event: any) => {
    setAnchorEl1(event.currentTarget);
  };
  const handleOptionClick = (option: string) => {
    const value = option.toLowerCase().replace(/\s+/g, "");

    const formattedOption = {
      id: options.find((item) => item.option === value)?.id || "1",
      value: value === "all" ? "allschedule" : value,
    };
    if (formattedOption.id === selectedFilter1.id) {
      handleClose1();
      return;
    }
    setPageData([]);
    setPage(1);
    setSelectedFilter1(formattedOption);
    handleClose1();
  };

  const handleOpenEditDialog = (title: any) => {
    const hasPermission = hasAcess("newScheduledCampaign");
    setTitle(title);
    if (hasPermission) {
      setOpenEditDialog(true);
    } else {
      setAddNewCampaignTooltip(true);
      setTimeout(() => {
        setAddNewCampaignTooltip(false);
      }, 2000);
    }
  };
  const handleCloseEditDialog = () => {
    setOpenEditDialog(false);
    setEditData(null);
  };
  const handleEdit = async (client: any, id: any, createdBy: string) => {
    const hasPermission =
      userInfo?.roleName === "Owner" || userInfo?.roleName === "Admin"
        ? hasAcess("editCampaign")
        : hasAcess("editCampaign") && createdBy === accountInfo?.name;
    if (hasPermission) {
      handleOpenEditDialog(client);
      try {
        const data = {
          businessId: userInfo?.companyId,
          userId: userInfo?.userId,
          campaignId: id,
        };

        const getDetailsRes = await dispatch(getCampaignById(data));

        setEditData(getDetailsRes?.payload?.data);
      } catch (error: any) {}
    } else {
      if (createdBy !== accountInfo?.name) {
        setScheduledCampaignTooltipMesage("You are not authorized");
      } else {
        setScheduledCampaignTooltipMesage("Access Denied");
      }
      setEditScheduledCampaignTooltip(id);
      setTimeout(() => {
        setEditScheduledCampaignTooltip("");
      }, 2000);
    }
  };

  const handleDelete = async () => {
    setIsDeleteLoading(true);
    try {
      const data = {
        businessId: userInfo?.companyId,
        userId: userInfo?.userId,
        campaignId: getCampaignId,
      };
      const getDeleteRes = await CAMPAIGN_API.deleteScheduleCampaign(data);
      if (getDeleteRes?.status === 200) {
        setDeleteDialog(false);
        const campaignData = {
          page: page,
          perPage: 20,
          filters: {
            searching: {
              column: "campaignTitle",
              value:
                searchCampaignQuery?.length > 0
                  ? searchCampaignQuery
                  : undefined,
            },
            filtering: {
              filterType: "and",
              conditions: [
                {
                  column: "state",
                  operator: "=",
                  value: CampaignStatusEnum.Scheduled,
                },
                {
                  column: "dateSetLive",
                  operator:
                    selectedFilter1.value.toLowerCase() === "all"
                      ? "allschedule"
                      : selectedFilter1.value.toLowerCase(),
                  value: new Date().toISOString().slice(0, 10),
                },
              ],
            },
          },
        };
        const campaignCountData = {
          status: CampaignStatusEnum.Scheduled,
          filters: {
            Condition: {
              Column: "dateSetLive",
              Operator:
                selectedFilter1.value.toLowerCase() === "all"
                  ? "allschedule"
                  : selectedFilter1.value.toLowerCase(),
              Value: new Date().toISOString().slice(0, 10),
            },
            Search: {
              Column: "campaignTitle",
              Value:
                searchCampaignQuery?.length > 0
                  ? searchCampaignQuery
                  : undefined,
            },
          },
        };
        debouncedFetchCampaignData(campaignData);
        debouncedFetchCampaignCount(campaignCountData);
        dispatch(
          toastActions.setToaster({
            type: "success",
            message: `${getDeleteRes?.data?.message}`,
          })
        );
      } else {
        dispatch(
          toastActions.setToaster({
            type: "error",
            message: `${getDeleteRes?.data?.message}`,
          })
        );
        setDeleteDialog(false);
      }
    } catch (error) {}
    setIsDeleteLoading(false);
  };
  const handlePageChange = (event: any, value: number) => {
    setPage(value);
  };

  useEffect(() => {
    const data = {
      status: CampaignStatusEnum.Scheduled,
      filters: {
        Condition: {
          Column: "dateSetLive",
          Operator:
            selectedFilter1.value.toLowerCase() === "all"
              ? "allschedule"
              : selectedFilter1.value.toLowerCase(),
          Value: new Date().toISOString().slice(0, 10),
        },
        Search: {
          Column: "campaignTitle",
          Value:
            searchCampaignQuery?.length > 0 ? searchCampaignQuery : undefined,
        },
      },
    };
    debouncedFetchCampaignCount(data);
  }, [selectedFilter1, searchCampaignQuery]);

  useEffect(() => {
    const campaignData = {
      page: page,
      perPage: 20,
      filters: {
        searching: {
          column: "campaignTitle",
          value:
            searchCampaignQuery?.length > 0 ? searchCampaignQuery : undefined,
        },
        filtering: {
          filterType: "and",
          conditions: [
            {
              column: "state",
              operator: "=",
              value: CampaignStatusEnum.Scheduled,
            },
            {
              column: "dateSetLive",
              operator:
                selectedFilter1.value.toLowerCase() === "all"
                  ? "allschedule"
                  : selectedFilter1.value.toLowerCase(),
              value: new Date().toISOString().slice(0, 10),
            },
          ],
        },
      },
    };
    debouncedFetchCampaignData(campaignData);
  }, [selectedFilter1, searchCampaignQuery, page]);

  const formatDate = (datetime: any) => {
    const date = new Date(datetime);
    // Convert UTC to IST by adding 5 hours and 30 minutes
    date.setHours(date.getHours() + 5);
    date.setMinutes(date.getMinutes() + 30);

    const year = date.getFullYear();
    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];
    const month = monthNames[date.getMonth()];
    const day = ("0" + date.getDate()).slice(-2);
    const hours = ("0" + date.getHours()).slice(-2);
    const minutes = ("0" + date.getMinutes()).slice(-2);
    const seconds = ("0" + date.getSeconds()).slice(-2);
    return `${day} ${month} ${year} ${hours}:${minutes}:${seconds}`;
  };

  useEffect(() => {
    if (getCampaignData) {
      setPageData(getCampaignData);
    }
  }, [getCampaignData]);

  // Define the columns for the Scheduled campaigns table
  const columns: TableColumn[] = [
    {
      id: "campaignTitle",
      label: "Title",
      width: "250px",
      format: (value: any) => (
        <Tooltip title={value} placement="top">
          <Box
            sx={{
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
              maxWidth: "250px",
              paddingLeft: "20px",
            }}
          >
            {value}
          </Box>
        </Tooltip>
      ),
    },
    {
      id: "createdby",
      label: "Created by",
      width: "150px",
    },
    {
      id: "dateSetLive",
      label: "Date Set Live",
      width: "150px",
      format: (value: any) => formatDate(value),
    },
  ];

  // Define the actions renderer for the table
  const renderActions = (row: any) => {
    return (
      <Box
        sx={{
          display: "flex",
          gap: 1,
          justifyContent: "flex-start",
          alignItems: "center",
        }}
      >
        {row?.state === 3 && (
          <Tooltip title="Edit">
            <Box
              onClick={() =>
                handleEdit("Edit", row?.campaignId, row?.createdby)
              }
              sx={{
                cursor: "pointer",
                display: "flex",
                alignItems: "center",
              }}
            >
              <EditIconSvg />
            </Box>
          </Tooltip>
        )}
        <Tooltip title="Delete">
          <Box
            onClick={() => handleDeleteDialog(row?.campaignId, row?.createdby)}
            sx={{
              cursor: "pointer",
              display: "flex",
              alignItems: "center",
            }}
          >
            <DeleteIconSvg />
          </Box>
        </Tooltip>
      </Box>
    );
  };

  // Define the mobile renderer
  const renderScheduledMobile = (row: any) => {
    return (
      <Card sx={{ m: 1, p: 2 }}>
        <Box sx={{ display: "flex", justifyContent: "space-between", mb: 1 }}>
          <Typography
            variant="subtitle1"
            sx={{
              fontWeight: 500,
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
              maxWidth: "200px",
            }}
          >
            {row.campaignTitle}
          </Typography>
          <Box sx={{ display: "flex", gap: 1 }}>
            {row?.state === 3 && (
              <IconButton
                size="small"
                onClick={() =>
                  handleEdit("Edit", row?.campaignId, row?.createdby)
                }
              >
                <EditIconSvg />
              </IconButton>
            )}
            <IconButton
              size="small"
              onClick={() =>
                handleDeleteDialog(row?.campaignId, row?.createdby)
              }
            >
              <DeleteIconSvg />
            </IconButton>
          </Box>
        </Box>

        <CardContent sx={{ pt: 0, pb: "8px !important" }}>
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <Typography variant="caption" color="text.secondary">
                Created by
              </Typography>
              <Typography variant="body2">{row.createdby}</Typography>
            </Grid>
            <Grid item xs={6}>
              <Typography variant="caption" color="text.secondary">
                Date Set Live
              </Typography>
              <Typography variant="body2">
                {formatDate(row.dateSetLive)}
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    );
  };

  return (
    <>
      {hasScheduledCampaignsPermission ? (
        <Grid sx={{ height: "100%" }}>
          <Grid className={classes.mainContainer}>
            <CommonTable
              columns={columns}
              data={pageData}
              rowIdKey="campaignId"
              title="Scheduled Campaigns"
              count={campaignsCount}
              isLoading={getCampaignSlice?.status === "loading"}
              actions={renderActions}
              renderOnMobile={renderScheduledMobile}
              showPagination={true}
              page={page}
              onPageChange={handlePageChange}
              totalPages={Math.ceil(campaignsCount / rowsPerPage)}
              searchProps={{
                value: searchCampaignQuery,
                onChange: setSearchCampaignQuery,
                placeholder: "Search scheduled campaigns...",
              }}
              primaryAction={{
                label: "Add Campaign",
                onClick: () => handleOpenEditDialog("Add"),
                icon: <Add />,
                show: hasAcess("newScheduledCampaign"),
              }}
              selectedMainFilter={selectedFilter1}
              handleMainFilter={handleFilterClick1}
              perPage={rowsPerPage}
            />
          </Grid>
          <DeletePopUp
            title="Schedule Campaign"
            open={deleteDialog}
            handleClose={handleDeleteCloseDialog}
            handleDelete={handleDelete}
            handleLoad={isDeleteLoading}
          />
          <EditCampaign
            open={openEditDialog}
            title={title}
            handleClose={handleCloseEditDialog}
            data={title === "Edit" ? editData : null}
            searchCampaignQuery={searchCampaignQuery}
            selectedFilter1={selectedFilter1}
          />

          <CampaignFilterPopOvers
            anchorEl={anchorEl1}
            handleClose={handleClose1}
            options={options1}
            handleOptionClick={handleOptionClick}
          />
        </Grid>
      ) : (
        <NoAccessPage />
      )}
    </>
  );
};

export default Scheduled;
