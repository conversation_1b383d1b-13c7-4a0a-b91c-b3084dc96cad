import { RefObject } from "react";

//templateform interfaces
export interface ButtonType {
  buttonType: string;
  buttonValue: any;
  countryCode: string;
  buttonName?: string;
}

export interface TemplateData {
  businessId: any;
  userId: any;
  language: number;
  // mediaFile: string;
  redirectUrl: string[];
  urlButtonName: string[];
  quickReply: string[];
  callButtonName?: string;
  phoneNumber?: string;
  countryCode?: string;
  templateId?: string;
  draft?: boolean;
}
export interface TemplateState {
  templateName: string;
  category: number;
  language: number;
  subCategory: string;
  mediaType: number;
  mediaFile: any;
  header: string;
  body: string;
  callButtonName: string;
  phoneNumber: string;
  countryCode: string;
  urlButtonName: string[];
  redirectUrl: string[];
  quickReply: string[];
  footer: string;
  codeDeliverySetup: string;
  zeroTapAuthentication: boolean;
  addSecurityRecommendation: boolean;
  addExpiryTime: boolean;
  codeExpiresIn: number;
  autoFillButtonText: string;
  copyCodeButtonText: string;
  messageValidity: boolean;
  messageValidityPeriod: number;

  appSetup: Array<{
    appPackageName: string;
    appSignatureHash: string;
  }>;
  leadratVariables: Array<{
    type: string;
    id: string;
    value: string;
    field: string;
    fallBackValue: string;
  }>;
  buttons: {
    buttonType: string;
    buttonValue: string;
    countryCode: string;
    buttonName?: any; // Optional property
  }[];
  variables: Array<{
    type: string;
    id: string;
    value: string;
    field: string;
    fallBackValue: string;
  }>;
  carouselCards?: Array<{
    id: string;
    mediaUrlType: number;
    headerMediaUrl: string;
    body: string;
    carouselButtons: Array<{
      buttonType: string;
      buttonValue: string;
      countryCode: string;
      buttonName?: any;
    }>;
  }>;
}

export interface FormErrors {
  [key: string]: string;
}

// actionButtons interfaces
export interface ButtonComponentProps {
  formErrors: any;
  buttonTypesList: any;
  buttonType: string;
  buttonValue: string;
  countryCode: string;
  index: number;
  buttonName?: string; // Optional property
  phoneNumberCount?: number;
  urlsCount?: number;
  repliesCount?: number;
  onRemoveButton: () => void;
  updateButton: (updatedButton: {
    buttonType: string;
    buttonValue: string;
    countryCode: string;
    buttonName?: string;
  }) => void;
  handleAddVariable: (
    type: string,
    urlButtonIndex?: number,
    buttonIndex?: number
  ) => void;
  urlButtonVariablesCount: any;
  urlButtonRef: RefObject<HTMLInputElement>;
  templateState: TemplateState;
  setTemplateState: any;
  isUrlDynamic: Boolean;
  selectStyles: any;
  menuItemStyles: any;
  muiSelectNoBlue: any;
}

export interface TemplateDialogContentProps {
  canEdit?: boolean;
  templateData: any; // Assuming TemplateState is defined elsewhere
  templateNameSlice: any; // Assuming TemplateState is defined elsewhere
  setTemplateState: any;
  templateState: TemplateState; // Assuming TemplateState is defined elsewhere
  formErrors: Record<string, string>;
  setFormErrors: any;
  handleChange: (event: any) => void;
  handleMediaSelectionChange: (event: any) => void;
  handleHeaderMediaChange: (file: string | null, event: File | null) => void;
  handleEmojiClick: (event: any) => void;
  handleEmojiSelect: (emoji: string) => void;
  handleCloseEmojiPopover: () => void;
  handleAddVariable: (
    type: string,
    urlButtonIndex?: number,
    buttonIndex?: number
  ) => void;
  emojiPopoverOpen: boolean;
  anchorEl: HTMLDivElement | null;
  bodyRef: RefObject<HTMLTextAreaElement>;
  headerRef: RefObject<HTMLInputElement>;
  urlButtonRef: RefObject<HTMLInputElement>;
  maxLength: number;
  phoneNumberButtonCount: number;
  urlButtonCount: number;
  replyButtonCount: number;

  addButton: () => void;
  removeButton: (index: number) => void;
  updateButton: (index: number, updatedButton: ButtonType) => void;
  buttonsArray: { value: string; label: string }[];
}

export interface ICarousel {
  carouselId: string;
  currentCarousel: any;
  carouselIndex: number;
  templateState: any;
  setTemplateState: any;
  predefinedVariables: Array<string>;
  selectedVariable: string;
  setSelectedVariable: any;
  formErrors: any;
  handleRemoveCarousel: (carouselId: string) => void;
  handleCarouselFileChange: any;
  setFormErrors: any;
  selectStyles: any;
  menuItemStyles: any;
  muiSelectNoBlue: any;
}
