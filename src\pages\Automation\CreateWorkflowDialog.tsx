import React, { useEffect, useState } from "react";
import {
  Box,
  CircularProgress,
  Dialog,
  Grid,
  IconButton,
  Tooltip,
  Typography,
} from "@mui/material";

import TextFieldWithBorderComponent from "../../components/common/TextFieldWithBorderComponent";
import { makeStyles } from "@mui/styles";
import { bgColors } from "../../utils/bgColors";
import { useDispatch } from "react-redux";
import { useAppDispatch, useAppSelector } from "../../utils/redux-hooks";
import { LuWorkflow } from "react-icons/lu";
import CloseIcon from "@mui/icons-material/Close";
import ButtonComponent from "../../components/common/ButtonComponent";
import { useNavigate } from "react-router-dom";
import { createWorkflow } from "../../redux/slices/Workflows/createWorkflowSlice";
import {
  createWorkflowReactflow,
  resetCreateWorkflowReactflow,
} from "../../redux/slices/Workflows/createWorkflowReactflowSlice";

import LoadingComponent from "../../components/common/LoadingComponent";
import { toastActions } from "../../utils/toastSlice";
import CancelDialogPopup from "../../components/common/CancelDialogPop";
import { getKeywords } from "../../redux/slices/Workflows/getKeywordsSlice";

const useStyles = makeStyles({
  mainContainer: {
    backgroundColor: bgColors.white,
    borderRadius: "25px",
    marginTop: "20px",
    width: "100%",
    height: "100%",
  },
  chatArea: {
    padding: "20px",
  },
  bgContainer: {
    backgroundColor: bgColors.white,
    borderRadius: "25px",
    height: "100%",
    width: "100%",
  },
  manageContainer: {
    display: "flex",

    justifyContent: "space-between",
  },
  blackColor: {
    color: `${bgColors.black1} !important`,
    fontWeight: "600 !important",
  },
  SaveChangesButton: {
    color: bgColors.green,
    border: `1px solid ${bgColors.green}`,
    borderRadius: "8px",
    width: "140px",
    height: "32px",
    cursor: "pointer",
  },
  textColor: {
    color: bgColors.gray1,
    fontSize: "16px",
  },
  templateBox: {
    backgroundColor: "#f5f5f5",
    borderRadius: "10px",
    padding: "20px",
    marginTop: "20px",
    borderLeft: `4px solid ${bgColors.green}`,
  },
  headerText: {
    fontWeight: "600 !important",
    marginBottom: "10px !important",
    fontSize: "15px",
  },
  tableContainer: {
    marginTop: "10px",
    boxShadow: "none",
    backgroundColor: "transparent",
  },
  table: {
    // borderCollapse: "separate",
    borderSpacing: "0",
    textAlign: "center",
    borderColor: "lightgray",
    "& th, & td": {
      borderBottom: "1px solid #f0f0f0",
      padding: "4px",
    },
    "& th:first-child, & td:first-child": {
      borderLeft: "none",
      // paddingLeft:'4px'
    },
    "& th:last-child, & td:last-child": {
      borderRight: "none",
    },
  },
  tableRow: {
    transition: "box-shadow 0.3s ease",
    "&:hover": {
      boxShadow:
        "0px 3px 5px -1px rgba(0,0,0,0.2), 0px 6px 10px 0px rgba(0,0,0,0.14), 0px 1px 18px 0px rgba(0,0,0,0.12)",
    },
  },
  grayText: {
    color: bgColors.gray1,
  },
  workflowsCreatedBox: {
    marginTop: "30px",
  },
  workflowItem: {
    backgroundColor: "#fff",
    borderRadius: "5px",
    padding: "10px",
    marginBottom: "10px",
  },
  overlayLoader: {
    position: "fixed",
    top: 0,
    left: 0,
    width: "100%",
    height: "100%",
    backgroundColor: "rgba(255, 255, 255, 0.7)",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    zIndex: 9999,
  },
  button: {
    backgroundColor: `${bgColors.green}!important`,
    margin: "0 auto",
    width: "95% !important",
    fontSize: "14px !important",
    fontWeight: "Semi Bold !important",
    borderRadius: "8px !important",
    marginTop: "16px",
  },
});

const CreateWorkflowDialog = ({ open, setOpen }: any) => {
  const classes = useStyles();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  const createWorkflowReactflowSlice = useAppSelector(
    (state: any) => state?.createWorkflowReactFlow
  );

  const [searchInput, setSearchInput] = useState("");

  const handleClose = () => {
    setOpen(false);
    setSearchInput("");
    dispatch(resetCreateWorkflowReactflow());
  };
  const handleCreate = async () => {
    const payload = {
      name: searchInput,
    };

    let apiResponse;
    try {
      apiResponse = await dispatch(createWorkflowReactflow(payload));
      if (apiResponse?.meta?.requestStatus === "fulfilled") {
        const responseData = apiResponse?.payload?.data;
        const workflowId = responseData?.id || 1;
        const workflowName = responseData?.name || "default workflow";

        dispatch(
          toastActions.setToaster({
            message:
              apiResponse?.payload?.message || "Workflow created successfully",
            type: "success",
          })
        );
        navigate(`/automation/workflows/${workflowId}`);
      } else {
        dispatch(
          toastActions.setToaster({
            message:
              apiResponse?.payload?.data?.message ||
              "Failed to create workflow",
            type: "error",
          })
        );
      }
    } catch (error) {
      dispatch(
        toastActions.setToaster({
          message:
            apiResponse?.payload?.data?.message || "Failed to create workflow",
          type: "error",
        })
      );
    }
  };

  const handleSearchChange = (e: any) => {
    const value = e.target.value;
    setSearchInput(value);
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="lg"
      fullWidth
      sx={{
        width: { xs: "95%", sm: "80%", md: "50%" },
        margin: "0 auto",
        borderRadius: "16px",
        "& .MuiPaper-root": {
          borderRadius: "16px",
          // overflow: "scroll",
        },
        height: "100%",
      }}
    >
      <Box
        className={classes.manageContainer}
        sx={{
          flexDirection: "row",
          alignItems: "baseline",
          justifyContent: "space-between",
        }}
        mb={1}
      >
        <Box ml={4} mt={2}>
          <Typography
            variant="h6"
            className={classes.blackColor}
            sx={{ display: "flex", alignItems: "center" }}
          >
            <LuWorkflow />
            &nbsp; Create a New Workflow
          </Typography>
        </Box>
        <IconButton
          aria-label="close"
          sx={{ color: "black" }}
          onClick={handleClose}
        >
          <CloseIcon />
        </IconButton>
      </Box>
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          mb: 1,
        }}
      >
        <TextFieldWithBorderComponent
          autoFocus
          label="Workflow name"
          name="Workflow"
          placeholder="Enter the Name of the Workflow"
          size="small"
          fullWidth
          value={searchInput}
          onChange={handleSearchChange}
          onKeyDown={(e) => {
            if (e.key === "Enter" && searchInput.trim()) {
              e.preventDefault();
              handleCreate();
            }
          }}
          fontweight="bold"
          sx={{
            px: 2,
          }}
          InputLabelProps={{
            sx: {
              left: "16px",
              fontSize: "14px",
            },
          }}
        />
        <Typography
          variant="caption"
          sx={{
            px: 2,
            mt: 0.5,
            color: "#ff9800",
            fontSize: "12px",
            fontStyle: "italic",
          }}
        >
          Note: Workflow name cannot be edited or changed after creation
        </Typography>
        {createWorkflowReactflowSlice?.createWorkflowReactflowStatus ===
        "loading" ? (
          <LoadingComponent color={bgColors.blue3} height="50px" />
        ) : (
          <ButtonComponent
            onClick={handleCreate}
            title="Create Workflow"
            className={classes.button}
            type="submit"
            disabled={!searchInput}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                e.preventDefault();
                handleCreate();
              }
            }}
            sx={{
              px: 2,
              fontWeight: "bold",
              "&.Mui-disabled": {
                backgroundColor: `${bgColors.gray2}!important`,
                color: `${bgColors.gray3} !important`,
                fontWeight: "normal",
                // border: "1px solid #ccc !important",
              },
            }}
          />
        )}
      </Box>
    </Dialog>
  );
};

export default CreateWorkflowDialog;
