import { jwtDecode } from "jwt-decode";
import CryptoJ<PERSON> from "crypto-js";
import { ADMIN_LOGIN_API } from "../Apis/AdminLogin/AdminLoginApi";
import axios, { InternalAxiosRequestConfig } from "axios";

const TOKEN_STORAGE_KEY = "token";
const REFRESH_TOKEN_STORAGE_KEY = "refreshToken";

// Secret key for encrypting tokens in local storage
const ENCRYPTION_KEY =
  process.env.REACT_APP_TOKEN_ENCRYPTION_KEY || "default-secure-key";

// Interface for decoded JWT Token
export interface DecodedToken {
  sub: string; // subject (user id)
  email: string;
  role: string;
  permissions: string[];
  companyId: string;
  exp: number; // expiration timestamp
  iat: number; // issued at timestamp
}

// Interface for stored tokens
export interface StoredTokens {
  token: string;
  refreshToken: string;
}

// Store pending requests that should be retried after token refresh
let pendingRequests: Array<{
  config: InternalAxiosRequestConfig;
  resolve: (value: any) => void;
  reject: (reason: any) => void;
}> = [];

// Encrypt data for storage
const encryptData = (data: string): string => {
  return CryptoJS.AES.encrypt(data, ENCRYPTION_KEY).toString();
};

// Decrypt data from storage
const decryptData = (encryptedData: string): string => {
  try {
    const bytes = CryptoJS.AES.decrypt(encryptedData, ENCRYPTION_KEY);
    return bytes.toString(CryptoJS.enc.Utf8);
  } catch (error) {
    console.error("Error decrypting data:", error);
    return "";
  }
};

// Store tokens securely
export const storeTokens = (tokens: StoredTokens): void => {
  try {
    // Encrypt tokens before storing
    const encryptedToken = encryptData(tokens.token);
    const encryptedRefreshToken = encryptData(tokens.refreshToken);

    // Store encrypted tokens
    localStorage.setItem(TOKEN_STORAGE_KEY, `Bearer ${encryptedToken}`);
    localStorage.setItem(REFRESH_TOKEN_STORAGE_KEY, encryptedRefreshToken);
  } catch (error) {
    console.error("Error storing tokens:", error);
  }
};

// Retrieve tokens from storage
export const getStoredTokens = (): StoredTokens | null => {
  try {
    const encryptedToken = localStorage.getItem(TOKEN_STORAGE_KEY);
    const encryptedRefreshToken = localStorage.getItem(
      REFRESH_TOKEN_STORAGE_KEY
    );

    if (!encryptedToken || !encryptedRefreshToken) return null;

    // Remove 'Bearer ' prefix and decrypt
    const cleanEncryptedToken = encryptedToken.replace("Bearer ", "");
    const token = decryptData(cleanEncryptedToken);
    const refreshToken = decryptData(encryptedRefreshToken);

    if (!token || !refreshToken) return null;

    return {
      token,
      refreshToken,
    };
  } catch (error) {
    console.error("Error retrieving tokens:", error);
    return null;
  }
};

// Clear tokens from storage
export const clearTokens = (): void => {
  localStorage.removeItem(TOKEN_STORAGE_KEY);
  localStorage.removeItem(REFRESH_TOKEN_STORAGE_KEY);
};

// Store refresh token
export const storeRefreshToken = (refreshToken: string): void => {
  const encryptedRefreshToken = encryptData(refreshToken);
  localStorage.setItem(REFRESH_TOKEN_STORAGE_KEY, encryptedRefreshToken);
};

// Get stored refresh token
export const getStoredRefreshToken = (): string | null => {
  const encryptedRefreshToken = localStorage.getItem(REFRESH_TOKEN_STORAGE_KEY);
  if (!encryptedRefreshToken) return null;
  return decryptData(encryptedRefreshToken);
};

// Decode token
export const decodeToken = (token: string): DecodedToken | null => {
  try {
    return jwtDecode<DecodedToken>(token);
  } catch (error) {
    console.error("Error decoding token:", error);
    return null;
  }
};

// Check if token is expired
export const isTokenExpired = (token: string): boolean => {
  try {
    const decoded = decodeToken(token);

    if (decoded?.exp) {
      const expiryDate = new Date(decoded?.exp * 1000);
    }
    if (!decoded) {
      return true;
    }

    // Check if the token will expire in the next 60 seconds
    const currentTime = Math.floor(Date.now() / 1000);
    const isExpired = decoded.exp < currentTime + 60;

    return isExpired;
  } catch (error) {
    return true;
  }
};

export const refreshAccessToken = async (data: any) => {
  try {
    const response = await ADMIN_LOGIN_API.refreshToken(data);

    return response.data;
  } catch (error) {
    console.error("[refreshAccessToken] Error refreshing token:", error);
    throw error;
  }
};

// Process all pending requests after token refresh
export const processPendingRequests = (newToken: string | null): void => {
  pendingRequests.forEach(({ config, resolve, reject }) => {
    if (newToken) {
      config.headers.Authorization = `Bearer ${newToken}`;
      resolve(axios(config));
    } else {
      reject(new Error("Token refresh failed"));
    }
  });

  // Clear pending requests
  pendingRequests = [];
};
