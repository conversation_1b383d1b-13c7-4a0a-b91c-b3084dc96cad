import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { WORKFLOW_API } from "../../../Apis/AdminLogin/Automation/Workflows";

export interface IData {
    AddSaveResponseAttributeStatus: "loading" | "succeeded" | "failed" | "idle";
    AddSaveResponseAttributeData: any;
  }

  const initialState: IData = {
    AddSaveResponseAttributeStatus: "idle",
    AddSaveResponseAttributeData: null,
  };

  export const AddSaveResponseAttribute = createAsyncThunk(
    "workflow/AddSaveResponseAttribute",
    async (data: any, { rejectWithValue }) => {
      try {
        const response = await WORKFLOW_API.AddSaveResponseAttribute(data);
        return response?.data;
      } catch (error: any) {
        // Provide a descriptive error message
        return rejectWithValue(error.response || 'An error occurred');
      }
    }
  );


  export const AddSaveResponseAttributeSlice = createSlice({
    name: "AddSaveResponseAttribute",
    initialState,
    reducers: {
       
    },
    extraReducers: (builder) => {
      builder
        .addCase(AddSaveResponseAttribute.pending, (state) => {
          state.AddSaveResponseAttributeStatus = "loading";
          state.AddSaveResponseAttributeData = null;
        })
        .addCase(AddSaveResponseAttribute.fulfilled, (state, action) => {
          state.AddSaveResponseAttributeStatus = "succeeded";
          state.AddSaveResponseAttributeData = action.payload;
        })
        .addCase(AddSaveResponseAttribute.rejected, (state) => {
          state.AddSaveResponseAttributeStatus = "failed";
        });
    },
  });


export const AddSaveResponseAttributeActions = AddSaveResponseAttributeSlice.actions;
export default AddSaveResponseAttributeSlice.reducer;
