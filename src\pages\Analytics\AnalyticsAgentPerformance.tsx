import { bgColors } from "../../utils/bgColors";
import { makeStyles } from "@mui/styles";
import ArrowDownSvg from "../../assets/svgs/ArrowDownSvg";
import ExportWhiteIcon from "../../assets/svgs/ExportWhiteIcon";
import { useAppDispatch, useAppSelector } from "../../utils/redux-hooks";
import React, {
  useEffect,
  useState,
  ChangeEvent,
  useRef,
  useCallback,
} from "react";
import { Box, Grid, Typography } from "@mui/material";
import ContactsFilterPopover from "../../components/ContactsComponents/ContactsFilterPopOver";
import { fetchAgentPerformanceByCompanyId } from "../../redux/slices/Analytics/AgentAnalyticsSlice";
import { ANALYTICS_API } from "../../Apis/Analytics/Analytics";
import DateRangePicker from "../../components/AnalyticsComponent/DatePickerComponent";
import CommonHeader from "../../components/common/CommonHeader";
import CommonButton from "../../components/common/CommonButton";
import CommonTable, { TableColumn } from "../../components/common/CommonTable";

const useStyles = makeStyles({
  mainContainer: {
    backgroundColor: bgColors.white1,
    height: "100vh",
    width: "100%",
  },
  bgContainer: {
    backgroundColor: bgColors.white,
  },
  headerContainer: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    borderBottom: "1px solid #f0f0f0",
    width: "100%",
    paddingLeft: "24px",
    paddingRight: "24px",
    paddingTop: "8px",
    paddingBottom: "8px",
  },
  blackColor: {
    color: `${bgColors.black1} !important`,
    fontWeight: "600 !important",
  },
  grayColor: {
    color: `${bgColors.black1} !important`,
    opacity: "60% !important",
  },
  grayColor1: {
    color: "#303030",
    opacity: "90%",
    fontSize: "13px",
    // padding:"5px"
  },
  rotatedIcon: {
    cursor: "pointer",
    paddingRight: "5px",
    transform: "rotate(180deg)",
  },
  iconStyles: {
    cursor: "pointer",
    paddingLeft: "5px",
  },
  messageCountContainer: {
    // border: `2px solid ${bgColors.gray5}`,
    // borderRadius: "12px",
    // padding: "2px 5px",
    height: "31px",
    border: "1px solid #F2F2F2",
    cursor: "pointer",
    borderRadius: "5px",
    paddingInline: "8px",
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  table: {
    borderCollapse: "separate",
    borderSpacing: "0",
    textAlign: "center",
    borderColor: "lightgray",
    "& th, & td": {
      // borderTop: '1px solid gray',
      borderBottom: "1px solid #f0f0f0",
      height: "35.8px",
    },
    "& th:first-child, & td:first-child": {
      borderLeft: "none",
    },
    "& th:last-child, & td:last-child": {
      borderRight: "none",
    },
  },
  messageInnerContainer: {
    display: "flex",
    flexDirection: "row",
  },
});

const subOptions = {
  Category: [{ id: 1, option: "Marketing" }],
  // SubCategory: subCategoriesList,
  Status: [
    { id: 1, option: "Pending" },
    { id: 2, option: "Approved" },
    { id: 3, option: "Rejected" },
    { id: 4, option: "Draft" },
    { id: 5, option: "Deleted" },
  ],
};

const options = [
  {
    id: 1,
    option: "View All",
  },
  {
    id: 2,
    option: "Category",
  },
  {
    id: 3,
    option: "Status",
  },
];

const AnalyticsAgentPerformance = () => {
  const classes = useStyles();
  const dispatch = useAppDispatch();
  const [pageNumber, setPageNumber] = React.useState(1);
  const userData = useAppSelector((state: any) => state?.adminLogin?.data);
  const agentData = useAppSelector((state: any) => state?.agentPerformance);
  const [anchorElFilter, setAnchorElFilter] = useState<null | HTMLElement>(
    null
  );
  const [selectedFilter, setSelectedFilter] = useState("Tags");
  const [selectedFilter2, setSelectedFilter2] = React.useState({
    column: "",
    value: "",
  });
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [page, setPage] = useState(1);
  const [pageData, setPageData] = useState(agentData.agentData || []);
  const [teamData, setTeamData] = useState<any>([]);
  const [hoveredRow, setHoveredRow] = useState(null);
  const [isDateRangeApplied, setIsDateRangeApplied] = useState(false);
  const today = () => {
    const currentDate = new Date();
    return currentDate.toISOString().split("T")[0]; // Formats the date to 'YYYY-MM-DD'
  };
  const [endCalenderDate, setEndCalenderDate] = useState(today());
  const [startCalenderDate, setStartCalenderDate] = useState(today());

  useEffect(() => {
    const postData = {
      userId: userData?.userId,
      companyId: userData?.companyId,
      pageNumber: pageNumber,
      fromDate: startCalenderDate,
      toDate: endCalenderDate,
      tags: teamData,
      isExportData: false,
      per_page: 40,
      filters: {
        sorting: {
          column: "",
          order: "",
        },
        filtering: {
          filterType: "and",
          conditions: [
            {
              column: selectedFilter2?.column,
              operator: "equal",
              value: selectedFilter2?.value,
            },
          ],
        },
      },
    };

    dispatch(fetchAgentPerformanceByCompanyId(postData));
    //  setPageData(agentData.agentData);
  }, [dispatch, pageNumber, endCalenderDate, startCalenderDate, teamData]);
  useEffect(() => {
    setPageData(agentData.agentData);
  }, [agentData]);

  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [isExportAllByIdLoading, setIsExportAllByIdLoading] = useState(false);

  const tableContainerRef = useRef(null);

  const handleLoadMore = useCallback(() => {
    if (
      !isLoadingMore &&
      pageData?.length !== agentData?.data?.total &&
      pageNumber <= Math.ceil(agentData?.data?.total / 40)
    ) {
      setIsLoadingMore(true);
      setPageNumber((prevPage) => prevPage + 1);
    }
  }, [isLoadingMore, pageData?.length]);

  useEffect(() => {
    if (agentData) {
      if (pageNumber === 1) {
        setPageData(agentData);
      } else {
        setPageData((prevPageData: any) => [...prevPageData, ...agentData]);
      }
      setIsLoadingMore(false);
    }
  }, [agentData]);

  const getContactTag = useAppSelector(
    (state: any) => state?.getContactTagsData?.data
  );
  const activeTagsArray: any = getContactTag?.filter(
    (tag: any) => tag.isActive
  );
  const filteredTags = activeTagsArray?.filter((tag: any) =>
    tag?.tag?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleFilterClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorElFilter(event.currentTarget);
  };

  const handleCloseFilterPopover = () => {
    setAnchorElFilter(null);
  };

  const handleOptionClick = (option: string) => {
    setSelectedFilter(option);
    handleCloseFilterPopover();
  };

  const handleSearchChange = (event: ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  const handleDateRangeChange = ({ startDate, endDate }: any) => {
    setStartCalenderDate(startDate);
    setEndCalenderDate(endDate);
    setIsDateRangeApplied(false);
  };

  const handleExportById = async () => {
    // const hasPermission = hasExportByIdPermission(manageContactsObject);
    try {
      let data = {
        userId: userData?.userId,
        companyId: userData?.companyId,
        pageNumber: pageNumber,
        fromDate: startCalenderDate,
        tags: teamData,
        toDate: endCalenderDate,
        isExportData: true,
        // per_page: 40,
      };
      setIsExportAllByIdLoading(true);
      const getResponseById = await ANALYTICS_API.getAgentPerformance(data);
      let rdata: string = getResponseById.data.data;
      // Decode base64 string to binary data
      const byteCharacters = atob(rdata);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      // Create a blob from the byte array
      const blob = new Blob([byteArray], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });
      const url = window.URL.createObjectURL(blob);

      const link = document.createElement("a");

      link.href = url;
      link.download = "exported_Analytics.xlsx";
      link.style.display = "none";
      document.body.appendChild(link);

      link.click();

      // Cleanup
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      setIsExportAllByIdLoading(false);
    } catch (error) {
      setIsExportAllByIdLoading(false);
    }
  };

  const agentPerformanceColumns: TableColumn[] = [
    {
      id: "name",
      label: "Name",
    },
    {
      id: "assigned",
      label: "Assigned",
    },
    {
      id: "reassigned",
      label: "Reassigned",
    },
    {
      id: "responded",
      label: "Responded",
    },
    {
      id: "resolved",
      label: "Resolved",
    },
    {
      id: "resolutionTime",
      label: "Resolution Time (Mins)",
    },
    {
      id: "avgResponseTime",
      label: "Avg Response Time (Mins)",
    },
  ];

  const transformData = pageData?.agentData?.map((item: any) => ({
    ...item,
    name: item.agent?.name,
    assigned: item.assignedCount,
    reassigned: item.reassignedCount,
    responded: item.respondedCount,
    resolved: item.resolvedCount,
    resolutionTime: item.resolutionTime,
    avgResponseTime: item.avgResonseTime,
  }));

  return (
    <Grid className={classes.mainContainer}>
      <Box className={classes.bgContainer}>
        <Box className={classes.headerContainer}>
          <CommonHeader title="Agent Performance" />
        </Box>
        <Box
          sx={{
            mt: 2,
            display: "flex",
            flexDirection: { xs: "column", sm: "row" },
            justifyContent: "space-between",
            gap: { xs: 2, sm: 0 },
          }}
          ml={2}
          mb={2}
        >
          <Box
            sx={{
              display: "flex",
              flexDirection: { xs: "column", sm: "row" },
              gap: { xs: 1, sm: 0 },
              mr: { xs: 1, sm: 0 },
            }}
          >
            <Box className={classes.messageCountContainer}>
              <Typography
                className={classes.grayColor1}
                sx={{ fontSize: "13px" }}
                onClick={handleFilterClick}
              >
                {selectedFilter}
              </Typography>
              <Box className={classes.iconStyles} onClick={handleFilterClick}>
                <ArrowDownSvg />
              </Box>
              <ContactsFilterPopover
                anchorEl={anchorElFilter}
                handleClose={handleCloseFilterPopover}
                options={filteredTags}
                handleOptionClick={handleOptionClick}
                handleSearchChange={handleSearchChange}
                setSearchQuery={setSearchQuery}
                setPage={setPage}
                setTeamData={setTeamData}
              />
            </Box>
            {/* <Box className={classes.messageCountContainer} mx={2}>
                <Box className={classes.messageInnerContainer}>
                  <Typography variant="body2" className={classes.grayColor}>
                    Events
                  </Typography>
                  <Box className={classes.iconStyles}>
                    <ArrowDownSvg />
                  </Box>
                </Box>
              </Box> */}
            {/* <Box className={classes.messageCountContainer}  mx={2}>
                <Box className={classes.messageInnerContainer}>
                  <Typography
                    className={classes.grayColor}
                    style={{
                      padding: "3px",
                      fontSize: 12,
                      display: "flex",
                      alignItems: "center",
                    }}
                  >
                    {getIconComponent(selectedFilter2?.value)}
                    &nbsp; &nbsp;|
                  </Typography>
                  <Box
                    className={
                      anchorElTemplate
                        ? classes.rotatedIcon
                        : classes.iconStyles
                    }
                    onClick={handleTemplateFilterClick}
                  >
                    <ArrowDownSvg />
                  </Box>
                  <TemplatesFilterPopover
                    anchorEl={anchorElTemplate}
                    handleClose={handleCloseTemplatePopover}
                    options={options}
                    subOptions={subOptions}
                    handleOptionClick={handleOptionClick2}
                  />
                </Box>
              </Box> */}
            {/* <Box className={classes.messageCountContainer} mx={2}>
                <TextField
                  // label="Select Date"
                  type="date"
                  value={selectedDate}
                  onChange={handleDateChange}
                  InputLabelProps={{
                    shrink: true, // Ensures the label is displayed above the input field
                  }}
                  fullWidth
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      border: "none",
                    },
                    "& .MuiOutlinedInput-notchedOutline": {
                      border: "none",
                    },
                    "& .MuiInputBase-root": {
                      height: "31px",
                    },
                    "& .MuiInputBase-input": {
                      padding: "8px 14px",
                    },
                  }}
                />
              </Box> */}
            <Box>
              <Box
                ml={{ xs: 0, sm: 2 }}
                sx={{
                  display: "flex",
                  // justifyContent: "space-between"
                  border: "1px solid #eee",
                  borderRadius: "5px",
                  height: "31px",
                }}
              >
                <DateRangePicker
                  onDateRangeChange={handleDateRangeChange}
                  // disabled={transactionData && transactionData?.length < 1}
                  startCalenderDate={startCalenderDate}
                  endCalenderDate={endCalenderDate}
                />
                {/* <Button
                    // disabled={!startCalenderDate || !endCalenderDate}
                    onClick={handleApplyDateRange}
                    sx={{
                      color: "green",
                      fontSize: "12px",
                      padding: 0,
                      margin: 0,
                    }}
                  >
                    <Tooltip title={tooltipText}>{icon}</Tooltip>
                  </Button> */}
              </Box>
            </Box>
          </Box>
          <CommonButton
            primaryAction={{
              label: "Export",
              processingLabel: "Exporting...",
              icon: <ExportWhiteIcon />,
              onClick: handleExportById,
            }}
            onLoading={isExportAllByIdLoading}
          />
        </Box>
        <CommonTable
          columns={agentPerformanceColumns}
          data={transformData}
          isLoading={agentData.agentStatus == "loading"}
          heightOfTable="auto"
          showPagination={false}
        />
      </Box>
    </Grid>
  );
};

export default AnalyticsAgentPerformance;
