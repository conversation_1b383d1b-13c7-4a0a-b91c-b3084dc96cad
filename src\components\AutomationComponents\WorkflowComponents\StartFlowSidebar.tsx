import React from "react";
import { Box } from "@mui/material";
import NodeCard from "./Nodecard";
import SidebarSection from "./SidebarSection";

interface StartFlowSidebarProps {
  messages: any[];
  actions: any[];
  onDragStart: (event: React.DragEvent, label: string) => void;
  draggable: boolean;
  onClick: (label: string) => void;
  nodes?: any[];
}

const StartFlowSidebar = ({
  messages,
  actions,
  onDragStart,
  draggable,
  onClick,
  nodes = [],
}: StartFlowSidebarProps) => {
  return (
    <Box
      sx={{
        width: "100%",
        height: "auto",
        maxHeight: { xs: "40vh", sm: "55vh", md: "70vh" },
        "&::-webkit-scrollbar": {
          width: "6px",
        },
        "&::-webkit-scrollbar-track": {
          background: "#f1f1f1",
        },
        "&::-webkit-scrollbar-thumb": {
          background: "#888",
          borderRadius: "3px",
        },
      }}
    >
      <Box>
        <SidebarSection title="Messages" isCollapsed={false} twoPerRow={true}>
          {messages?.map((message: any) => (
            <NodeCard
              key={message?.id}
              icon={message?.icon}
              label={message?.label}
              description={message?.description}
              isCollapsed={false}
              onDragStart={(event: any) => onDragStart(event, message?.label)}
              draggable={draggable}
              onClick={() => onClick(message?.label)}
              sx={{ cursor: "pointer" }}
            />
          ))}
        </SidebarSection>

        <SidebarSection title="Actions" isCollapsed={false} twoPerRow={true}>
          {actions?.map((action: any) => {
            if (action.isVisible && !action.isVisible(nodes)) {
              return null;
            }
            return (
              <NodeCard
                key={action?.id}
                icon={action?.icon}
                label={action?.label}
                description={action?.description}
                isCollapsed={false}
                onDragStart={(event: any) => onDragStart(event, action?.label)}
                draggable={draggable}
                onClick={() => onClick(action?.label)}
                sx={{ cursor: "pointer" }}
              />
            );
          })}
        </SidebarSection>
      </Box>
    </Box>
  );
};

export default StartFlowSidebar; 