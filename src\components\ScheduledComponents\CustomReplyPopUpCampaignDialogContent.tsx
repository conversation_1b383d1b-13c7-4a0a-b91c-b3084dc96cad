import {
  Box,
  FormControl,
  FormHelperText,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  Tooltip,
  Typography,
} from "@mui/material";
import React, { useEffect, useRef, useState } from "react";
import { makeStyles } from "@mui/styles";
import InfoIcon from "@mui/icons-material/Info";
import { bgColors } from "../../utils/bgColors";
import TextFieldWithBorderComponent from "../common/TextFieldWithBorderComponent";
import EmojiPopover from "../InboxComponents/inboxDetailsComponents/EmojiPicker";
import { DraftEditorComponent } from "../common/DraftEditorComponent";
import { EditorState, ContentBlock, Modifier } from "draft-js";
import { formatContent, parseTextToDraft } from "../../utils/functions";
import { reactDraftWysiwygToolbarOptionsarticle } from "../../utils/react-draft-wysiwyg-options";
import useDebouncedFetch from "../../utils/debounceHook";
import { fetchTemplateName } from "../../redux/slices/Templates/GetTemplateNameSlice";
import { CustomerResponse } from "./EditCampaign";

const useStyles = makeStyles({
  blackColor: {
    color: "#303030 !important",
    fontWeight: "500 !important",
  },
  variable: {
    color: `${bgColors.green} !important`,
    fontWeight: "500 !important",
    fontSize: "14px !important",
    cursor: "pointer",
  },
  formControl: {
    display: "flex",
    alignItems: "center",
  },
});

interface TemplateState {
  body: string;
}

interface TemplateState {
  templateId: string;
  templateName: string;
  category: number;
  subCategory: string;
  mediaType: number;
  mediaAwsUrl: any;
  header: string;
  body: string;
  callButtonName: string;
  phoneNumber: string;
  countryCode: string;
  urlButtonName: string[];
  redirectUrl: string[];
  quickReply: string[];
  footer: string;
  buttons: {
    buttonType: string;
    buttonValue: string;
    buttonName?: any; // Optional property
  }[];
  variables: Array<{
    type: string;
    id: string;
    value: string;
    field: string;
    fallBackValue: string;
  }>;
}

interface CustomReplyPopUpCampaignDialogContentProps {
  editorState: any;
  called: any;
  setCalled: any;
  file: any;
  bodyRef: any;
  canEdit: any;
  setEditorState: any;
  triggerType: any;
  setTypedReply: any;
  setTriggerType: any;
  typedReply: any;
  charCountTypedReply: any;
  setCharCountTypedReply: any;
  charCountEditor: any;
  setCharCountEditor: any;
  selectedButton: any;
  setSelectedButton: any;
  emojiPopoverOpen: any;
  setEmojiPopoverOpen: any;
  anchorEl: any;
  setAnchorEl: any;
  loading: any;
  setLoading: any;
  campaignState: any;
  setCampaignState: any;
  templateData: any;
  // canEdit={canEdit}
  customReplyLimit: any;
  error: any;
  setError: any;
}

const CustomReplyPopUpCampaignDialogContent: React.FC<
  CustomReplyPopUpCampaignDialogContentProps
> = ({
  editorState,
  called,
  setCalled,
  file,
  bodyRef,
  canEdit,
  setEditorState,
  triggerType,
  setTriggerType,
  typedReply,
  setTypedReply,
  selectedButton,
  setSelectedButton,
  emojiPopoverOpen,
  setEmojiPopoverOpen,
  anchorEl,
  setAnchorEl,
  campaignState,
  setCampaignState,
  templateData,
  // canEdit={canEdit}
  customReplyLimit,
  error,
  setError,
}) => {
  const classes = useStyles();
  const chatAreaRef = useRef<HTMLDivElement>(null);
  const [templateState, setTemplateState] = useState<TemplateState>({
    templateId: "",
    templateName: "",
    category: 1,
    subCategory: "",
    mediaType: 1,
    mediaAwsUrl: null,
    header: "",
    body: "",
    callButtonName: "",
    phoneNumber: "",
    countryCode: "",
    urlButtonName: [],
    redirectUrl: [],
    quickReply: [],
    footer: "",
    buttons: [],
    variables: [],
  });

  const maxCharLimit = 999;

  const handleEditorDelayedStateChange = (editorState: EditorState) => {
    const formattedContent = formatContent(editorState.getCurrentContent());

    const event = {
      target: {
        name: "body",
        value: formattedContent,
      },
    };
    handleChange(
      event as React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
    );

    setEditorState(editorState);
  };

  const handleEmojiClick = (event: any) => {
    setAnchorEl(event.currentTarget);
    setEmojiPopoverOpen(true);
  };

  const handleCloseEmojiPopover = () => {
    setEmojiPopoverOpen(false);
  };

  const handleEmojiSelect: any = (emoji: string) => {
    const { current } = bodyRef;
    if (current) {
      const { selectionStart, selectionEnd } = current;
      const currentBody = templateState?.body;
      const newBody =
        currentBody?.slice(0, selectionStart) +
        emoji +
        currentBody?.slice(selectionEnd);

      setTemplateState((prevState: any) => ({
        ...prevState,
        body: newBody,
      }));
      setTimeout(() => {
        bodyRef?.current?.focus();
        bodyRef?.current?.setSelectionRange(
          selectionStart + emoji?.length,
          selectionStart + emoji?.length
        );
      }, 0);
    }
  };

  const checkTemplateName = useDebouncedFetch(fetchTemplateName, 0);

  const handleChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = event.target;

    if (name === "buttons") {
      if (value !== "None") {
        setTemplateState((prevState: any) => ({
          ...prevState,
          [name]: [
            {
              buttonType: value,
              buttonValue: "",
              countryCode: "",
              buttonName: "", // Optional property
            },
          ],
        }));
      } else {
        setTemplateState((prevState: any) => ({
          ...prevState,
          [name]: [],
        }));
      }
    } else {
      setTemplateState((prevState: any) => ({
        ...prevState,
        [name]: value,
      }));
    }
  };
  const handleSaveInboxSettings = () => {};

  const Media = (props: any) => {
    const entity = props.contentState.getEntity(props.block.getEntityAt(0));
    const { src, type } = entity.getData();
    let media;
    if (type === "IMAGE") {
      media = (
        <img
          src={src}
          alt="uploaded"
          style={{ height: "100px", width: "100px" }}
        />
      );
    } else if (type === "VIDEO") {
      media = (
        <video src={src} controls style={{ height: "100px", width: "300px" }} />
      );
    }
    return media;
  };

  const handleTriggerTypeChange = (event: any) => {
    const value = event.target.value;

    setTriggerType(value);
    if (value === CustomerResponse.OnButtonClick) {
      setTypedReply("");
      setError("");
    } else if (value === CustomerResponse.TypedOutReply) {
      setTypedReply("");
      setError("");
    }
  };

  const handleChooseButtonChange = (event: any) => {
    const newValue = event.target.value as string;

    const newvalueLowerCase = newValue.toLowerCase();

    setSelectedButton(newvalueLowerCase);
    setCampaignState((prev: any) => ({
      ...prev,
      autoCustomAutomation: {
        input: selectedButton,
      },
    }));
  };

  const handleTypedReplyChange = (event: any) => {
    const input = event.target.value.trim();
    const targetWord = campaignState?.workflowAutomation?.input;

    const regex = new RegExp(`^${targetWord}$`, "i");

    if (regex.test(input)) {
      setError("Reply should not be the same as the previous input.");
    } else {
      setError("");
    }

    if (input.length <= customReplyLimit) {
      setTypedReply(input);
    }
  };

  const renderButtonOptions = () => {
    if (
      !templateData ||
      !templateData.buttons ||
      templateData.buttons.length === 0
    ) {
      return (
        <MenuItem sx={{ fontSize: { xs: 10, md: 12 } }} disabled>
          No buttons available
        </MenuItem>
      );
    }

    const quickReplyButtons = templateData.buttons.filter(
      (button: any) => button.buttonType === "QUICK_REPLY"
    );

    const uniquebuttons = quickReplyButtons?.filter((uniquebutton: any) => {
      return (
        uniquebutton?.buttonValue?.toLowerCase() !==
        campaignState?.workflowAutomation?.input.toLowerCase()
      );
    });

    if (uniquebuttons?.length === 0) {
      return (
        <MenuItem sx={{ fontSize: { xs: 10, md: 12 } }} disabled>
          No QUICK_REPLY buttons available
        </MenuItem>
      );
    }

    return uniquebuttons?.map((button: any) => (
      <MenuItem
        key={button.id}
        value={button.buttonValue.toLowerCase()}
        sx={{ fontSize: { xs: 10, md: 12 } }}
      >
        {button.buttonValue}
      </MenuItem>
    ));
  };

  useEffect(() => {
    if (canEdit && templateState?.body && !called) {
      setEditorState(
        EditorState?.createWithContent(parseTextToDraft(templateState?.body))
      );
      setCalled(true);
    }
  }, [canEdit, templateState?.body]);

  const blockRendererFn = (contentBlock: ContentBlock) => {
    const type = contentBlock.getType();
    if (type === "atomic") {
      return {
        component: Media,
        editable: false,
      };
    }
    return null;
  };

  useEffect(() => {
    if (campaignState?.autoCustomAutomation?.input !== "") {
      setTriggerType(
        campaignState?.autoCustomAutomation?.customerResponse ||
          CustomerResponse.OnButtonClick
      );
      setTypedReply(campaignState?.autoCustomAutomation?.input || "");
      setSelectedButton(campaignState?.autoCustomAutomation?.input || "");
      setEditorState(() =>
        EditorState.createWithContent(
          parseTextToDraft(
            campaignState?.autoCustomAutomation?.bodyMessage || "Custom Body"
          )
        )
      );
    }

    () => {
      setTriggerType(CustomerResponse.OnButtonClick);
      setTypedReply("");
      setSelectedButton("");
      setEditorState(() =>
        EditorState.createWithContent(parseTextToDraft("Custom Body"))
      );
    };
  }, []);

  return (
    <Grid
      container
      // justifyContent={{ md: "space-around" }}
      flexDirection={{ xs: "column", md: "row" }}
      gap={{ xs: "20px", md: "0" }}
      ref={chatAreaRef}
      sx={{ p: 2 }}
    >
      <Box display="flex" flexDirection="column">
        <Typography variant="body1" sx={{ mb: 2 }}>
          If the customer's reply matches the below trigger, a custom reply will
          be sent.
        </Typography>
        <Typography variant="h6" sx={{ mb: 2, fontWeight: "bold" }}>
          Set Trigger
        </Typography>
      </Box>
      <Grid item xs={12} md={6} mt={0}>
        <Box
          display="flex"
          flexDirection="column"
          width="100%"
          mb={{ xs: 1, md: 2 }}
        >
          <Box display="flex">
            <Box width="50%" mr={0}>
              {/* <InputLabel
                className={classes.blackColor}
                sx={{ fontSize: { xs: 12, md: 14 }, mb: 2 }}
              >
                Trigger Type*
                <Tooltip
                  title={
                    <>
                      <div>
                        1. Marketing conversation: Business-initiated
                        conversations to market a product or service to
                        customers, such as relevant offers to customers who have
                        opted in.
                      </div>
                      <div>
                        2. Utility conversations: Business-initiated
                        conversations relating to a transaction, including
                        post-purchase notifications and recurring billing
                        statements to customers who have opted in.
                      </div>
                    </>
                  }
                  arrow
                >
                  <IconButton size="small">
                    <InfoIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </InputLabel> */}
              <FormControl fullWidth size="small">
                <InputLabel
                  id="demo-simple-select-label"
                  sx={{ fontSize: { xs: 12, md: 14 } }}
                >
                  Trigger Type
                </InputLabel>
                <Select
                  id="demo-simple-select"
                  labelId="demo-simple-select-label"
                  value={triggerType}
                  name="Trigger Type"
                  label="Trigger Type"
                  inputProps={{ style: { fontSize: 14 } }}
                  onChange={handleTriggerTypeChange}
                  sx={{
                    fontSize: 14,
                    borderRadius: "8px",
                    // fontWeight: "600",
                  }}
                >
                  <MenuItem
                    value={CustomerResponse.OnButtonClick}
                    sx={{ fontSize: { xs: 10, md: 12 } }}
                  >
                    On Button Click
                  </MenuItem>
                  <MenuItem
                    value={CustomerResponse.TypedOutReply}
                    sx={{ fontSize: { xs: 10, md: 12 } }}
                  >
                    Typed Out Reply
                  </MenuItem>
                </Select>
              </FormControl>
            </Box>

            <Box width="80%" ml={1}>
              {triggerType === CustomerResponse.OnButtonClick && (
                <Box>
                  {/* <InputLabel
                    className={classes.blackColor}
                    sx={{ fontSize: { xs: 12, md: 14 }, mb: 2 }}
                  >
                    Choose Button*
                    <Tooltip
                      title={
                        <>
                          <div>
                            1. Choose relevant language to reduce the
                            probability of rejection for your template.
                          </div>
                        </>
                      }
                      arrow
                    >
                      <IconButton size="small">
                        <InfoIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </InputLabel> */}

                  <FormControl fullWidth size="small">
                    <InputLabel
                      id="demo-options-label"
                      sx={{ fontSize: { xs: 12, md: 14 } }}
                    >
                      Choose Button
                    </InputLabel>
                    <Select
                      id="demo-select-label"
                      labelId="demo-options-label"
                      label="Choose Button"
                      name="Choose Button"
                      value={selectedButton || ""}
                      onChange={handleChooseButtonChange}
                      inputProps={{ style: { fontSize: 14 } }}
                      sx={{
                        fontSize: 14,
                        borderRadius: "8px",
                        // fontWeight: "600",
                      }}
                    >
                      {renderButtonOptions()}
                    </Select>
                  </FormControl>
                </Box>
              )}

              {triggerType === CustomerResponse.TypedOutReply && (
                <Box width="100%">
                  {/* <InputLabel
                    className={classes.blackColor}
                    sx={{ fontSize: { xs: 12, md: 14 }, mb: 1, mr: 0 }}
                  >
                    Custom Reply*
                    <Tooltip
                      title={
                        <div>
                          The template name should be unique from the existing
                          templates.
                        </div>
                      }
                      arrow
                    >
                      <IconButton size="small">
                        <InfoIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </InputLabel> */}
                  <FormControl
                    sx={{ mt: -1, position: "relative" }}
                    fullWidth
                    size="small"
                  >
                    <TextFieldWithBorderComponent
                      label="Custom Reply"
                      name="Custom Reply"
                      placeholder="Enter Reply KeyWord"
                      value={typedReply}
                      onChange={handleTypedReplyChange}
                    />
                    <Typography
                      style={{ color: `${bgColors.gray3}` }}
                      variant="body2"
                      sx={{
                        position: "absolute",
                        color:
                          typedReply.length > customReplyLimit
                            ? "error.main"
                            : "inherit",
                        right: 8,
                        top: 8,
                        bottom: error ? 36 : 8,
                        transition: "bottom 0.2s ease-in-out",
                      }}
                    >
                      {` ${typedReply.length}/${customReplyLimit}`}
                    </Typography>
                    {error && typedReply.length !== 0 && (
                      <FormHelperText error>{error}</FormHelperText>
                    )}
                  </FormControl>
                </Box>
              )}
            </Box>
          </Box>

          <Box mt={2}>
            <InputLabel
              className={classes.blackColor}
              sx={{ fontSize: { xs: 12, md: 14 }, mb: 1 }}
            >
              Custom Message*
              <Tooltip
                title={
                  <>
                    <div>
                      1. Minimum one letter or word must be present in between
                      any two variables.
                    </div>
                    <div>
                      2. Body cannot be started or ended with a variable.
                    </div>
                    <div>
                      3. Total variables count should be less than half of the
                      words in the body.
                    </div>
                    <div>4.Body must not exceed 1024 characters.</div>
                  </>
                }
                arrow
              >
                <IconButton size="small">
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </InputLabel>
            <FormControl fullWidth>
              <div style={{ position: "relative" }}>
                <EmojiPopover
                  open={emojiPopoverOpen}
                  anchorEl={anchorEl}
                  onClose={handleCloseEmojiPopover}
                  onEmojiSelect={handleEmojiSelect}
                />
                <Typography
                  style={{ color: `${bgColors.gray3}` }}
                  variant="body2"
                  sx={{
                    position: "absolute",
                    color:
                      formatContent(editorState.getCurrentContent()).length >
                      maxCharLimit
                        ? "error.main"
                        : "inherit",
                    right: 8,
                    top: 8,
                  }}
                >
                  {`${
                    formatContent(editorState.getCurrentContent()).length
                  } / ${maxCharLimit}`}
                </Typography>
                <DraftEditorComponent
                  editorState={editorState}
                  handleEditorStateChange={handleEditorDelayedStateChange}
                  handleSaveInboxSettings={handleSaveInboxSettings}
                  reactDraftWysiwygToolbarOptionsarticle={
                    reactDraftWysiwygToolbarOptionsarticle
                  }
                  blockRendererFn={blockRendererFn}
                  file={file}
                  bodyRef={bodyRef}
                  chatAreaRef={chatAreaRef}
                />
              </div>
            </FormControl>
          </Box>
        </Box>
      </Grid>
    </Grid>
  );
};

export default CustomReplyPopUpCampaignDialogContent;
