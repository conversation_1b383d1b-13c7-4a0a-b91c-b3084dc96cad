import React from "react";
import { Box } from "@mui/material";
import { makeStyles } from "@mui/styles";
import EngagetoLogoSvg from "../../assets/svgs/EngagetoLogoSvg";
import SignupFormContainer from "../../components/SignupComponents/SignupFormContainer";

const useStyles = makeStyles({
  background: {
    backgroundImage: `url("/images/new-pwd-bg.png")`,
    backgroundSize: "cover",
    backgroundPosition: "center",
    minHeight: "100vh",
    display: "flex",
    flexDirection: "column",
  },
  logoContainer: {
    // padding: '16px',
  },
  formContainer: {
    flex: 1,
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  },
});

const Signup = () => {
  const classes = useStyles();
  
  return (
    <Box className={classes.background}>
      <Box className={classes.logoContainer}>
        <EngagetoLogoSvg />
      </Box>
      <Box className={classes.formContainer}>
        <SignupFormContainer />
      </Box>
    </Box>
  );
};

export default Signup;
