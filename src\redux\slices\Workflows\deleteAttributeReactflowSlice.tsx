import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { WORKFLOW_API } from "../../../Apis/AdminLogin/Automation/Workflows";

export interface IData {
  deleteAttributeReactflowStatus: "loading" | "succeeded" | "failed" | "idle";
  deleteAttributeReactflowData: any;
}

const initialState: IData = {
  deleteAttributeReactflowStatus: "idle",
  deleteAttributeReactflowData: null,
};

export const deleteAttributeReactflow = createAsyncThunk(
  "deleteAttributeReactflow",
  async (data: any, { rejectWithValue }) => {
    try {
      const response = await WORKFLOW_API.deleteAttribute(data);
      return response?.data;
    } catch (error: any) {
      // Provide a descriptive error message
      return rejectWithValue(error.response || "An error occurred");
    }
  }
) as any;

export const deleteAttributeReactflowSlice = createSlice({
  name: "deleteAttributeReactflowSlice",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(deleteAttributeReactflow.pending, (state) => {
        state.deleteAttributeReactflowStatus = "loading";
        state.deleteAttributeReactflowData = null;
      })
      .addCase(deleteAttributeReactflow.fulfilled, (state, action) => {
        state.deleteAttributeReactflowStatus = "succeeded";
        state.deleteAttributeReactflowData = action.payload;
      })
      .addCase(deleteAttributeReactflow.rejected, (state) => {
        state.deleteAttributeReactflowStatus = "failed";
      });
  },
});

export const deleteAttributeActions = deleteAttributeReactflowSlice.actions;
export default deleteAttributeReactflowSlice.reducer;
