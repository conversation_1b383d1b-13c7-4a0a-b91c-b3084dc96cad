/* global process */

import * as firebase from "firebase/app";
import "firebase/messaging";
import { getMessaging, getToken } from "firebase/messaging";

export const firebaseConfig = {
  apiKey: process.env.REACT_APP_FIREBASE_CONFIG_API_KEY,
  authDomain: process.env.REACT_APP_FIRBASE_CONFIG_AUTH_DOMAIN,
  projectId: process.env.REACT_APP_FIREBASE_CONFIG_PROJECT_ID,
  storageBucket: process.env.REACT_APP_FIREBASE_CONFIG_STORAGEBUCKET,
  messagingSenderId: process.env.REACT_APP_FIREBASE_CONFIG_MESSAGINGSENDERID,
  appId: process.env.REACT_APP_FIREBASE_CONFIG_APPID,
  measurementId: process.env.REACT_APP_FIREBASE_CONFIG_MEASUREMENTID,
};

const app = firebase.initializeApp(firebaseConfig);

export const messaging: any = getMessaging(app);

export const generateToken = async () => {
  try {
    const notify = await Notification.requestPermission();

    if (notify === "granted") {
      // const serviceWorkerRegistration = await navigator.serviceWorker.ready;
      await navigator.serviceWorker.ready;
      const token = await getToken(messaging, {
        vapidKey: process.env.REACT_APP_FIREBASE_VAPID_KEY,
      });

      return token;
    }
  } catch (error) {}
};

// export default firebase

export const firbase = () => {};
