import { Box, Button, IconButton, Paper, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import { bgColors } from "../../../utils/bgColors";
import { Position } from "reactflow";
import { NodeProps, useReactFlow } from "reactflow";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import DeleteOutlinedIcon from "@mui/icons-material/DeleteOutlined";
import CustomHandle from "./CustomHandle";
import ReminderPanel from "./ReminderPanel";
import DevicePreviewComponent from "../../TemplateComponents/TemplateForm/devicePreview";
import { unescapeJsonString } from "../../../utils/functions";
import { useAppDispatch, useAppSelector } from "../../../utils/redux-hooks";
import { fetchTemplateById } from "../../../redux/slices/Templates/TemplateById";
import { toastActions } from "../../../utils/toastSlice";
import { useWorkflow } from "../../../contexts/WorkflowContext";
import AccessTimeIcon from "@mui/icons-material/AccessTime";

interface ReminderNodeProps {
  data: {
    isMessagePanelOpen: boolean;
    selectedTemplate: any;
    templateObj: any;
    reminderTime: number;
    reminderUnit: string; // 'minutes', 'hours', 'days'
    timeInMinutes: number;
    isValid?: boolean;
    validationErrors?: string[];
  };
  id: string;
}

const ReminderNode: React.FC<ReminderNodeProps> = ({ data, id }) => {
  const {
    isMessagePanelOpen,
    selectedTemplate,
    templateObj,
    reminderTime,
    reminderUnit,
    timeInMinutes,
  } = data;

  const { setNodes } = useReactFlow();
  const [localTemplate, setLocalTemplate] = useState<any>(null);
  const dispatch = useAppDispatch();
  const userData = useAppSelector((state: any) => state?.adminLogin?.data);
  const { handleNodeDelete } = useWorkflow();
  const [currentIndex, setCurrentIndex] = useState(0);

  const updateNodeData = (updateFn: (data: any) => any) => {
    setNodes((nds: any[]) =>
      nds.map((node: any) => {
        if (node.id === id) {
          const updatedData = updateFn(node.data);

          // Only validate if the node has already been marked as invalid
          if (updatedData.isValid === false) {
            // Validate the node data to determine if all errors are resolved
            const isTemplateSelected = !!(
              updatedData.templateObj && updatedData.templateObj.templateId
            );
            const isTimeValid = !!(
              updatedData.reminderTime &&
              updatedData.reminderTime > 0 &&
              updatedData.reminderUnit
            );

            // Only set isValid to true if all validation checks pass
            if (isTemplateSelected && isTimeValid) {
              updatedData.isValid = true;
              updatedData.validationErrors = [];
            }
          }

          return { ...node, data: updatedData };
        }
        return node;
      })
    );
  };

  const handleEdit = () => {
    updateNodeData((data) => ({
      ...data,
      isMessagePanelOpen: !data.isMessagePanelOpen,
    }));
  };

  const handleDelete = () => {
    handleNodeDelete(id);
  };

  const handleSendTemplatePayload = (payload: any, templateState: any) => {
    // First update the node data
    updateNodeData((data) => ({
      ...data,
      templateObj: templateState,
    }));

    // Then update local state
    setLocalTemplate(templateState);
  };

  const handleTimeChange = (time: number, unit: string) => {
    updateNodeData((data) => ({
      ...data,
      timeInMinutes:
        time *
        (unit === "minutes"
          ? 1
          : unit === "hours"
          ? 60
          : unit === "days"
          ? 1440
          : 0),
      reminderTime: time,
      reminderUnit: unit,
    }));
  };

  async function getTemplateById(id: any) {
    try {
      const payload = {
        templateId: id,
        businessId: userData?.companyId,
        userId: userData?.userId,
      };
      const result = await dispatch(fetchTemplateById(payload));
      if (result?.meta?.requestStatus === "fulfilled") {
        setLocalTemplate(result?.payload?.[0]);
        updateNodeData((data) => ({
          ...data,
          selectedTemplate: result?.payload?.[0],
        }));
      } else {
        dispatch(
          toastActions.setToaster({
            message: result?.payload?.message || "Error fetching template",
            type: "error",
          })
        );
      }
    } catch (error) {
      console.error("Error fetching template:", error);
    }
  }

  useEffect(() => {
    if (templateObj?.templateId) {
      getTemplateById(templateObj?.templateId);
    }
  }, [templateObj]);

  const formatReminderTime = () => {
    if (!reminderTime || !reminderUnit) return "No time set";
    return `${reminderTime} ${reminderUnit}`;
  };

  return (
    <Paper
      elevation={3}
      sx={{
        borderRadius: 2,
        position: "relative",
        backgroundColor: "white",
        border: `1px solid ${bgColors.gray2}`,
        boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.1)",
        width: 320,
        minHeight: "200px",
        maxHeight: "400px",
        display: "flex",
        flexDirection: "column",
        cursor: "auto",
      }}
    >
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        sx={{
          backgroundColor: data.isValid === false ? "#FF7171" : bgColors.green,
          color: "white",
          padding: "8px 12px",
          borderTopLeftRadius: "6px",
          borderTopRightRadius: "6px",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          flexShrink: 0,
        }}
      >
        <Box display="flex" alignItems="center" gap={1}>
          <AccessTimeIcon fontSize="small" />
          <Typography variant="h6">Reminder</Typography>
        </Box>
        <Box display="flex" flexDirection="row">
          <IconButton
            size="small"
            sx={{
              background: "transparent",
              border: "none",
              color: "white",
              cursor: "auto",
              padding: "2px",
              borderRadius: "3px",
              "&:hover": {
                backgroundColor: "rgba(255, 255, 255, 0.2)",
              },
            }}
            onClick={handleEdit}
          >
            <EditOutlinedIcon />
          </IconButton>
          <IconButton
            size="small"
            sx={{
              background: "transparent",
              border: "none",
              color: "white",
              cursor: "auto",
              padding: "2px",
              borderRadius: "3px",
              "&:hover": {
                backgroundColor: "rgba(255, 255, 255, 0.2)",
              },
            }}
            onClick={handleDelete}
          >
            <DeleteOutlinedIcon />
          </IconButton>
        </Box>
        <Box sx={{ position: "absolute", top: 100, left: -2 }}>
          <CustomHandle
            type="target"
            position={Position.Left}
            id={`left-nodeId-${id}`}
          />
        </Box>
        <Box sx={{ position: "absolute", top: 100, right: -2 }}>
          <CustomHandle
            type="source"
            position={Position.Right}
            id={`right-nodeId-${id}`}
          />
        </Box>
      </Box>

      <Box
        sx={{
          flexGrow: 1,
          overflow: "auto",
          padding: 2,
          display: "flex",
          flexDirection: "column",
          overflowY: "auto",
          "&::-webkit-scrollbar": {
            width: "6px",
          },
          "&::-webkit-scrollbar-track": {
            background: "#f1f1f1",
            borderRadius: "3px",
          },
          "&::-webkit-scrollbar-thumb": {
            background: "#888",
            borderRadius: "3px",
          },
          "&::-webkit-scrollbar-thumb:hover": {
            background: "#555",
          },
        }}
        className="nowheel"
        onWheel={(e) => {
          e.stopPropagation();
          const container = e.currentTarget;
          container.scrollTop += e.deltaY;
        }}
      >
        {/* Validation Errors */}
        {data.isValid === false && data.validationErrors && (
          <Box
            sx={{
              backgroundColor: "#FFEEEE",
              p: 1,
              mb: 2,
              borderRadius: 1,
              border: "1px solid #FF7171",
            }}
          >
            <Box component="ul" sx={{ m: 0, pl: 2 }}>
              {data.validationErrors.map((error: string, index: number) => (
                <Typography
                  key={index}
                  component="li"
                  variant="caption"
                  color="error"
                >
                  {error}
                </Typography>
              ))}
            </Box>
          </Box>
        )}

        {/* Reminder Time Display */}
        <Box
          sx={{
            backgroundColor: bgColors.blue3,
            p: 2,
            borderRadius: 1,
            mb: 2,
            display: "flex",
            alignItems: "center",
            gap: 1,
          }}
        >
          <AccessTimeIcon color="primary" />
          <Typography variant="body2" fontWeight={600}>
            Send reminder Before: {formatReminderTime()}
          </Typography>
        </Box>

        {/* Template Display */}
        {localTemplate ? (
          <Box>
            <Typography
              variant="body1"
              sx={{
                mb: 2,
                backgroundColor: bgColors.green,
                color: "white",
                borderRadius: "6px",
                padding: "4px 8px",
                flexShrink: 0,
              }}
            >
              {localTemplate?.templateName}
            </Typography>
            <Box
              sx={{
                width: "100%",
                minHeight: "200px",
                overflow: "hidden",
              }}
            >
              <DevicePreviewComponent
                header={localTemplate?.header}
                body={unescapeJsonString(localTemplate?.body)}
                footer={localTemplate?.footer}
                mediaType={localTemplate?.mediaType}
                mediaFile={localTemplate?.mediaFile}
                buttons={localTemplate?.buttons}
                carouselCards={localTemplate?.carouselCards}
                currentIndex={currentIndex}
                setCurrentIndex={setCurrentIndex}
                style={{ maxWidth: "450px" }}
              />
            </Box>
          </Box>
        ) : (
          <Typography variant="body1">
            No template selected. Click the edit icon to configure the reminder.
          </Typography>
        )}
      </Box>
      {isMessagePanelOpen && (
        <ReminderPanel
          id={id}
          handleEdit={handleEdit}
          templateObj={templateObj}
          localTemplate={localTemplate}
          setLocalTemplate={setLocalTemplate}
          handleSendTemplatePayload={handleSendTemplatePayload}
          reminderTime={reminderTime}
          reminderUnit={reminderUnit}
          handleTimeChange={handleTimeChange}
        />
      )}
    </Paper>
  );
};

export default ReminderNode;
