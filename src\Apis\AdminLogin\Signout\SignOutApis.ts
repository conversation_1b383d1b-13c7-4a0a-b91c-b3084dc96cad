/* global process */

import axios from "axios";
import { getStoredTokens } from "../../../utils/authUtils";

const getAuthHeader = () => {
  const tokens = getStoredTokens();
  return tokens?.token ? `Bearer ${tokens?.token}` : "";
};
  
const USER_API_URL = process.env.REACT_APP_BASE_URL;
/* Logout api */
const signOut = () => {
  return axios({
    url: `${USER_API_URL}/Logout`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
      "Cache-Control": "no-cache",
    },
  });
};

export const ADMIN_SIGNOUT_APIS = {
  signOut,
};
