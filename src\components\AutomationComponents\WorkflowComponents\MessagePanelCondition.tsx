import {
  Box,
  Button,
  Checkbox,
  Chip,
  Divider,
  FormControl,
  FormControlLabel,
  FormGroup,
  IconButton,
  InputLabel,
  MenuItem,
  Paper,
  Radio,
  RadioGroup,
  Select,
  TextField,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import CustomResponseComp from "./CustomResponseComp";
import { bgColors } from "../../../utils/bgColors";
import CloseIconSvg from "../../../assets/svgs/CloseIconSvg";
import { ContentState, convertToRaw, EditorState } from "draft-js";

import { formatContent } from "../../../utils/functions";
import { ConditionOperator } from "./enums";
import { useAppSelector } from "../../../utils/redux-hooks";

interface MessagePanelConditionNodeProps {
  conditionType: ConditionOperator;
  attribute: string;
  conditionValue: string;
  selectedPaths?: ("true" | "false")[];
  handleConditionTypeChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  handleAttributeChange: (attribute: string) => void;
  handleConditionValueChange: (value: string) => void;
  handlePathChange?: (paths: ("true" | "false")[]) => void;
  onDelete?: () => void;
  handleEdit: any;
  handleDelete: any;
}

const MessagePanelCondition = ({
  conditionType,
  handleEdit,
  handlePathChange,
  handleConditionTypeChange,
  handleConditionValueChange,
  handleAttributeChange,
  conditionValue,
  attribute,
  selectedPaths = ["true", "false"],
}: MessagePanelConditionNodeProps) => {
  const [error, setError] = useState("");

  const data = useAppSelector(
    (state: any) => state.getSaveResponseAttribute.getSaveResponseAttributeData
  );
  const attributes = data?.data;

  // Local state for input value
  const [inputValue, setInputValue] = useState(conditionValue);

  // Validation functions
  const validateValue = (value: string, attr: string): boolean => {
    switch (attr.toLowerCase()) {
      case "budget":
        return /^\d+$/.test(value);
      case "phone":
        return /^[0-9]{10}$/.test(value);
      case "email":
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
      default:
        return true;
    }
  };

  // Get error message for validation
  const getErrorMessage = (attr: string): string => {
    switch (attr.toLowerCase()) {
      case "budget":
        return "Please enter a valid number";
      case "phone":
        return "Please enter a valid 10-digit phone number";
      case "email":
        return "Please enter a valid email address";
      default:
        return "";
    }
  };

  // Handle value change with validation
  const handleValueChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);

    if (validateValue(newValue, attribute)) {
      setError("");
      handleConditionValueChange(newValue);
    } else {
      setError(getErrorMessage(attribute));
    }
  };

  // Update local state when prop changes
  useEffect(() => {
    setInputValue(conditionValue);
  }, [conditionValue]);

  // Handle path selection
  const handlePathSelect = (path: "true" | "false") => {
    if (handlePathChange) {
      const newSelectedPaths = selectedPaths.includes(path)
        ? selectedPaths.filter((p) => p !== path)
        : [...selectedPaths, path];
      handlePathChange(newSelectedPaths);
    }
  };

  return (
    <Paper
      elevation={4}
      sx={{
        position: "absolute",
        right: -320,
        top: 0,
        width: 300,
        backgroundColor: "white",
        border: `1px solid ${bgColors.gray2}`,
        borderRadius: 2,
        boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
        transition: "transform 0.3s ease",
        zIndex: 10,
      }}
      className="nowheel nopan nodrag"
    >
      <Box
        sx={{
          position: "sticky",
          top: 0,
          backgroundColor: bgColors.green,
          zIndex: 1000,
          padding: "8px 16px",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          borderBottom: `1px solid ${bgColors.gray2}`,
          borderTopLeftRadius: 6,
          borderTopRightRadius: 6,
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            gap: 1,
            width: "100%",
          }}
        >
          <Typography variant="h6" color="white">
            Configure Condition
          </Typography>
          <IconButton
            aria-label="close"
            sx={{
              color: `${bgColors.red}`,
              width: 48,
              height: 48,
              "& svg": {
                width: 40,
                height: 40,
                fill: `${bgColors.red1}`,
              },
            }}
            onClick={() => handleEdit()}
          >
            <CloseIconSvg />
          </IconButton>
        </Box>
      </Box>
      <Box p={2}>
        <Box display="flex" flexDirection={"column"} gap={2}>
          <FormControl fullWidth>
            <InputLabel id="condition-type-label">Condition Type</InputLabel>
            <Select
              labelId="condition-type-label"
              value={conditionType}
              label="Condition Type"
              onChange={(e: any) => handleConditionTypeChange(e)}
            >
              {Object.entries(ConditionOperator)
                .filter((v, i) => i < 7)
                .map(([key, value]) => (
                  <MenuItem key={key} value={key}>
                    {value}
                  </MenuItem>
                ))}
            </Select>
          </FormControl>

          <FormControl fullWidth>
            <InputLabel id="attribute-label">Attribute</InputLabel>
            <Select
              labelId="attribute-label"
              value={attribute}
              label="Attribute"
              onChange={(e) => {
                handleAttributeChange(e.target.value);
                setInputValue("");
                setError("");
              }}
            >
              {attributes.map((attr: any) => (
                <MenuItem key={attr.id} value={attr.name.toLowerCase()}>
                  {attr.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <TextField
            fullWidth
            label="Value"
            value={inputValue}
            onChange={handleValueChange}
            error={!!error}
            helperText={error}
            placeholder={`Enter ${attribute} value`}
            variant="outlined"
            type={
              attribute.toLowerCase() === "budget" ||
              attribute.toLowerCase() === "phone"
                ? "number"
                : "text"
            }
          />

          <FormGroup>
            <FormControlLabel
              control={
                <Checkbox
                  checked={selectedPaths.includes("true")}
                  onChange={() => handlePathSelect("true")}
                  sx={{
                    color: "#4CAF50",
                    "&.Mui-checked": {
                      color: "#4CAF50",
                    },
                  }}
                />
              }
              label="True Path"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={selectedPaths.includes("false")}
                  onChange={() => handlePathSelect("false")}
                  sx={{
                    color: "#EB5757",
                    "&.Mui-checked": {
                      color: "#EB5757",
                    },
                  }}
                />
              }
              label="False Path"
            />
          </FormGroup>
        </Box>
      </Box>
    </Paper>
  );
};

export default MessagePanelCondition;
