import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { WORKFLOW_API } from "../../../Apis/AdminLogin/Automation/Workflows";
import { createKeywordReactflow, createKeywordReactflowSlice } from "./createKeywordsReactflowSlice";

export interface IData {
  deleteKeywordReactflowStatus: "loading" | "succeeded" | "failed" | "idle";
  deleteKeywordReactflowData: any;
}

const initialState: IData = {
  deleteKeywordReactflowStatus: "idle",
  deleteKeywordReactflowData: null,
};

export const deleteKeywordReactflow = createAsyncThunk(
  "deleteKeywordReactflow",
  async (data: any, { rejectWithValue }) => {
    try {
      const response = await WORKFLOW_API.deleteKeywordReactflow(data);
      return response?.data;
    } catch (error: any) {
      // Provide a descriptive error message
      return rejectWithValue(error.response || "An error occurred");
    }
  }
) as any;

export const deleteKeywordReactflowSlice = createSlice({
  name: "deleteKeywordReactflowSlice",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(deleteKeywordReactflow.pending, (state) => {
        state.deleteKeywordReactflowStatus = "loading";
        state.deleteKeywordReactflowData = null;
      })
      .addCase(deleteKeywordReactflow.fulfilled, (state, action) => {
        state.deleteKeywordReactflowStatus = "succeeded";
        state.deleteKeywordReactflowData = action.payload;
      })
      .addCase(deleteKeywordReactflow.rejected, (state) => {
        state.deleteKeywordReactflowStatus = "failed";
      });
  },
});

export const deleteKeywordActions = deleteKeywordReactflowSlice.actions;
export default deleteKeywordReactflowSlice.reducer;
