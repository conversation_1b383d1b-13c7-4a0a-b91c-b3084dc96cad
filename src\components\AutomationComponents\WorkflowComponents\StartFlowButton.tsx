import React, { useState, useRef, useEffect } from "react";
import { Box, Button, IconButton, useTheme } from "@mui/material";
import { AiFillPlusCircle } from "react-icons/ai";
import { Close as CloseIcon } from "@mui/icons-material";
import StartFlowSidebar from "./StartFlowSidebar";
import { bgColors } from "../../../utils/bgColors";

interface StartFlowButtonProps {
  messages: any[];
  actions: any[];
  onDragStart: (event: React.DragEvent, label: string) => void;
  onClick: (label: string) => void;
  nodes?: any[];
}

const StartFlowButton: React.FC<StartFlowButtonProps> = ({
  messages,
  actions,
  onDragStart,
  onClick,
  nodes = [],
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const popoverRef = useRef<HTMLDivElement>(null);
  const theme = useTheme();

  const handleStartClick = () => {
    setIsOpen((prev) => !prev);
  };

  const handleClose = () => {
    setIsOpen(false);
  };

  return (
    <Box
      sx={{
        position: "absolute",
        top: { xs: "120px", sm: "140px", md: "150px" },
        left: { xs: "200px", sm: "240px", md: "370px" },
        zIndex: 1100,
      }}
    >
      <Button
        sx={{
          position: "relative",
          p: 0,
          border: `1px solid ${bgColors.gray1}`,
          background: `${bgColors.green1}`,
          width: { xs: "80px", sm: "90px", md: "110px" },
          height: { xs: "30px", sm: "35px", md: "40px" },
          fontSize: { xs: "12px", sm: "14px", md: "16px" },
        }}
        onClick={handleStartClick}
      >
        Start
        <Box
          sx={{
            position: "absolute",
            bottom: { xs: "-10px", sm: "-12px", md: "-15px" },
            right: { xs: "-4px", sm: "-5px", md: "-7px" },
          }}
        >
          <Box sx={{ fontSize: { xs: "16px", sm: "20px", md: "24px" } }}>
            <AiFillPlusCircle style={{ color: `${bgColors.green}` }} />
          </Box>
        </Box>
      </Button>

      {isOpen && (
        <Box
          ref={popoverRef}
          sx={{
            position: "absolute",
            top: { xs: "40px", sm: "45px", md: "60px" },
            left: { xs: "0", sm: "0", md: "0" },
            width: { xs: "180px", sm: "320px", md: "320px" },
            height: "auto",
            maxHeight: { xs: "calc(100vh - 100px)", sm: "calc(100vh - 120px)", md: "60vh" },
            display: "flex",
            flexDirection: "column",
            borderRadius: "8px",
            backgroundColor: "white",
            zIndex: 1200,
            boxShadow: theme.shadows[3],
          }}
        >
          <Box
            sx={{
              position: "relative",
              padding: "8px",
              borderBottom: `1px solid ${bgColors.gray1}`,
              backgroundColor: "white",
              height: "40px",
              zIndex: 1,
            }}
          >
            <IconButton
              onClick={handleClose}
              sx={{
                position: "absolute",
                top: "50%",
                right: 8,
                transform: "translateY(-50%)",
                padding: "4px",
              }}
            >
              <CloseIcon fontSize="small" />
            </IconButton>
          </Box>
          <Box
            sx={{
              flex: 1,
              overflowY: "auto",
              padding: "8px",
              "&::-webkit-scrollbar": {
                width: "4px",
              },
              "&::-webkit-scrollbar-track": {
                background: "#f1f1f1",
              },
              "&::-webkit-scrollbar-thumb": {
                background: "#888",
                borderRadius: "2px",
              },
            }}
          >
            <StartFlowSidebar
              messages={messages}
              actions={actions}
              onDragStart={onDragStart}
              draggable={false}
              onClick={onClick}
              nodes={nodes}
            />
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default StartFlowButton; 