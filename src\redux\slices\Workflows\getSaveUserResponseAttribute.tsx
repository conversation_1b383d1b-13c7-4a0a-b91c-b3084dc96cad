import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { WORKFLOW_API } from "../../../Apis/AdminLogin/Automation/Workflows";

export interface IData {
    getSaveResponseAttributeStatus: "loading" | "succeeded" | "failed" | "idle";
    getSaveResponseAttributeData: any;
  }

  const initialState: IData = {
    getSaveResponseAttributeStatus: "idle",
    getSaveResponseAttributeData: null,
  };

  export const getSaveResponseAttribute = createAsyncThunk(
    "workflow/getSaveResponseAttribute",
    async () => {
      try {
        const response = await WORKFLOW_API.getSaveResponseAttribute();
        return response?.data;
      } catch (error: any) {
        // Provide a descriptive error message
        return 'An error occurred';
      }
    }
  );


  export const getSaveResponseAttributeSlice = createSlice({
    name: "getSaveResponseAttributeSlice",
    initialState,
    reducers: {
       
    },
    extraReducers: (builder) => {
      builder
        .addCase(getSaveResponseAttribute.pending, (state) => {
          state.getSaveResponseAttributeStatus = "loading";
          state.getSaveResponseAttributeData = null;
        })
        .addCase(getSaveResponseAttribute.fulfilled, (state, action) => {
          state.getSaveResponseAttributeStatus = "succeeded";
          state.getSaveResponseAttributeData = action.payload;
        })
        .addCase(getSaveResponseAttribute.rejected, (state) => {
          state.getSaveResponseAttributeStatus = "failed";
        });
    },
  });


export const getSaveResponseAttributeActions = getSaveResponseAttributeSlice.actions;
export default getSaveResponseAttributeSlice.reducer;
