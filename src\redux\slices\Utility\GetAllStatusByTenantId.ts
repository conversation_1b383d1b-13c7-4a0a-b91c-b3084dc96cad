import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { LEADRAT_QA_APIS } from "../../../Apis/Utility/leadratApis";

const initialState = {
  status: "idle",
  data: null,
};

export const GetAllStatusByTenantId = createAsyncThunk(
  "GetAllStatusByTenantId",
  async (data: any) => {
    const response = await LEADRAT_QA_APIS.getAllWorkflowStatusByTenantId(data);
    return response?.data;
  }
);

export const GetAllStatusByTenantIdSlice = createSlice({
  name: "GetAllStatusByTenantIdSlice",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(GetAllStatusByTenantId.pending, (state) => {
        state.status = "loading";
      })
      .addCase(GetAllStatusByTenantId.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.data = action.payload;
      })
      .addCase(GetAllStatusByTenantId.rejected, (state) => {
        state.status = "failed";
      });
  },
});

export const GetAllStatusByTenantIdActions =
  GetAllStatusByTenantIdSlice.actions;
export default GetAllStatusByTenantIdSlice.reducer;
