import React, { useEffect, useRef, useState } from "react";
import { Box, Typography, Grid } from "@mui/material";
import CloseSvg from "../../assets/svgs/CloseSvg";
import { makeStyles } from "@mui/styles";
import { bgColors } from "../../utils/bgColors";
import ButtonComponent from "../common/ButtonComponent";
import { useAppDispatch, useAppSelector } from "../../utils/redux-hooks";
import { toastActions } from "../../utils/toastSlice";
import { createTemplate } from "../../redux/slices/Templates/CreateTemplateSlice";
import LoadingComponent from "../common/LoadingComponent";
import { fetchTemplateById } from "../../redux/slices/Templates/TemplateById";
import TemplateDialogContent from "./TemplateForm/templateFormDialogContent";
import { updateTemplate } from "../../redux/slices/Templates/UpdateTemplateSlice";
import {
  extractLeadratVariables,
  extractVariables,
  ProcessButtonData,
  updateVariables,
} from "./TemplateForm/functions";
import useValidation from "./TemplateForm/validations";
import { fetchTemplateName } from "../../redux/slices/Templates/GetTemplateNameSlice";
import useDebouncedFetch from "../../utils/debounceHook";
import { createAuthTemplate } from "../../redux/slices/Templates/CreateAuthTemplateSlice";
import { previewAuthTemplate } from "../../redux/slices/Templates/PreviewAuthTemplateSlice";
import { useNavigate, useParams } from "react-router-dom";
import { OtpType } from "../../utils/enums";
import { buttonsArray, maxLength } from "../../utils/constants";
import {
  formatCarouselPayload,
  getLangValue,
  refactorCarouselCards,
} from "../../utils/functions";
import TemplateSvg from "../../assets/svgs/TemplateSvg";
import {
  ButtonType,
  TemplateData,
  TemplateState,
  FormErrors,
} from "../../utils/interfaces";
import { createCarouselTemplate } from "../../redux/slices/Templates/CreateCarouselTemplate";
import ConfirmationDailog from "./TemplateForm/ConfirmationDailog";
import CommonHeader from "../common/CommonHeader";

const useStyles = makeStyles({
  mainContainer: {
    backgroundColor: bgColors.white1,
    height: "100vh", // uncomment to fix the height of the template making only inner content scrollable
    width: "100%",
  },
  bgContainer: {
    backgroundColor: bgColors.white,
    height: "100%",
    width: "100%",
    overFlow: "hidden !important",
    display: "flex",
    flexDirection: "column",
  },
  headerContainer: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    borderBottom: "1px solid #f0f0f0",
    width: "100%",
    paddingLeft: "24px",
    paddingRight: "24px",
    paddingTop: "8px",
    paddingBottom: "8px",
  },
  dialogBox: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  draftButton: {
    backgroundColor: `${bgColors.white} !important`,
    width: "120px !important",
    color: `${bgColors.black} !important`,
    fontSize: "14px !important",
    fontWeight: "Semi Bold !important",
    borderRadius: "8px !important",
  },
  button: {
    backgroundColor: `${bgColors.green} !important`,
    width: "120px !important",
    fontSize: "14px !important",
    fontWeight: "Semi Bold !important",
    borderRadius: "8px !important",
  },
  cursor: {
    cursor: "pointer",
    fontSize: "10px",
  },
});

const TemplateComponent = () => {
  const classes = useStyles();
  const dispatch = useAppDispatch();
  const bodyRef = useRef<HTMLTextAreaElement>(null);
  const headerRef = useRef<HTMLInputElement>(null);
  const urlButtonRef = useRef<HTMLInputElement>(null);
  const { templatesId: toEditTemplateId } = useParams();
  const navigate = useNavigate();
  const userData = useAppSelector((state: any) => state?.adminLogin?.data);
  const templateNameSlice = useAppSelector(
    (state: any) => state?.templateNameData
  );
  const templateNameData = templateNameSlice?.data;

  const createAuthTemplateSlice = useAppSelector(
    (state: any) => state?.createAuthTemplateData
  );
  const createTemplateSlice = useAppSelector(
    (state: any) => state?.createTemplateData
  );
  const uploadFileSlice = useAppSelector((state: any) => state?.uploadFileData);
  const updateTemplateSlice = useAppSelector(
    (state: any) => state?.updateTemplateData
  );
  const templateByIdSlice = useAppSelector(
    (state: any) => state?.templateByIdData
  );

  const createCarouselTemplateSlice = useAppSelector(
    (state: any) => state.createCarouselTemplate
  );

  const [templateState, setTemplateState] = useState<TemplateState>({
    templateName: "",
    category: 1,
    language: 15,
    subCategory: "",
    mediaType: 1,
    mediaFile: null,
    header: "",
    body: "",
    callButtonName: "",
    phoneNumber: "",
    countryCode: "",
    urlButtonName: [],
    redirectUrl: [],
    quickReply: [],
    footer: "",
    buttons: [],
    variables: [],
    leadratVariables: [],
    codeDeliverySetup: "COPY_CODE",
    zeroTapAuthentication: false,
    appSetup: [{ appPackageName: "", appSignatureHash: "" }],
    addSecurityRecommendation: true,
    addExpiryTime: false,
    codeExpiresIn: 10,
    autoFillButtonText: "Auto fill",
    copyCodeButtonText: "Copy code",
    messageValidity: true,
    messageValidityPeriod: 300,
    carouselCards: [],
  });

  const [emojiPopoverOpen, setEmojiPopoverOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<HTMLDivElement | null>(null);
  const [formErrors, setFormErrors] = useState<any>({});
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);

  const { isFormValid } = useValidation({
    templateState,
    formErrors,
    setFormErrors,
  });

  const checkTemplateName = useDebouncedFetch(fetchTemplateName, 0);

  const cleanTemplateData = (data: any) => {
    const cleanedData: any = {};
    for (const key in data) {
      if (
        data[key] !== null &&
        data[key] !== undefined &&
        data[key] !== "" &&
        !(Array.isArray(data[key]) && data[key]?.length === 0)
      ) {
        cleanedData[key] = data[key];
      }
    }
    return cleanedData;
  };

  const handleSaveTemplate = async (draft: boolean) => {
    if (templateState?.mediaType === 6) {
      if (isFormValid()?.isValid) {
        const cleanedData = cleanTemplateData(templateState);
        delete cleanedData.variables;
        delete cleanedData.leadratVariables;
        const payload = formatCarouselPayload(
          templateState,
          userData?.companyId,
          userData?.userId
        );
        if (toEditTemplateId) {
          payload.templateId = toEditTemplateId;
        }
        const data = {
          draft: draft,
          payload: payload,
        };
        try {
          // Dispatch the action and wait for the result
          let apiResponse: any;
          apiResponse = await dispatch(createCarouselTemplate(data));

          // Check if the API call was successful
          if (apiResponse?.meta?.requestStatus === "fulfilled") {
            dispatch(
              toastActions.setToaster({
                type: "success",
                message: apiResponse?.payload?.message,
              })
            );
            handleDeleteTemplate();
          } else {
            dispatch(
              toastActions.setToaster({
                type: "error",
                message: apiResponse?.payload,
              })
            );
          }
        } catch (error: any) {
          dispatch(
            toastActions.setToaster({
              type: "error",
              message: error?.message,
            })
          );
        }
      }
    } else if (templateState?.category === 3) {
      const cleanedData = cleanTemplateData(templateState);
      delete cleanedData.variables;
      delete cleanedData.leadratVariables;

      const payload = {
        businessId: userData.companyId,
        id: toEditTemplateId ? templateByIdSlice?.data[0]?.templateId : null,
        name: cleanedData.templateName,
        body: cleanedData.body,
        footer: cleanedData.footer || "",
        languages: [cleanedData.language],
        category: cleanedData.category,
        subCategory: "Test",
        messageSendTtlSeconds: cleanedData.messageValidity
          ? cleanedData.messageValidityPeriod
          : 0,
        addSecurityRecommendation: cleanedData.addSecurityRecommendation,
        codeExpirationMinutes: cleanedData?.addExpiryTime
          ? cleanedData.codeExpiresIn
          : 0,
        otpType: OtpType[cleanedData.codeDeliverySetup],
        supportedApps: [],
        button: {
          text: cleanedData.copyCodeButtonText,
          autofillText: cleanedData.autoFillButtonText,
        },
      };

      try {
        let apiResponse: any;
        // if (
        //   toEditTemplateId &&
        //   templateByIdSlice?.data[0]?.status !== 4
        // ) {
        //   // Edit case

        //   cleanedData.templateId = toEditTemplateId;
        //   apiResponse = await dispatch(updateTemplate(cleanedData));
        // } else {
        // Create case
        // cleanedData.draft = draft;

        // if (toEditTemplateId) {
        //   cleanedData.templateId = toEditTemplateId;
        // }

        // Dispatch the action and wait for the result
        apiResponse = await dispatch(createAuthTemplate(payload));
        // }

        if (apiResponse?.meta?.requestStatus === "fulfilled") {
          dispatch(
            toastActions.setToaster({
              type: "success",
              message: apiResponse?.payload?.message,
            })
          );
          handleDeleteTemplate();
        } else {
          dispatch(
            toastActions.setToaster({
              type: "error",
              message: apiResponse?.payload,
            })
          );
        }
      } catch (error: any) {
        dispatch(
          toastActions.setToaster({
            type: "error",
            message: error?.message,
          })
        );
      }
    } else if (isFormValid()?.isValid) {
      const { buttons, ...restTemplateState } = templateState;
      const { updatedState, quickReply, redirectUrl, urlButtonName } =
        ProcessButtonData(restTemplateState, buttons);

      const data: TemplateData = {
        ...updatedState,
        businessId: userData?.companyId,
        userId: userData?.userId,
        language: templateState?.language,
        redirectUrl,
        urlButtonName,
        quickReply,
      };

      const cleanedData: any = cleanTemplateData(data);

      // Remove 'variables' from cleanedData
      delete cleanedData.variables;
      delete cleanedData.leadratVariables;
      delete cleanedData.addExpiryTime;
      delete cleanedData.addSecurityRecommendation;
      delete cleanedData.codeExpiresIn;
      delete cleanedData.messageValidity;
      delete cleanedData.messageValidityPeriod;
      delete cleanedData.zeroTapAuthentication;

      try {
        let apiResponse: any;
        if (toEditTemplateId && templateByIdSlice?.data[0]?.status !== 4) {
          // Edit case

          cleanedData.templateId = toEditTemplateId;
          apiResponse = await dispatch(updateTemplate(cleanedData));
        } else {
          // Create case
          cleanedData.draft = draft;

          if (toEditTemplateId) {
            cleanedData.templateId = toEditTemplateId;
          }

          // Dispatch the action and wait for the result
          apiResponse = await dispatch(createTemplate(cleanedData));
        }

        if (apiResponse?.meta?.requestStatus === "fulfilled") {
          dispatch(
            toastActions.setToaster({
              type: "success",
              message: apiResponse?.payload?.message,
            })
          );
          handleDeleteTemplate();
        } else {
          dispatch(
            toastActions.setToaster({
              type: "error",
              message: apiResponse?.payload,
            })
          );
        }
      } catch (error: any) {
        dispatch(
          toastActions.setToaster({
            type: "error",
            message: error?.message,
          })
        );
      }
    }
  };

  const handleDeleteTemplate = () => {
    setFormErrors({
      templateName: "",
      category: "",
      subCategory: "",
      mediaType: "",
      header: "",
      mediaFile: "",
      body: "",
      carouselCardsValid: "",
      carouselCards: [],
    });
    setTemplateState({
      templateName: "",
      category: 1,
      language: 15,
      subCategory: "",
      mediaType: 1,
      mediaFile: null,
      header: "",
      body: "",
      footer: "",
      callButtonName: "",
      phoneNumber: "",
      countryCode: "",
      urlButtonName: [],
      redirectUrl: [],
      quickReply: [],
      buttons: [],
      variables: [],
      leadratVariables: [],
      codeDeliverySetup: "COPY_CODE",
      zeroTapAuthentication: false,
      appSetup: [{ appPackageName: "", appSignatureHash: "" }],
      addSecurityRecommendation: true,
      addExpiryTime: false,
      codeExpiresIn: 10,

      autoFillButtonText: "Auto fill",
      copyCodeButtonText: "Copy code",
      messageValidity: true,
      messageValidityPeriod: 300,
      carouselCards: [],
    });
    navigate(-1);
  };

  const handleChange = async (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value, checked }: any = event.target;

    if (name === "language") {
      if (templateState.category === 3) {
        try {
          let payload = {
            companyId: userData.companyId,
            languages: value,
            addSecurityRecommendation: templateState.addSecurityRecommendation,
            buttonTypes: "OTP",
            messageSendTtlSeconds: templateState.messageValidityPeriod,
            codeExpirationMinutes: templateState.addExpiryTime
              ? templateState.codeExpiresIn
              : 0,
          };

          // Dispatch the action and wait for the result
          let apiResponse = await dispatch(previewAuthTemplate(payload));
          // }

          const data = apiResponse?.payload?.data[0];
          const buttonsArray: Array<ButtonType> = Object.entries(
            data?.buttons[0]
          )
            .filter((button) => {
              return button[0] === "text";
            })
            .map((button) => {
              return {
                buttonType: button[0],
                buttonValue: button[1] || "",
                countryCode: templateState?.countryCode,
              };
            });
          setTemplateState((prevState) => ({
            ...prevState,
            body: data?.body,
            autoFillButtonText: data?.buttons[0]?.autofill_text,
            copyCodeButtonText: data?.buttons[0]?.text,
            footer: data?.footer,
            language: data?.language,
            buttons: buttonsArray,
          }));
        } catch (error: any) {
          dispatch(
            toastActions.setToaster({
              type: "error",
              message: error?.message,
            })
          );
        }
      } else {
        setTemplateState((prevstate) => ({
          ...prevstate,
          language: value,
        }));
      }

      return;
    }

    if (name === "messageValidity") {
      try {
        let payload = {
          companyId: userData.companyId,
          languages: templateState.language,
          addSecurityRecommendation: templateState.addSecurityRecommendation,
          buttonTypes: "OTP",
          messageSendTtlSeconds: checked
            ? templateState.messageValidityPeriod
            : 0,
          codeExpirationMinutes: templateState.addExpiryTime
            ? templateState.codeExpiresIn
            : 0,
        };

        // Dispatch the action and wait for the result
        let apiResponse = await dispatch(previewAuthTemplate(payload));
        // }

        const data = apiResponse?.payload?.data[0];
        const buttonsArray: Array<ButtonType> = Object.entries(data.buttons[0])
          .filter((button) => {
            return button[0] === "text";
          })
          .map((button) => {
            return {
              buttonType: button[0],
              buttonValue: button[1] || "",
              countryCode: templateState?.countryCode,
            };
          });
        setTemplateState((prevState) => ({
          ...prevState,
          body: data?.body,
          autoFillButtonText: data?.buttons[0].autofill_text,
          copyCodeButtonText: data?.buttons[0].text,
          footer: data?.footer,
          messageValidity: checked,
          messageValidityPeriod: 300,
          buttons: buttonsArray,
        }));
      } catch (error: any) {
        dispatch(
          toastActions.setToaster({
            type: "error",
            message: error?.message,
          })
        );
      }

      return;
    }

    if (name === "messageValidityPeriod") {
      try {
        let payload = {
          companyId: userData.companyId,
          languages: templateState.language,
          addSecurityRecommendation: templateState.addSecurityRecommendation,
          buttonTypes: "OTP",
          messageSendTtlSeconds: value,
          codeExpirationMinutes: templateState.addExpiryTime
            ? templateState.codeExpiresIn
            : 0,
        };

        // Dispatch the action and wait for the result
        let apiResponse = await dispatch(previewAuthTemplate(payload));
        // }

        const data = apiResponse?.payload?.data[0];
        const buttonsArray: Array<ButtonType> = Object.entries(data.buttons[0])
          .filter((button) => {
            return button[0] === "text";
          })
          .map((button) => {
            return {
              buttonType: button[0],
              buttonValue: button[1] || "",
              countryCode: templateState?.countryCode,
            };
          });
        setTemplateState((prevState) => ({
          ...prevState,
          body: data?.body,
          autoFillButtonText: data?.buttons[0].autofill_text,
          copyCodeButtonText: data?.buttons[0].text,
          footer: data?.footer,
          messageValidityPeriod: value,
          buttons: buttonsArray,
        }));
      } catch (error: any) {
        dispatch(
          toastActions.setToaster({
            type: "error",
            message: error?.message,
          })
        );
      }
    }

    if (name === "codeExpiresIn") {
      try {
        let payload = {
          companyId: userData.companyId,
          languages: templateState.language,
          addSecurityRecommendation: templateState.addSecurityRecommendation,
          buttonTypes: "OTP",
          messageSendTtlSeconds: templateState.messageValidityPeriod,
          codeExpirationMinutes: value || 0,
        };

        // Dispatch the action and wait for the result
        let apiResponse = await dispatch(previewAuthTemplate(payload));
        // }

        if (apiResponse?.payload?.error) {
          throw new Error(
            apiResponse?.payload?.error?.message ||
              JSON.parse(apiResponse?.payload)?.error?.message
          );
        }
        const data = apiResponse?.payload?.data[0];
        const buttonsArray: Array<ButtonType> = Object.entries(data?.buttons[0])
          .filter((button) => {
            return button[0] === "text";
          })
          .map((button) => {
            return {
              buttonType: button[0],
              buttonValue: button[1] || "",
              countryCode: templateState?.countryCode,
            };
          });
        setTemplateState((prevState) => ({
          ...prevState,
          body: data?.body,
          buttons: buttonsArray,
          footer: data?.footer,
          codeExpiresIn: value,
        }));
      } catch (error: any) {
        dispatch(
          toastActions.setToaster({
            type: "error",
            message: error?.message,
          })
        );
      }

      return;
    }

    if (name === "addExpiryTime") {
      try {
        let payload = {
          companyId: userData.companyId,
          languages: templateState.language,
          addSecurityRecommendation: templateState.addSecurityRecommendation,
          buttonTypes: "OTP",
          messageSendTtlSeconds: templateState.messageValidityPeriod,
          codeExpirationMinutes: checked ? 10 : 0,
        };

        // Dispatch the action and wait for the result
        let apiResponse = await dispatch(previewAuthTemplate(payload));
        // }

        const data = apiResponse?.payload?.data[0];
        const buttonsArray: Array<ButtonType> = Object.entries(data.buttons[0])
          .filter((button) => {
            return button[0] === "text";
          })
          .map((button) => {
            return {
              buttonType: button[0],
              buttonValue: button[1] || "",
              countryCode: templateState?.countryCode,
            };
          });
        setTemplateState((prevState) => ({
          ...prevState,
          body: data?.body,
          autoFillButtonText: data?.buttons[0].autofill_text,
          copyCodeButtonText: data?.buttons[0].text,
          footer: data?.footer,
          addExpiryTime: checked,
          codeExpiresIn: 10,
          buttons: buttonsArray,
        }));
      } catch (error: any) {
        dispatch(
          toastActions.setToaster({
            type: "error",
            message: error?.message,
          })
        );
      }

      return;
    }

    if (name === "addSecurityRecommendation") {
      try {
        let payload = {
          companyId: userData.companyId,
          languages: templateState.language,
          addSecurityRecommendation: checked,
          buttonTypes: "OTP",
          messageSendTtlSeconds: templateState.messageValidityPeriod,
          codeExpirationMinutes: templateState.addExpiryTime
            ? templateState.codeExpiresIn
            : 0,
        };

        // Dispatch the action and wait for the result
        let apiResponse = await dispatch(previewAuthTemplate(payload));
        // }

        const data = apiResponse?.payload?.data[0];
        const buttonsArray: Array<ButtonType> = Object.entries(data.buttons[0])
          .filter((button) => {
            return button[0] === "text";
          })
          .map((button) => {
            return {
              buttonType: button[0],
              buttonValue: button[1] || "",
              countryCode: templateState?.countryCode,
            };
          });
        setTemplateState((prevState) => ({
          ...prevState,
          body: data?.body,
          autoFillButtonText: data?.buttons[0].autofill_text,
          copyCodeButtonText: data?.buttons[0].text,
          footer: data?.footer,
          addSecurityRecommendation: checked,
          language: data?.language,
          buttons: buttonsArray,
        }));
      } catch (error: any) {
        dispatch(
          toastActions.setToaster({
            type: "error",
            message: error?.message,
          })
        );
      }

      return;
    }

    if (name === "zeroTapAuthentication") {
      setTemplateState((prevState) => ({
        ...prevState,
        zeroTapAuthentication: checked,
      }));
      return;
    }

    if (name === "templateName") {
      const data = {
        businessId: userData?.companyId,
        templateName: value,
      };
      checkTemplateName(data);
    }

    if (name === "category") {
      if (value === 3) {
        setFormErrors({
          subCategory: "",
          mediaType: "",
          header: "",
          mediaFile: "",
          body: "",
          carouselCardsValid: "",
          carouselCards: [],
        });

        setTemplateState((prevState: any) => ({
          ...prevState,
          subCategory: "",
          mediaType: 1,
          mediaFile: null,
          header: "",
          body: "",
          callButtonName: "",
          phoneNumber: "",
          countryCode: "",
          urlButtonName: [],
          redirectUrl: [],
          quickReply: [],
          footer: "",
          buttons: [],
          variables: [],
          leadratVariables: [],
          category: value,
          codeDeliverySetup: "COPY_CODE",
          zeroTapAuthentication: false,
          appSetup: [{ appPackageName: "", appSignatureHash: "" }],
          addSecurityRecommendation: true,
          addExpiryTime: false,
          codeExpiresIn: 10,

          autoFillButtonText: "Auto fill",
          copyCodeButtonText: "Copy code",
          messageValidity: true,
          messageValidityPeriod: 300,
          carouselCards: [],
        }));

        try {
          let apiResponse: any;
          let payload = {
            companyId: userData.companyId,
            languages: templateState.language,
            category: value,
            addSecurityRecommendation: templateState.addSecurityRecommendation,
            buttonTypes: "OTP",
            messageSendTtlSeconds: templateState.messageValidityPeriod,
          };

          // Dispatch the action and wait for the result
          apiResponse = await dispatch(previewAuthTemplate(payload));
          // }

          const data = apiResponse?.payload?.data[0];
          const buttonsArray: Array<ButtonType> = Object.entries(
            data.buttons[0]
          )
            .filter((button) => {
              return button[0] === "text";
            })
            .map((button) => {
              return {
                buttonType: button[0],
                buttonValue: button[1] || "",
                countryCode: templateState?.countryCode,
              };
            });

          setTemplateState((prevState) => ({
            ...prevState,
            body: data?.body,
            autoFillButtonText: data?.buttons[0].autofill_text,
            copyCodeButtonText: data?.buttons[0].text,
            footer: data?.footer,
            buttons: buttonsArray,
          }));
        } catch (error: any) {
          dispatch(
            toastActions.setToaster({
              type: "error",
              message: error?.message,
            })
          );
        }
      } else {
        setFormErrors({
          subCategory: "",
          mediaType: "",
          header: "",
          mediaFile: "",
          body: "",
          carouselCardsValid: "",
          carouselCards: [],
        });

        setTemplateState((prevState: any) => ({
          ...prevState,
          subCategory: "",
          mediaType: 1,
          mediaFile: null,
          header: "",
          body: "",
          callButtonName: "",
          phoneNumber: "",
          countryCode: "",
          urlButtonName: [],
          redirectUrl: [],
          quickReply: [],
          footer: "",
          buttons: [],
          variables: [],
          leadratVariables: [],
          category: value,
          codeDeliverySetup: null,
          zeroTapAuthentication: null,
          appSetup: [],
          addSecurityRecommendation: null,
          addExpiryTime: null,
          codeExpiresIn: null,

          autoFillButtonText: null,
          copyCodeButtonText: null,
          messageValidity: null,
          messageValidityPeriod: null,
          carouselCards: [],
        }));
      }
    }

    if (name === "buttons") {
      if (value !== "None") {
        setTemplateState((prevState) => ({
          ...prevState,
          [name]: [
            {
              buttonType: value,
              buttonValue: "",
              countryCode: "",
              buttonName: "", // Optional property
            },
          ],
        }));
      } else {
        setTemplateState((prevState) => ({
          ...prevState,
          [name]: [],
        }));
      }
    } else {
      setTemplateState((prevState) => ({
        ...prevState,
        [name]: value,
      }));
      // Filter out variables not present in the body/header text
      if (name === "body" || name === "header") {
        const leadratVariableIds: any =
          value.match(/#([^\s#][\w\s]*[^\s#])#/g) || [];

        const updatedLeadratVariables = templateState.leadratVariables?.filter(
          (leadratvariable: any) =>
            leadratvariable.type !== name ||
            leadratVariableIds.includes(leadratvariable.id)
        );
        const variableIds: any = value.match(/{{(\d+)}}/g) || [];
        const updatedVariables =
          templateState.variables?.filter(
            (variable) =>
              variable.type !== name || variableIds.includes(variable.id)
          ) || [];
        setTemplateState((prevState) => ({
          ...prevState,
          variables: updatedVariables,
          leadratVariables: updatedLeadratVariables,
        }));
      }
      // Perform validation
      const { isFormValid } = useValidation({
        templateState: { ...templateState, [name]: value },
        formErrors,
        setFormErrors,
      });

      setFormErrors(isFormValid()?.errors);
    }
  };

  const handleAddVariable = (
    type: string,
    urlButtonIndex?: number,
    buttonIndex?: number
  ) => {
    // Get current template state
    const bodyText = templateState?.body || "";
    const headerText = templateState?.header || "";
    const urlButtons =
      templateState?.buttons?.filter(
        (button: any) => button?.buttonType === "URL"
      ) || [];

    // Determine input references and positions based on type
    let input, startPos, endPos, newText;

    if (type === "body") {
      input = bodyRef?.current;
      newText = bodyText;
    } else if (type === "header") {
      input = headerRef?.current;
      newText = headerText;
    } else if (type === "url Button") {
      // Check if a specific URL button is targeted
      if (
        typeof buttonIndex === "number" &&
        urlButtons[urlButtonIndex ? urlButtonIndex : 0]
      ) {
        input = urlButtonRef?.current;
        newText = urlButtons[urlButtonIndex ? urlButtonIndex : 0].buttonValue;
      }
    }

    // If input is undefined, exit early
    if (!input) return;

    // Assign default values to startPos and endPos if they are null or undefined
    startPos = input.selectionStart ?? 0;
    endPos = input.selectionEnd ?? 0;

    // Update variables with the proper count for the type
    const { newVariables, newCount } = updateVariables(
      type,
      templateState,
      urlButtonIndex
    );

    const variableText =
      type === "url Button" ? `{{${newCount}}}` : ` {{${newCount}}} `;

    // Update the appropriate text based on type
    const startPosIndex = startPos || 0;
    const endPosIndex = endPos || 0;

    // Construct the new text with the variable inserted at the correct position
    const updatedText =
      newText?.substring(0, startPosIndex) +
      variableText +
      newText?.substring(endPosIndex, newText?.length);

    // Set the new template state
    setTemplateState((prevState) => ({
      ...prevState,
      body: type === "body" ? updatedText : prevState.body,
      header: type === "header" ? updatedText : prevState.header,
      buttons:
        type === "url Button"
          ? prevState.buttons.map((button: any, index: number) =>
              button.buttonType === "URL" && index === buttonIndex
                ? { ...button, buttonValue: updatedText }
                : button
            )
          : prevState.buttons,
      variables: newVariables,
    }));
  };

  const handleHeaderMediaChange = (file: any, event: File | null) => {
    // const file = event.target.files && event.target.files[0];
    const allowedImageTypes = ["image/jpeg", "image/png", "image/gif"];
    const allowedVideoTypes = ["video/mp4", "video/avi", "video/mpeg"];
    const allowedDocumentTypes = [
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ];
    let isValidFile = false;
    if (event) {
      switch (templateState?.mediaType) {
        case 3:
          isValidFile = allowedImageTypes?.includes(event.type);
          break;
        case 4:
          isValidFile = allowedVideoTypes?.includes(event.type);
          break;
        case 5:
          isValidFile = allowedDocumentTypes?.includes(event.type);
          break;
        default:
          isValidFile = false;
      }

      if (isValidFile) {
        setTemplateState((prevState: any) => ({
          ...prevState,
          mediaFile: file?.payload,
        }));
      } else {
        alert(
          `Only ${
            templateState?.mediaType === 3
              ? allowedImageTypes
              : templateState?.mediaType === 4
              ? allowedVideoTypes
              : templateState?.mediaType === 5
              ? allowedDocumentTypes
              : ""
          } files are allowed.`
        );
        file = null; // Clear the input field if the file is invalid
      }
    }
    setFormErrors((prevErrors: any) => ({
      ...prevErrors,
      mediaFile: "",
    }));
  };

  const handleMediaSelectionChange = (event: any) => {
    if (Number(event.target.value) === 6) {
      setTemplateState((prevState: any) => ({
        ...prevState,
        mediaType: Number(event.target.value),
        mediaFile: "",
        header: "",
        buttons: [],
        quickReply: [],
        redirectUrl: [],
        footer: "",
        carouselCards: [
          {
            id: crypto.randomUUID(),
            mediaUrlType: 0,
            headerMediaUrl: "",
            body: "",
            carouselButtons: [],
          },
        ],
      }));
      setFormErrors({
        templateName: "",
        category: "",
        subCategory: "",
        mediaType: "",
        header: "",
        mediaFile: "",
        body: "",
        carouselCardsValid: "",
        carouselCards: [
          {
            mediaUrlType: "",
            headerMediaUrl: "",
            body: "",
            minimumButton: "",
            carouselButtons: [],
          },
        ],
      });
    } else {
      setTemplateState((prevState: any) => ({
        ...prevState,
        mediaType: Number(event.target.value),
        mediaFile: "",
        header: "",
        carouselCards: [],
      }));
      setFormErrors({
        templateName: "",
        category: "",
        subCategory: "",
        mediaType: "",
        header: "",
        mediaFile: "",
        body: "",
        carouselCardsValid: "",
        carouselCards: [],
      });
    }
  };

  const handleEmojiClick = (event: React.MouseEvent<HTMLDivElement>) => {
    setAnchorEl(event.currentTarget);
    setEmojiPopoverOpen(true);
  };

  const handleEmojiSelect = (emoji: string) => {
    const { current } = bodyRef;
    if (current) {
      const { selectionStart, selectionEnd } = current;
      const currentBody = templateState?.body;
      const newBody =
        currentBody?.slice(0, selectionStart) +
        emoji +
        currentBody?.slice(selectionEnd);

      setTemplateState((prevState) => ({
        ...prevState,
        body: newBody,
      }));

      // Set cursor position after the inserted emoji
      setTimeout(() => {
        bodyRef?.current?.focus();
        bodyRef?.current?.setSelectionRange(
          selectionStart + emoji?.length,
          selectionStart + emoji?.length
        );
      }, 0);
    }
  };

  const handleCloseEmojiPopover = () => {
    setEmojiPopoverOpen(false);
  };

  const addButton = () => {
    setTemplateState((prevState) => ({
      ...prevState,
      buttons: [
        ...prevState.buttons,
        {
          buttonType: "",
          buttonValue: "",
          countryCode: "",
          buttonName: "",
        },
      ],
    }));
  };

  const removeButton = (index: number) => {
    setTemplateState((prevState) => ({
      ...prevState,
      buttons: prevState.buttons.filter((_, i) => i !== index),
    }));
  };

  const updateButton = (index: number, updatedButton: ButtonType) => {
    setTemplateState((prevState) => ({
      ...prevState,
      buttons: prevState.buttons.map((button, i) =>
        i === index ? updatedButton : button
      ),
    }));
  };

  useEffect(() => {
    if (toEditTemplateId) {
      const params = {
        userId: userData?.userId,
        businessId: userData?.companyId,
        templateId: toEditTemplateId,
      };
      dispatch(fetchTemplateById(params));
    }
  }, [toEditTemplateId]);

  useEffect(() => {
    if (
      toEditTemplateId &&
      templateByIdSlice?.data &&
      templateByIdSlice?.data?.length !== 0
    ) {
      const templateData = templateByIdSlice?.data[0];
      const authTemplatePreview = JSON.parse(
        templateData?.authTemplatePreviewJson
          ? templateData?.authTemplatePreviewJson
          : "{}"
      );

      const carouselCardsData = refactorCarouselCards(
        templateData?.carouselCardsJson
      );

      const bodyVariables = extractVariables(templateData?.body, "body");
      const headerVariables = extractVariables(templateData?.header, "header");
      const redirectUrlVariables = extractVariables(
        templateData?.buttons,
        "url Button"
      );

      const newVariables = [
        ...headerVariables,
        ...bodyVariables,
        ...redirectUrlVariables,
      ];

      const bodyLeadratVariables = extractLeadratVariables(
        templateData?.body,
        "body"
      );
      const headerLeadratVariables = extractLeadratVariables(
        templateData?.header,
        "header"
      );

      const newLeadratVariables = [
        ...headerLeadratVariables,
        ...bodyLeadratVariables,
      ];

      let langVal = getLangValue(templateData?.languageCode);

      const newTemplateState = {
        templateName: templateData?.templateName || "",
        category: templateData?.category || 1,
        language: langVal || 15,
        subCategory: templateData?.subCategory || "",
        mediaType: templateData?.mediaType || 1,
        mediaFile: templateData?.mediaFile || null,
        header: templateData?.header || "",
        body: templateData?.body || "",
        callButtonName: templateData?.callButtonName || "",
        phoneNumber: templateData?.phoneNumber || "",
        countryCode: templateData?.countryCode || "",
        urlButtonName: templateData?.urlButtonName || [],
        redirectUrl: templateData?.redirectUrl || [],
        quickReply: templateData?.quickReply || [],
        footer: templateData?.footer || "",
        buttons: templateData?.buttons || [],
        variables: newVariables || [],
        leadratVariables: newLeadratVariables || [],
        codeDeliverySetup: templateData?.codeDeliverySetup || "COPY_CODE",
        zeroTapAuthentication: templateData?.zeroTapAuthentication || false,
        appSetup: templateData?.appSetup || [
          { appPackageName: "", appSignatureHash: "" },
        ],
        addSecurityRecommendation:
          templateData?.addSecurityRecommendation ?? true,
        addExpiryTime: templateData?.addExpiryTime || false,
        codeExpiresIn: templateData?.codeExpiresIn || 10,
        autoFillButtonText: templateData?.autoFillButtonText || "Auto fill",
        copyCodeButtonText: templateData?.copyCodeButtonText || "Copy code",
        messageValidity: templateData?.messageValidity ?? true,
        messageValidityPeriod: templateData?.messageValidityPeriod || 300,
        carouselCards: carouselCardsData?.length
          ? carouselCardsData
          : templateData?.carouselCards || [],
      };

      if (authTemplatePreview && Object.keys(authTemplatePreview).length > 0) {
        newTemplateState.templateName =
          authTemplatePreview.Name || newTemplateState.templateName;
        newTemplateState.category =
          authTemplatePreview.Category || newTemplateState.category;
        newTemplateState.language =
          authTemplatePreview.Languages?.[0] || newTemplateState.language;
        newTemplateState.subCategory =
          authTemplatePreview.SubCategory || newTemplateState.subCategory;
        newTemplateState.header =
          authTemplatePreview.Header || newTemplateState.header;
        newTemplateState.body =
          authTemplatePreview.Body || newTemplateState.body;
        newTemplateState.footer =
          authTemplatePreview.Footer || newTemplateState.footer;
        newTemplateState.codeDeliverySetup =
          OtpType[authTemplatePreview.OtpType || "COPY_CODE"];
        newTemplateState.zeroTapAuthentication =
          authTemplatePreview.ZeroTapAuthentication ??
          newTemplateState.zeroTapAuthentication;
        newTemplateState.appSetup =
          authTemplatePreview.SupportedApps || newTemplateState.appSetup;
        newTemplateState.addSecurityRecommendation =
          authTemplatePreview.AddSecurityRecommendation ??
          newTemplateState.addSecurityRecommendation;
        newTemplateState.addExpiryTime =
          !!authTemplatePreview.CodeExpirationMinutes;
        newTemplateState.codeExpiresIn =
          authTemplatePreview.CodeExpirationMinutes ||
          newTemplateState.codeExpiresIn;
        newTemplateState.autoFillButtonText =
          authTemplatePreview.Button?.autofill_text ||
          newTemplateState.autoFillButtonText;
        newTemplateState.copyCodeButtonText =
          authTemplatePreview.Button?.Text ||
          newTemplateState.copyCodeButtonText;
        newTemplateState.messageValidity =
          !!authTemplatePreview.MessageSendTtlSeconds;
        newTemplateState.messageValidityPeriod =
          authTemplatePreview.MessageSendTtlSeconds ||
          newTemplateState.messageValidityPeriod;
      }

      setTemplateState(newTemplateState);
      let carouselFormErrors = carouselCardsData?.map((carousel: any) => ({
        body: "",
        carouselButtons: [],
        headerMediaUrl: "",
        mediaUrlType: "",
        minimumButton: "",
      }));
      setFormErrors({
        templateName: "",
        category: "",
        subCategory: "",
        mediaType: "",
        header: "",
        mediaFile: "",
        body: "",
        carouselCardsValid: "",
        carouselCards: carouselFormErrors || [],
      });
    }
  }, [toEditTemplateId, templateByIdSlice]);

  useEffect(() => {
    if (
      templateNameSlice?.status === "failed" &&
      templateState?.templateName &&
      templateNameData
    ) {
      setFormErrors((prevErrors: any) => ({
        ...prevErrors,
        templateName: templateNameData !== null ? templateNameData : "", // Assume templateNameData contains the validation response
      }));
    }
  }, [templateNameData]);

  const phoneNumberButtonCount = templateState?.buttons?.filter(
    (button) => button?.buttonType === "PHONE_NUMBER"
  )?.length;
  const urlButtonCount = templateState?.buttons?.filter(
    (button) => button?.buttonType === "URL"
  )?.length;
  const replyButtonCount = templateState?.buttons?.filter(
    (button) => button?.buttonType === "QUICK_REPLY"
  )?.length;

  return (
    <Grid className={classes.mainContainer}>
      <Box className={classes.bgContainer}>
        <Box className={classes.headerContainer}>
          <CommonHeader title={toEditTemplateId ? "Edit" : "Create"} />
          <Box
            sx={{ cursor: "pointer" }}
            onClick={() => setOpenDeleteDialog(true)}
          >
            <CloseSvg />
          </Box>
        </Box>
        {templateByIdSlice?.status === "loading" ? (
          <Box sx={{ height: "70vh" }}>
            <LoadingComponent height="100%" color={bgColors?.blue} />
          </Box>
        ) : (
          <>
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 2 },
                height: "calc(100vh - 100px)",
                overflow: "auto",
              }}
            >
              <TemplateDialogContent
                canEdit={toEditTemplateId ? !!toEditTemplateId : false}
                templateData={
                  toEditTemplateId && templateByIdSlice?.data?.length > 0
                    ? templateByIdSlice?.data[0]
                    : null
                }
                templateState={templateState}
                setTemplateState={setTemplateState}
                templateNameSlice={templateNameSlice}
                formErrors={formErrors}
                setFormErrors={setFormErrors}
                handleChange={handleChange}
                handleMediaSelectionChange={handleMediaSelectionChange}
                handleHeaderMediaChange={handleHeaderMediaChange}
                handleEmojiClick={handleEmojiClick}
                handleEmojiSelect={handleEmojiSelect}
                handleCloseEmojiPopover={handleCloseEmojiPopover}
                emojiPopoverOpen={emojiPopoverOpen}
                anchorEl={anchorEl}
                bodyRef={bodyRef}
                headerRef={headerRef}
                urlButtonRef={urlButtonRef}
                maxLength={maxLength}
                phoneNumberButtonCount={phoneNumberButtonCount}
                urlButtonCount={urlButtonCount}
                replyButtonCount={replyButtonCount}
                addButton={addButton}
                removeButton={removeButton}
                updateButton={updateButton}
                buttonsArray={buttonsArray}
                handleAddVariable={handleAddVariable}
              />
            </Box>
            {/* Show loading component when any template-related operation is in progress */}
            {createTemplateSlice?.status === "loading" ||
            uploadFileSlice?.status === "loading" ||
            updateTemplateSlice?.status === "loading" ||
            createAuthTemplateSlice?.status === "loading" ||
            createCarouselTemplateSlice?.status === "loading" ? (
              <Box
                sx={{
                  height: "50px",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  borderTop: "1px solid #E4E6EB",
                }}
              >
                <LoadingComponent height="auto" color={bgColors?.blue} />
              </Box>
            ) : (
              <Box sx={{ height: "50px", borderTop: "1px solid #E4E6EB" }}>
                {(!!toEditTemplateId === false ||
                  (toEditTemplateId &&
                    templateByIdSlice?.data?.length > 0)) && (
                  <Box
                    display="flex"
                    justifyContent="flex-end"
                    mx={{ xs: 0, sm: 4 }}
                    my={1}
                    alignItems="center"
                  >
                    <ButtonComponent
                      onClick={() => handleSaveTemplate(false)}
                      title="Submit"
                      className={classes.button}
                      disabled={
                        createCarouselTemplateSlice?.status === "loading"
                      }
                    />
                    <Box width={30}></Box>
                    {((toEditTemplateId &&
                      templateByIdSlice?.data[0]?.status !== 2) ||
                      !toEditTemplateId) &&
                      templateState?.category !== 3 && (
                        <ButtonComponent
                          onClick={() => handleSaveTemplate(true)}
                          title="Save as Draft"
                          className={classes.draftButton}
                          disabled={
                            createCarouselTemplateSlice?.status === "loading"
                          }
                        />
                      )}
                  </Box>
                )}
              </Box>
            )}
          </>
        )}
      </Box>
      <ConfirmationDailog
        open={openDeleteDialog}
        handleCloseDialog={() => setOpenDeleteDialog(false)}
        handleDelete={handleDeleteTemplate}
        title="Template"
      />
    </Grid>
  );
};

export default TemplateComponent;
