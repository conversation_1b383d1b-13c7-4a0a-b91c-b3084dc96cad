export const bgColors = {
  green: "#00934F",
  green1: " #3CAA931A",
  green2: "#CCE9DA",
  blue: "#2999cf",
  blue2: "#1795D2",
  blue3: "#1795D21A",
  yellow: "#FF9D0D",
  yellow2: "#FF9D0D1A",
  gray1: "#707070",
  gray2: "#f0eff1",
  gray3: "#878794",
  gray4: "#fafafa",
  gray5: "#DBDBDB",
  white: "#ffffff",
  white1: "#F8F8F8",
  white2: "#F2F2F2",
  black: "#000000",
  black1: "#303030",
  black2: "#3C3C3C",
  black3: "#707070",
  red: "#FF7171",
  red1: "#F64E60",
  red2: "#F64E601A",
  gray9: "#F5F7F7",
};
export const getStylesByValue = (value: number) => {
  switch (value) {
    case 0: // None
      return { backgroundColor: "#e0e0e0", color: "#000" };
    case 1: // Direct
      return { backgroundColor: "#d1e7dd", color: "#0f5132" };
    case 2: // WhatsApp
      return { backgroundColor: "#d1f7d6", color: "#1e7e34" };
    case 3: // Leadrat
      return { backgroundColor: "#cfe2ff", color: "#084298" };
    case 4: // Excell
      return { backgroundColor: "#fce5cd", color: "#7f6000" };
    default: // Unknown
      return { backgroundColor: "#f8d7da", color: "#842029" };
  }
};
