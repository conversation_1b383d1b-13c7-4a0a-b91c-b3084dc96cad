import { NodeProps, Position, useReactFlow } from "reactflow";
import TriggerWebhookPageReactflow from "./triggerWebhookReactflow";
import { Box } from "@mui/system";
import { bgColors } from "../../../utils/bgColors";
import CustomHandle from "./CustomHandle";
import { useEffect, useState } from "react";
import { IconButton, Paper, Typography } from "@mui/material";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import DeleteOutlinedIcon from "@mui/icons-material/DeleteOutlined";
import { useWorkflow } from "../../../contexts/WorkflowContext";

interface TriggerWebhookNodeProps extends NodeProps {
  data: {
    isMessagePanelOpen: boolean;
    variables: any;
    webhookTriggerHttpMethod: string;
    webhookTriggerUrl: string;
    webhookTriggerHeader: any[];
    defaultErrorResponse: string;
    webhookTriggerBody: string;
    saveResponseType: string;
    showSaveUserResponse: boolean;
    handleShowSaveUserResponse: () => void;
    isValid?: boolean;
  };
}

const TriggerWebhookNode = ({ data, id }: TriggerWebhookNodeProps) => {
  const {
    isMessagePanelOpen,
    variables,
    webhookTriggerHttpMethod,
    webhookTriggerUrl,
    webhookTriggerHeader,
    defaultErrorResponse,
    webhookTriggerBody,
    saveResponseType,
    showSaveUserResponse,
    handleShowSaveUserResponse,
  } = data;

  const { handleNodeDelete } = useWorkflow();
  const { getNode, getNodes, getEdges, setNodes } = useReactFlow();
  const [localVariables, setLocalVariables] = useState(variables);

  useEffect(() => {
    if (variables) {
      setLocalVariables(variables);
    }
  }, [variables]);

  const handleEdit = () => {
    updateNodeData((data) => ({
      ...data,
      isMessagePanelOpen: !data.isMessagePanelOpen,
    }));
  };
  const handleDelete = () => {
    handleNodeDelete(id);
  };

  const updateNodeData = (updateFn: (data: any) => any) => {
    setNodes((nds) =>
      nds.map((node) => {
        if (node.id === id) {
          const updatedData = updateFn(node.data);

          // Only validate if the node has already been marked as invalid
          // This ensures validation only runs after the Save button has been clicked
          if (updatedData.isValid === false) {
            // Validate the node data to determine if all errors are resolved
            const isUrlValid = !!updatedData.webhookTriggerUrl;
            const isMethodValid = !!updatedData.webhookTriggerHttpMethod;

            // Check if body is valid JSON
            let isBodyValid = true;
            try {
              if (updatedData.webhookTriggerBody) {
                JSON.parse(updatedData.webhookTriggerBody);
              }
            } catch (error) {
              isBodyValid = false;
            }

            // Check if variables have all required fields
            let areVariablesValid = true;
            if (updatedData.variables?.webhookBodyVariables) {
              areVariablesValid =
                !updatedData.variables.webhookBodyVariables.some(
                  (prop: any) =>
                    !prop.veriable || !prop.value || !prop.fallbackValue
                );
            }

            // Only set isValid to true if all validation checks pass
            if (
              isUrlValid &&
              isMethodValid &&
              isBodyValid &&
              areVariablesValid
            ) {
              updatedData.isValid = true;
            }
          }

          // Force a new object reference to ensure React Flow detects the change
          return {
            ...node,
            data: { ...updatedData },
            // Add a timestamp to force re-render
            timestamp: Date.now(),
          };
        }
        return node;
      })
    );
  };
  const handleWebhookTriggerUpdateAll = ({
    method,
    url,
    headers,
    body,
  }: {
    method?: any;
    url?: any;
    headers?: any;
    body?: any;
  }) => {
    // Update node data in a single, atomic operation to avoid race conditions
    updateNodeData((data) => {
      // Create a new object to ensure React detects the change
      const updatedData = {
        ...data,
        ...(method !== undefined && { webhookTriggerHttpMethod: method }),
        ...(url !== undefined && { webhookTriggerUrl: url }),
        ...(headers !== undefined && { webhookTriggerHeader: headers }),
      };

      // Handle body update separately to ensure it's properly formatted
      if (body !== undefined) {
        try {
          // If body is a string, parse it to ensure it's valid JSON
          if (typeof body === "string") {
            // If it's an empty string or not valid JSON, use empty object
            if (!body.trim()) {
              updatedData.webhookTriggerBody = "{}";
            } else {
              // Try to parse and stringify to ensure valid JSON format
              const parsedBody = JSON.parse(body);
              updatedData.webhookTriggerBody = JSON.stringify(
                parsedBody,
                null,
                2
              );
            }
          } else {
            // If body is already an object, stringify it
            updatedData.webhookTriggerBody = JSON.stringify(body, null, 2);
          }
        } catch (error) {
          // If there's an error parsing the JSON, use empty object
          console.error("Error parsing body:", error);
          updatedData.webhookTriggerBody = "{}";
        }
      }

      // Add a timestamp to force React to recognize this as a new state
      updatedData.lastUpdated = Date.now();

      return updatedData;
    });
  };
  const handleVariablesChange = (updatedVariable: any) => {
    // Update local state first
    setLocalVariables((prev: any) => ({
      ...prev,
      webhookBodyVariables: updatedVariable,
    }));

    // Then update node data in a single, atomic operation
    updateNodeData((data) => {
      return {
        ...data,
        variables: {
          customMessageVariables: data.variables?.customMessageVariables || [],
          webhookBodyVariables: updatedVariable,
        },
        // Add a timestamp to force React to recognize this as a new state
        lastVariablesUpdate: Date.now(),
      };
    });
  };

  const handleVariablesValueChange = (newVariableObj: any) => {
    const updateArray = (arr: any[]) =>
      arr?.map((item) =>
        item.veriable === newVariableObj.veriable
          ? { ...item, value: newVariableObj.value || "" }
          : item
      );

    if (newVariableObj?.type === 1) {
      const updatedCustomVars = updateArray(
        localVariables.customMessageVariables
      );

      setLocalVariables({
        ...localVariables,
        customMessageVariables: updatedCustomVars,
      });

      updateNodeData((data) => {
        return {
          ...data,
          variables: {
            ...(data.variables || {}),
            customMessageVariables: updatedCustomVars,
          },
        };
      });
    } else {
      const updatedWebhookVars = updateArray(
        localVariables.webhookBodyVariables
      );

      setLocalVariables({
        ...localVariables,
        webhookBodyVariables: updatedWebhookVars,
      });

      updateNodeData((data) => {
        return {
          ...data,
          variables: {
            ...(data.variables || {}),
            webhookBodyVariables: updatedWebhookVars,
          },
        };
      });
    }
  };

  const handleVariablesFallbackValueChange = (newVariableObj: any) => {
    const updateArray = (arr: any[]) =>
      arr.map((item) =>
        item.veriable === newVariableObj.veriable
          ? { ...item, fallbackValue: newVariableObj.fallbackValue || "" }
          : item
      );

    if (newVariableObj?.type === 1) {
      const updatedCustomVars = updateArray(
        localVariables.customMessageVariables
      );

      setLocalVariables({
        ...localVariables,
        customMessageVariables: updatedCustomVars,
      });

      updateNodeData((data) => {
        return {
          ...data,
          variables: {
            ...(data.variables || {}),
            customMessageVariables: updatedCustomVars,
          },
        };
      });
    } else {
      const updatedWebhookVars = updateArray(
        localVariables.webhookBodyVariables
      );

      setLocalVariables({
        ...localVariables,
        webhookBodyVariables: updatedWebhookVars,
      });

      updateNodeData((data) => {
        return {
          ...data,
          variables: {
            ...(data.variables || {}),
            webhookBodyVariables: updatedWebhookVars,
          },
        };
      });
    }
  };

  const handleDefaultErrorResponse = (value: any) => {
    updateNodeData((data) => ({
      ...data,
      defaultErrorResponse: value,
    }));
  };
  const handleSaveResponseType = (value: any) => {
    updateNodeData((data) => ({
      ...data,
      saveResponseType: value,
    }));
  };
  const handleEditorStateChange = (value: any) => {
    updateNodeData((data) => ({
      ...data,
      webhookTriggerBody: value,
    }));
  };

  return (
    <Paper
      elevation={3}
      sx={{
        borderRadius: 2,
        position: "relative",
        backgroundColor: "white",
        border: `1px solid ${bgColors.gray2}`,
        boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.1)",
        width: 405,
        height: 500,
        display: "flex",
        flexDirection: "column",
      }}
      className="nowheel"
    >
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        sx={{
          backgroundColor: data.isValid === false ? "#FF7171" : bgColors.green,
          color: "white",
          padding: "8px 12px",
          borderTopLeftRadius: "6px",
          borderTopRightRadius: "6px",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          flexShrink: 0,
        }}
      >
        <Typography variant="h6">Trigger Webhook</Typography>
        <Box display="flex" flexDirection="row">
          <IconButton
            size="small"
            sx={{
              background: "transparent",
              border: "none",
              color: "white",
              cursor: "auto",
              padding: "2px",
              borderRadius: "3px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              "&:hover": {
                backgroundColor: "rgba(255, 255, 255, 0.2)",
              },
            }}
            onClick={handleEdit}
          >
            <EditOutlinedIcon />
          </IconButton>
          <IconButton
            size="small"
            sx={{
              background: "transparent",
              border: "none",
              color: "white",
              cursor: "auto",
              padding: "2px",
              borderRadius: "3px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              "&:hover": {
                backgroundColor: "rgba(255, 255, 255, 0.2)",
              },
            }}
            onClick={handleDelete}
          >
            <DeleteOutlinedIcon />
          </IconButton>
        </Box>
        <Box sx={{ position: "absolute", top: 100, left: -2 }}>
          <CustomHandle
            type="target"
            position={Position.Left}
            id={`left-nodeId-${id}`}
          />
        </Box>
        <Box sx={{ position: "absolute", top: 100, right: -2 }}>
          <CustomHandle
            type="source"
            position={Position.Right}
            id={`right-nodeId-${id}`}
          />
        </Box>
      </Box>
      <Box
        p={2}
        sx={{
          flex: 1,
          overflowY: "auto",
          "&::-webkit-scrollbar": {
            width: "8px",
          },
          "&::-webkit-scrollbar-track": {
            background: "#f1f1f1",
            borderRadius: "4px",
          },
          "&::-webkit-scrollbar-thumb": {
            background: "#888",
            borderRadius: "4px",
            "&:hover": {
              background: "#555",
            },
          },
        }}
      >
        {data.isValid === false && (
          <Box
            sx={{
              backgroundColor: "#FFEEEE",
              p: 1,
              mb: 2,
              borderRadius: 1,
              border: "1px solid #FF7171",
            }}
          >
            <Box component="ul" sx={{ m: 0, pl: 2 }}>
              {!webhookTriggerUrl && (
                <Typography component="li" variant="caption" color="error">
                  URL must be provided
                </Typography>
              )}
              {!webhookTriggerHttpMethod && (
                <Typography component="li" variant="caption" color="error">
                  HTTP method must be selected
                </Typography>
              )}
              {webhookTriggerBody &&
                (() => {
                  try {
                    JSON.parse(webhookTriggerBody);
                    return null;
                  } catch (error) {
                    return (
                      <Typography
                        component="li"
                        variant="caption"
                        color="error"
                      >
                        Body must be valid JSON
                      </Typography>
                    );
                  }
                })()}
              {variables?.webhookBodyVariables?.some(
                (prop: any) =>
                  !prop.veriable || !prop.value || !prop.fallbackValue
              ) && (
                <Typography component="li" variant="caption" color="error">
                  All webhook variables must have variable, value, and fallback
                  value
                </Typography>
              )}
            </Box>
          </Box>
        )}
        <Box
          p={2}
          sx={{
            background: `${bgColors.gray2}`,
            borderRadius: 2,
          }}
          width={360}
        >
          <Typography
            sx={{
              wordBreak: "break-word",
              whiteSpace: "pre-wrap",
              p: 2,
              fontWeight: "bold",
            }}
          >
            Request
          </Typography>
          <Box sx={{ display: "flex", mb: 1 }}>
            <Typography variant="subtitle2" sx={{ px: 2 }}>
              Request Type:
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: "bold", mx: 1 }}>
              {webhookTriggerHttpMethod}
            </Typography>
          </Box>
          {webhookTriggerUrl && (
            <Box sx={{ display: "flex", mb: 1 }}>
              <Typography variant="subtitle2" sx={{ px: 2 }}>
                Request Url:
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  fontWeight: "bold",
                  mx: 1,
                  width: "250px",
                  overflowWrap: "break-word !important",
                }}
              >
                {webhookTriggerUrl}
              </Typography>
            </Box>
          )}
          <Box sx={{ mb: 2, display: "flex" }}>
            <Typography variant="subtitle2" sx={{ px: 2 }}>
              Headers:
            </Typography>
            <Box sx={{ display: "block", mb: 1 }}>
              {webhookTriggerHeader?.length > 0 ? (
                webhookTriggerHeader?.map((header: any, index: number) => (
                  <Box
                    key={index}
                    sx={{
                      display: "flex",
                      mb: 1,
                      flexWrap: "wrap",
                    }}
                  >
                    <Typography
                      variant="body2"
                      sx={{ fontWeight: "bold", mx: 1 }}
                    >
                      {header?.key}:
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        fontWeight: "bold",
                        width: "250px",
                        overflowWrap: "break-word !important",
                      }}
                    >
                      {typeof header.value === "object"
                        ? JSON.stringify(header.value)
                        : header.value}
                    </Typography>
                  </Box>
                ))
              ) : (
                <Typography variant="body2">No headers</Typography>
              )}
            </Box>
          </Box>
          <Box>
            <Typography variant="subtitle2">Request Body:</Typography>
            <Typography
              variant="body2"
              sx={{
                bgcolor: "#e0e0e0",
                p: 1,
                borderRadius: 1,
                whiteSpace: "pre-wrap",
                fontFamily: "monospace",
              }}
            >
              {typeof webhookTriggerBody === "object" &&
              webhookTriggerBody !== null
                ? JSON.stringify(webhookTriggerBody, null, 2) // Pretty print the JSON
                : webhookTriggerBody || "{}"}
            </Typography>
          </Box>
        </Box>
      </Box>
      {isMessagePanelOpen && (
        <TriggerWebhookPageReactflow
          handleSidebar={handleEdit}
          webhookTriggerHttpMethod={webhookTriggerHttpMethod}
          variables={localVariables}
          handleVariablesChange={handleVariablesChange}
          handleEditorStateChange={handleEditorStateChange}
          webhookTriggerUrl={webhookTriggerUrl}
          webhookTriggerHeader={webhookTriggerHeader}
          defaultErrorResponse={defaultErrorResponse}
          webhookTriggerBody={webhookTriggerBody}
          handleWebhookTriggerUpdateAll={handleWebhookTriggerUpdateAll}
          onDefaultErrorResponseChange={handleDefaultErrorResponse}
          handleSaveResponseType={handleSaveResponseType}
          saveResponseType={saveResponseType}
          showSaveUserResponse={showSaveUserResponse}
          handleShowSaveUserResponse={handleShowSaveUserResponse}
          handleVariablesValueChange={handleVariablesValueChange}
          handleVariablesFallbackValueChange={
            handleVariablesFallbackValueChange
          }
          nodeId={id}
          handleEdit={handleEdit}
        />
      )}
    </Paper>
  );
};

export default TriggerWebhookNode;
