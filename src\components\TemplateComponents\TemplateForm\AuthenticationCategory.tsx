import {
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormGroup,
  FormLabel,
  InputLabel,
  Radio,
  RadioGroup,
  Tooltip,
  Typography,
  Link,
  IconButton,
  InputAdornment,
  Switch,
  Select,
  MenuItem,
} from "@mui/material";
import { makeStyles } from "@mui/styles";
import { bgColors } from "../../../utils/bgColors";
import InfoIcon from "@mui/icons-material/Info";
import { TiInfo } from "react-icons/ti";
import TextFieldWithBorderComponent from "../../common/TextFieldWithBorderComponent";

interface TemplateState {
  templateName: string;
  category: number;
  language: number;
  subCategory: string;
  mediaType: number;
  mediaFile: any;
  header: string;
  body: string;
  countryCode: string;
  callButtonName: any;
  phoneNumber: any;
  urlButtonName: any;
  redirectUrl: any;
  quickReply: any;
  footer: string;
  codeDeliverySetup: string;
  zeroTapAuthentication: boolean;
  addSecurityRecommendation: boolean;
  addExpiryTime: boolean;
  codeExpiresIn: number;
  autoFillButtonText: string;
  copyCodeButtonText: string;
  messageValidity: boolean;
  messageValidityPeriod: number;

  appSetup: Array<{
    appPackageName: string;
    appSignatureHash: string;
  }>;
  leadratVariables: Array<{
    type: string;
    id: string;
    value: string;
    field: string;
    fallBackValue: string;
  }>;
  buttons: {
    buttonType: string;
    buttonValue: string;
    countryCode: string;
    buttonName?: any; // Optional property
  }[];
  variables: Array<{
    type: string;
    id: string;
    value: string;
    field: string;
    fallBackValue: string;
  }>;
  carouselCards?: Array<{
    mediaUrlType: number;
    headerMediaUrl: string;
    body: string;
    callButtonName?: string;
    countryCode?: string;
    phoneNumber?: string;
    quickReply?: Array<string>;
    urlButtonName?: Array<string>;
    redirectUrl?: Array<string>;
  }>;
}

interface AuthenticationCategory {
  handleChange: (event: any) => void;
  templateState: TemplateState;
  setTemplateState: any;
}

const useStyles = makeStyles({
  blackColor: {
    color: "#303030 !important",
    fontWeight: "500 !important",
  },
  textColor: {
    color: bgColors.black1,
    fontSize: "14px",
    opacity: "80%",
  },
  variable: {
    color: `${bgColors.green} !important`,
    fontWeight: "500 !important",
    fontSize: "14px !important",
    cursor: "pointer",
  },
});

const AuthenticationCategory: React.FC<AuthenticationCategory> = ({
  handleChange,
  templateState,
  setTemplateState,
}) => {
  const classes = useStyles();

  const appSetupArray = templateState?.appSetup;

  function handleAppSetupChange(event: any, index: number) {
    const { name, value } = event.target;

    setTemplateState((prevState: TemplateState) => {
      const newAppSetup = [...prevState.appSetup];

      if (name === "appPackageName") {
        newAppSetup[index] = {
          ...newAppSetup[index],
          appPackageName: value,
        };
      } else if (name === "appSignatureHash") {
        newAppSetup[index] = {
          ...newAppSetup[index],
          appSignatureHash: value,
        };
      }

      return {
        ...prevState,
        appSetup: newAppSetup,
      };
    });
  }

  const handleAddAnotherApp = () => {
    if (templateState?.appSetup?.length === 5) {
      return;
    }
    setTemplateState((prevState: TemplateState) => {
      return {
        ...prevState,
        appSetup: [
          ...prevState.appSetup,
          { appPackageName: "", appSignatureHash: "" },
        ],
      };
    });
  };

  return (
    <Box width="100%">
      <Box
        sx={{
          border: "1px solid #ccc",

          padding: "16px",
        }}
        width="100%"
        mb={{ xs: 1, md: 3 }}
      >
        <FormControl>
          <FormLabel
            id="demo-radio-buttons-group-label"
            className={classes.blackColor}
            sx={{ fontSize: { xs: 12, md: 14 }, mb: 1 }}
          >
            Code Delivery Setup
          </FormLabel>
          <Typography className={classes.textColor} variant="body2">
            Choose how customers send the code from WhatsApp to your app. Edits
            to this section won't require review or count towards edit limits.
            Learn how to send authentication message templates.
          </Typography>
          <RadioGroup
            aria-labelledby="demo-radio-buttons-group-label"
            name="codeDeliverySetup"
            value={templateState?.codeDeliverySetup}
            onChange={handleChange}
          >
            {/* <Box sx={{ display: "flex" }}>
              <FormControlLabel
                value="ZERO_TAP"
                control={<Radio />}
                label=""
                sx={{ margin: 0 }}
              />
              <Box sx={{ display: "flex", alignItems: "center", gap: "2px" }}>
                <Typography
                  className={classes.blackColor}
                  sx={{ fontSize: "15px" }}
                >
                  Zero-tap auto fill
                </Typography>
                <Tooltip
                  title={
                    <>
                      <div>
                        Auto-fill only works when your customer requests a code
                        from the same device that has their WhatsApp account. If
                        autofill isn't possible, a copy code message will be
                        sent.
                      </div>
                    </>
                  }
                  arrow
                >
                  <IconButton size="small">
                    <InfoIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
            </Box>

            {templateState?.codeDeliverySetup === "ZERO_TAP" && (
              <Box sx={{ marginLeft: "30px", marginBottom: 2 }}>
                <Typography
                  className={classes.textColor}
                  variant="body2"
                  sx={{ marginBottom: "10px" }}
                >
                  This is recommended as the easiest option for your customers.
                  Zero-tap will automatically send code without requiring your
                  customer to tap a button. An auto-fill or copy code message
                  will be sent if zero-tap and auto-fill aren't possible.
                </Typography>
                <FormGroup
                  sx={{ padding: "20px", backgroundColor: bgColors.gray5 }}
                >
                  <Box sx={{ display: "flex" }}>
                    <FormControlLabel
                      sx={{
                        alignItems: "flex-start",
                        margin: 0,
                        marginTop: "-5px",
                      }}
                      control={
                        <Checkbox
                          name="zeroTapAuthentication"
                          value={templateState?.zeroTapAuthentication}
                          onChange={handleChange}
                        />
                      }
                      label=""
                    />
                    <Box>
                      <Typography variant="body2">
                        By selecting zero-tap, I understand that{" "}
                        <b>Easy GharOffice Solutions Private Limited</b>'s use
                        of zero-tap authentication is subject to the{" "}
                        <Link
                          href="https://www.whatsapp.com/legal/business-terms/"
                          target="_blank"
                          rel="noopener"
                        >
                          WhatsApp Business Terms of Service
                        </Link>
                        . It's <b>Easy GharOffice Solutions Private Limited</b>
                        's responsibility to ensure that its customers expect
                        that the code will be automatically filled in on their
                        behalf when they choose to receive the zero-tap code
                        through WhatsApp.
                      </Typography>
                      <Link
                        href="https://business.facebook.com/business/help"
                        target="_blank"
                        rel="noopener"
                      >
                        Learn more and review best practices.
                      </Link>
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          gap: 1,
                          backgroundColor: bgColors.yellow2,
                          marginTop: "10px",
                          borderRadius: 1,
                          padding: 1,
                        }}
                      >
                        <IconButton size="small">
                          <TiInfo color={bgColors.yellow} />
                        </IconButton>
                        <Typography variant="body2">
                          This box must be ticked to submit this template.
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                </FormGroup>
              </Box>
            )} */}
            {/* <Box sx={{ display: "flex" }}>
              <FormControlLabel
                value="ONE_TAP"
                control={<Radio />}
                label=""
                sx={{ margin: 0 }}
              />
              <Box sx={{ display: "flex", alignItems: "center", gap: "2px" }}>
                <Typography
                  className={classes.blackColor}
                  sx={{ fontSize: "15px" }}
                >
                  One-tap auto fill
                </Typography>
                <Tooltip
                  title={
                    <>
                      <div>
                        Auto-fill only works when your customer requests a code
                        from the same device that has their WhatsApp account. If
                        autofill isn't possible, a copy code message will be
                        sent.
                      </div>
                    </>
                  }
                  arrow
                >
                  <IconButton size="small">
                    <InfoIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
            </Box>
            <Typography
              className={classes.textColor}
              variant="body2"
              sx={{ marginBottom: 2, marginLeft: "30px" }}
            >
              The code sends to your app when customers tap the button. A copy
              code message will be sent if auto-fill isn't possible.
            </Typography> */}
            <Box sx={{ display: "flex" }}>
              <FormControlLabel
                value="COPY_CODE"
                control={
                  <Radio
                    sx={{
                      color: "gray",
                      "&.Mui-checked": {
                        color: "#4CAF50",
                      },
                    }}
                  />
                }
                label=""
                sx={{ margin: 0 }}
              />
              <Box sx={{ display: "flex", alignItems: "center", gap: "2px" }}>
                <Typography
                  className={classes.blackColor}
                  sx={{ fontSize: "15px" }}
                >
                  Copy code
                </Typography>
                <Tooltip
                  title={
                    <>
                      <div>
                        Auto-fill only works when your customer requests a code
                        from the same device that has their WhatsApp account. If
                        autofill isn't possible, a copy code message will be
                        sent.
                      </div>
                    </>
                  }
                  arrow
                >
                  <IconButton size="small">
                    <InfoIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
            </Box>
            <Typography
              className={classes.textColor}
              variant="body2"
              sx={{ marginBottom: "10px", marginLeft: "30px" }}
            >
              Basic authentication with quick setup. Your customers copy and
              paste the code into your app.
            </Typography>
          </RadioGroup>
        </FormControl>
      </Box>
      {templateState.codeDeliverySetup !== "COPY_CODE" && (
        <Box
          sx={{
            border: "1px solid #ccc",

            padding: "16px",
          }}
          width="100%"
          mb={{ xs: 1, md: 3 }}
        >
          <Box width="100%">
            <InputLabel
              className={classes.blackColor}
              sx={{ fontSize: { xs: 12, md: 14 }, mb: 1 }}
            >
              App setup
            </InputLabel>
            <Typography className={classes.textColor} variant="body2">
              You can add up to 5 apps.
            </Typography>
          </Box>

          {appSetupArray?.map((app: any, index: number) => {
            return (
              <Box
                sx={{ display: "flex", width: "100%", gap: 1, marginY: "2px" }}
              >
                <Box flex="75%">
                  <TextFieldWithBorderComponent
                    size="small"
                    fullWidth
                    name="appPackageName"
                    // disabled={canEdit && templateData?.status !== 4}
                    placeholder=""
                    label="Enter Package name"
                    value={app.appPackageName}
                    onChange={(event) => handleAppSetupChange(event, index)}
                    sx={{
                      "& input": {
                        fontSize: "14px", // Set input font size
                      },
                      "& .MuiOutlinedInput-root": {
                        borderRadius: "8px", // Set border radius
                      },
                    }}
                    // error={!!formErrors?.templateName}
                    // helperText={
                    //   templateNameSlice?.status === "loading" ||
                    //   templateNameSlice?.status === "idle" ||
                    //   (canEdit === true && templateData?.status !== 4) ||
                    //   (canEdit === true &&
                    //     templateData?.status === 4 &&
                    //     templateState?.templateName === templateData?.templateName)
                    //     ? ""
                    //     : formErrors?.templateName ||
                    //       (templateState?.templateName !== "" && (
                    //         <span style={{ color: bgColors?.green }}>
                    //           {templateNameSlice?.data?.message}
                    //         </span>
                    //       ))
                    // }
                    // inputProps={{
                    //   maxLength: 224,
                    // }}
                    // InputProps={{
                    //   // sx: { fontSize: 12 },
                    //   endAdornment: (
                    //     <InputAdornment position="end">{`0/224`}</InputAdornment>
                    //   ),
                    //   // endAdornment: (
                    //   //   <>
                    //   //     {templateNameSlice?.status === "loading" && (
                    //   //       <CircularProgress size={20} />
                    //   //     )}
                    //   //   </>
                    //   // ),
                    // }}
                  />
                </Box>
                <Box flex="35%">
                  <TextFieldWithBorderComponent
                    size="small"
                    fullWidth
                    name="appSignatureHash"
                    // disabled={canEdit && templateData?.status !== 4}
                    placeholder=""
                    label="App Signature Hash"
                    value={app.appSignatureHash}
                    onChange={(event) => handleAppSetupChange(event, index)}
                    sx={{
                      "& input": {
                        fontSize: "14px", // Set input font size
                      },
                      "& .MuiOutlinedInput-root": {
                        borderRadius: "8px", // Set border radius
                      },
                    }}
                    // error={!!formErrors?.templateName}
                    // helperText={
                    //   templateNameSlice?.status === "loading" ||
                    //   templateNameSlice?.status === "idle" ||
                    //   (canEdit === true && templateData?.status !== 4) ||
                    //   (canEdit === true &&
                    //     templateData?.status === 4 &&
                    //     templateState?.templateName === templateData?.templateName)
                    //     ? ""
                    //     : formErrors?.templateName ||
                    //       (templateState?.templateName !== "" && (
                    //         <span style={{ color: bgColors?.green }}>
                    //           {templateNameSlice?.data?.message}
                    //         </span>
                    //       ))
                    // }
                    // inputProps={{
                    //   maxLength: 11,
                    // }}
                    // InputProps={{
                    //   // sx: { fontSize: 12 },
                    //   endAdornment: (
                    //     <InputAdornment position="end">{`0/11`}</InputAdornment>
                    //   ),
                    //   // endAdornment: (
                    //   //   <>
                    //   //     {templateNameSlice?.status === "loading" && (
                    //   //       <CircularProgress size={20} />
                    //   //     )}
                    //   //   </>
                    //   // ),
                    // }}
                  />
                </Box>
              </Box>
            );
          })}

          <Typography
            variant="h5"
            sx={{
              // visibility: headerVariablesCount >= 1 ? "hidden" : "visible",
              color: appSetupArray?.length === 5 ? "grey" : "",
              width: 200,
              cursor: appSetupArray?.length === 5 ? "not-allowed" : "pointer",
              opacity: appSetupArray?.length === 5 ? 0.5 : 1,
            }}
            className={classes.variable}
            onClick={handleAddAnotherApp}
          >
            + Add another app
          </Typography>
        </Box>
      )}
      <Box
        sx={{
          border: "1px solid #ccc",

          padding: "16px",
        }}
        width="100%"
        mb={{ xs: 1, md: 3 }}
      >
        <Box width="100%">
          <InputLabel
            className={classes.blackColor}
            sx={{ fontSize: { xs: 12, md: 14 }, mb: 1 }}
          >
            Content
          </InputLabel>
          <Typography className={classes.textColor} variant="body2">
            Content for authentication message templates can't be edited. You
            can add additional content from the options below.
          </Typography>
        </Box>
        <Box
          sx={{
            width: "100%",
            marginY: 1,
            display: "flex",
            flexDirection: "column",
            gap: 1,
          }}
        >
          <Box
            sx={{
              width: "100%",
              display: "flex",
              alignItems: "center",
              gap: 1,
            }}
          >
            <Checkbox
              name="addSecurityRecommendation"
              sx={{
                margin: 0,
                color: "gray",
                "&.Mui-checked": { color: "#4CAF50" },
              }}
              value={templateState?.addSecurityRecommendation}
              checked={templateState?.addSecurityRecommendation}
              onChange={handleChange}
            />
            <Typography className={classes.blackColor}>
              Add security recommendation
            </Typography>
          </Box>
          <Box
            sx={{
              width: "100%",
              display: "flex",
              alignItems: "start",
              gap: 1,
            }}
          >
            <Checkbox
              name="addExpiryTime"
              sx={{
                margin: 0,
                color: "gray",
                "&.Mui-checked": { color: "#4CAF50" },
              }}
              value={templateState?.addExpiryTime}
              checked={templateState?.addExpiryTime}
              onChange={handleChange}
            />
            <Box>
              <Typography className={classes.blackColor}>
                Add expiry time for the code
              </Typography>
              <Typography variant="body2">
                After the code has expired, the auto-fill button will be
                disabled.
              </Typography>
              {templateState?.addExpiryTime && (
                <Box width={"250px"} sx={{ marginTop: 2 }}>
                  <Typography className={classes.blackColor}>
                    Expires in
                  </Typography>
                  <TextFieldWithBorderComponent
                    size="small"
                    fullWidth
                    name="codeExpiresIn"
                    // disabled={canEdit && templateData?.status !== 4}
                    placeholder=""
                    label=""
                    value={`${templateState?.codeExpiresIn}`}
                    onChange={handleChange}
                    sx={{
                      "& input": {
                        fontSize: "14px", // Set input font size
                      },
                      "& .MuiOutlinedInput-root": {
                        borderRadius: "8px", // Set border radius
                      },
                    }}
                    // error={!!formErrors?.templateName}

                    // inputProps={{
                    //   maxLength: 224,
                    // }}
                    InputProps={{
                      // sx: { fontSize: 12 },
                      endAdornment: (
                        <InputAdornment position="end">minutes</InputAdornment>
                      ),
                      // endAdornment: (
                      //   <>
                      //     {templateNameSlice?.status === "loading" && (
                      //       <CircularProgress size={20} />
                      //     )}
                      //   </>
                      // ),
                    }}
                  />
                </Box>
              )}
            </Box>
          </Box>
        </Box>
      </Box>
      <Box
        sx={{
          border: "1px solid #ccc",

          padding: "16px",
        }}
        width="100%"
        mb={{ xs: 1, md: 3 }}
      >
        <Box width="100%">
          <InputLabel
            className={classes.blackColor}
            sx={{ fontSize: { xs: 12, md: 14 }, mb: 1 }}
          >
            Buttons
          </InputLabel>
          <Typography className={classes.textColor} variant="body2">
            You can customise the button text for both auto-fill and copy code.
            Even when zero-tap is turned on, buttons are still needed for the
            backup code delivery method.
          </Typography>
        </Box>

        <Box sx={{ display: "flex", width: "100%", gap: 2, marginY: 2 }}>
          {templateState.codeDeliverySetup !== "COPY_CODE" && (
            <Box flex="50%">
              <TextFieldWithBorderComponent
                size="small"
                fullWidth
                name="autoFillButtonText"
                // disabled={canEdit && templateData?.status !== 4}
                placeholder=""
                label="Auto-fill"
                value={templateState?.autoFillButtonText}
                onChange={handleChange}
                sx={{
                  "& input": {
                    fontSize: "14px", // Set input font size
                  },
                  "& .MuiOutlinedInput-root": {
                    borderRadius: "8px", // Set border radius
                  },
                }}
                // error={!!formErrors?.templateName}
                // helperText={
                //   templateNameSlice?.status === "loading" ||
                //   templateNameSlice?.status === "idle" ||
                //   (canEdit === true && templateData?.status !== 4) ||
                //   (canEdit === true &&
                //     templateData?.status === 4 &&
                //     templateState?.templateName === templateData?.templateName)
                //     ? ""
                //     : formErrors?.templateName ||
                //       (templateState?.templateName !== "" && (
                //         <span style={{ color: bgColors?.green }}>
                //           {templateNameSlice?.data?.message}
                //         </span>
                //       ))
                // }
                inputProps={{
                  maxLength: 25,
                }}
                InputProps={{
                  // sx: { fontSize: 12 },
                  endAdornment: (
                    <InputAdornment position="end">{`${templateState?.autoFillButtonText?.length}/25`}</InputAdornment>
                  ),
                  // endAdornment: (
                  //   <>
                  //     {templateNameSlice?.status === "loading" && (
                  //       <CircularProgress size={20} />
                  //     )}
                  //   </>
                  // ),
                }}
              />
            </Box>
          )}
          {templateState.codeDeliverySetup !== "AUTO_REPLY" && (
            <Box flex="50%">
              <TextFieldWithBorderComponent
                size="small"
                fullWidth
                name="copyCodeButtonText"
                // disabled={canEdit && templateData?.status !== 4}
                placeholder=""
                label="Copy code"
                value={templateState?.copyCodeButtonText}
                onChange={handleChange}
                sx={{
                  "& input": {
                    fontSize: "14px", // Set input font size
                  },
                  "& .MuiOutlinedInput-root": {
                    borderRadius: "8px", // Set border radius
                  },
                }}
                // error={!!formErrors?.templateName}
                // helperText={
                //   templateNameSlice?.status === "loading" ||
                //   templateNameSlice?.status === "idle" ||
                //   (canEdit === true && templateData?.status !== 4) ||
                //   (canEdit === true &&
                //     templateData?.status === 4 &&
                //     templateState?.templateName === templateData?.templateName)
                //     ? ""
                //     : formErrors?.templateName ||
                //       (templateState?.templateName !== "" && (
                //         <span style={{ color: bgColors?.green }}>
                //           {templateNameSlice?.data?.message}
                //         </span>
                //       ))
                // }
                inputProps={{
                  maxLength: 25,
                }}
                InputProps={{
                  // sx: { fontSize: 12 },
                  endAdornment: (
                    <InputAdornment position="end">{`${templateState?.copyCodeButtonText?.length}/25`}</InputAdornment>
                  ),
                  // endAdornment: (
                  //   <>
                  //     {templateNameSlice?.status === "loading" && (
                  //       <CircularProgress size={20} />
                  //     )}
                  //   </>
                  // ),
                }}
              />
            </Box>
          )}
        </Box>
      </Box>
      <Box
        sx={{
          border: "1px solid #ccc",

          padding: "16px",
        }}
        width="100%"
        mb={{ xs: 1, md: 3 }}
      >
        <Box sx={{ mb: 1 }}>
          <Box sx={{ display: "flex", alignItems: "center", gap: "2px" }}>
            <Typography
              className={classes.blackColor}
              sx={{ fontSize: "15px" }}
            >
              Message validity period
            </Typography>
            <Tooltip
              title={
                <>
                  <div>
                    The message validity period is different from expiry time
                    for the code. Code expiry is content that you can add to
                    your message so that customers know when the OTP passcode
                    has expired.
                  </div>
                </>
              }
              arrow
            >
              <IconButton size="small">
                <InfoIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
          <Typography className={classes.textColor} variant="body2">
            It's recommended to set a custom validity period that your
            authentication message must be delivered by before it expires. If a
            message is not delivered within this time frame, you will not be
            charged and your customer will not see the message.
          </Typography>
        </Box>
        <Box
          sx={{
            display: "flex",
            alignItem: "center",
            justifyContent: "space-between",
            mb: 1,
          }}
        >
          <Box>
            <Typography
              className={classes.blackColor}
              sx={{ fontSize: "15px" }}
            >
              Set custom validity period for your message
            </Typography>
            <Typography className={classes.textColor}>
              If you don't set a custom validity period, the standard 30 days
              WhatsApp message validity period will be applied.
            </Typography>
          </Box>
          <Switch
            name="messageValidity"
            value={templateState?.messageValidity}
            checked={templateState?.messageValidity}
            onChange={handleChange}
            sx={{
              "& .MuiSwitch-thumb": {
                color: templateState?.messageValidity ? "#4CAF50" : "white",
              },
              "& .MuiSwitch-track": {
                backgroundColor: templateState?.messageValidity
                  ? "#90EE90 !important"
                  : "grey",
                opacity: 1,
                transition: "background-color 0.3s",
              },
            }}
          />
        </Box>
        {templateState?.messageValidity && (
          <Box>
            <Box sx={{ display: "flex", alignItems: "center", gap: "2px" }}>
              <Typography
                className={classes.blackColor}
                sx={{ fontSize: "15px" }}
              >
                Validity
              </Typography>
              <Tooltip
                title={
                  <>
                    <div>
                      The message validity period can expire if your customer's
                      WhatsApp account is unreachable. You can check you webhook
                      for message delivery status after the validity period has
                      passed. Make sure that you have a backup delivery method
                      for you autentication message in case your customer's
                      WhatsApp account is unreachable. You should only set a low
                      validity period if you have automatic backup delivery
                      methods in place.
                    </div>
                  </>
                }
                arrow
              >
                <IconButton size="small">
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
            <Select
              size="small"
              sx={{
                width: "200px",
                padding: 0,
                fontSize: 14,
                borderRadius: "8px",
              }}
              name="messageValidityPeriod"
              value={+templateState?.messageValidityPeriod}
              onChange={handleChange}
            >
              <MenuItem value={60}>1 minute</MenuItem>
              <MenuItem value={120}>2 minutes</MenuItem>
              <MenuItem value={180}>3 minutes</MenuItem>
              <MenuItem value={300}>5 minutes</MenuItem>
              <MenuItem value={600}>10 minutes</MenuItem>
            </Select>
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default AuthenticationCategory;
