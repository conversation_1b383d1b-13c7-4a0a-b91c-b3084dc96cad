import { Box, CircularProgress, Typography } from "@mui/material";
import Tooltip from "@mui/material/Tooltip";
import { QualityScore } from "../../../utils/enums";
import { useAppSelector } from "../../../utils/redux-hooks";
import { useEffect, useState } from "react";
import { INBOX_APIS } from "../../../Apis/Inbox/InboxApis";
import HashIconSvg from "../../../assets/svgs/HashIconSvg";
import PersonIconSvg from "../../../assets/svgs/PersonIconSvg";
import ChatIconSvg from "../../../assets/svgs/ChatIconSvg";
import ChatIconOutlineSvg from "../../../assets/svgs/ChatIconOutlineSvg";
import StartIconSvg from "../../../assets/svgs/startIconSvg";
import useMediaQuery from "@mui/material/useMediaQuery";
import IconButton from "@mui/material/IconButton";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";

interface MetricsbarInterface {
  businessId?: string;
  businessStatus?: string;
  conversationCount?: number;
  cost?: string;
  id?: string;
  messageLimit?: number;
  qualityRating?: number;
  tier?: string;
}

interface AccountMetricsBarProps {
  handleCloseDrawer?: () => void;
}

const AccountMetricsBar = ({ handleCloseDrawer }: AccountMetricsBarProps) => {
  const companyDetails = useAppSelector((state: any) => state.adminLogin.data);
  const { companyId } = companyDetails;

  const wallet = useAppSelector((state: any) => state?.wallet);

  const [metricsBarData, setMetricsBarData] =
    useState<MetricsbarInterface | null>(null);

  const fetchMetricsData = async () => {
    try {
      const response = await INBOX_APIS.metricsData(companyId);
      setMetricsBarData(response.data.data);
    } catch (err: any) {}
  };

  const getTierValue = (tier: any) => {
    if (!tier) return "Unknown";
    const match = tier.match(/^TIER_([0-9\.]+)([kK]?)$/);
    if (match) {
      let value = parseFloat(match[1]);
      return match[2].toLowerCase() === "k" ? value * 1000 : value;
    }
    return "Unknown";
  };

  useEffect(() => {
    fetchMetricsData();
  }, [companyId]);

  const isMediumScreen = useMediaQuery("(min-width:600px) and (max-width:900px)");
  const isSmallScreen = useMediaQuery("(min-width:300px) and (max-width:600px)");

  return (
    <Box>
      <Box
        sx={{
          background: "#348D7A",
          alignItems: "center",
          justifyContent: isSmallScreen
            ? "center"
            : isMediumScreen
            ? "center"
            : { xs: "center", sm: "space-between" },
          paddingY: isSmallScreen
            ? 0.5
            : isMediumScreen
            ? 1
            : { xs: 0.5, sm: 1 },
          display: "flex",
          gap: isSmallScreen
            ? 0.5
            : isMediumScreen
            ? 1
            : { xs: 1, sm: 2 },
          flexDirection: isSmallScreen
            ? "column"
            : isMediumScreen
            ? "column"
            : "row",
          position: "relative",
        }}
      >
        {handleCloseDrawer && (
          <IconButton
            onClick={handleCloseDrawer}
            sx={{
              position: "absolute",
              right: 8,
              top: "50%",
              transform: "translateY(-50%)",
              color: "#fff",
              zIndex: 2,
              background: "rgba(0,0,0,0.1)",
              "&:hover": { background: "rgba(0,0,0,0.2)" }
            }}
          >
            <ChevronLeftIcon />
          </IconButton>
        )}
        <Box sx={{ position: "absolute", top: 0, left: 0 }}>
          <HashIconSvg />
        </Box>
        <Box
          sx={{
            display: "flex",
            justifyContent: isSmallScreen
              ? "center"
              : isMediumScreen
              ? "center"
              : { xs: "center", sm: "space-between" },
            alignItems: "center",
            gap: isSmallScreen
              ? 0.5
              : isMediumScreen
              ? 1
              : { xs: 1, sm: 2 },
            width: isSmallScreen
              ? "100%"
              : isMediumScreen
              ? "100%"
              : { xs: "100%", sm: "auto" },
            flexDirection: isSmallScreen
              ? "row"
              : isMediumScreen
              ? "row"
              : "row",
          }}
        >
          <Box sx={{ mt: isSmallScreen ? 0.5 : isMediumScreen ? 1 : { xs: 1, sm: 2, md: 0 } }}>
            <Box
              component="img"
              src="/images/coins.gif"
              alt="Coins animation"
              sx={{
                width: isSmallScreen
                  ? "28px"
                  : isMediumScreen
                  ? "40px"
                  : { xs: "30px", sm: "50px" },
                height: isSmallScreen
                  ? "28px"
                  : isMediumScreen
                  ? "40px"
                  : { xs: "30px", sm: "50px" },
              }}
            />
          </Box>
          {!metricsBarData ? (
            <CircularProgress size={isSmallScreen ? 14 : isMediumScreen ? 18 : 20} />
          ) : (
            <Box
              sx={{
                display: "flex",
                width: isSmallScreen
                  ? "100%"
                  : isMediumScreen
                  ? "100%"
                  : { xs: "100%", sm: "100%" },
                flexDirection: "row",
                alignItems: "center",
                justifyContent: isSmallScreen
                  ? "center"
                  : isMediumScreen
                  ? "center"
                  : { xs: "space-between", sm: "center" },
                gap: isSmallScreen
                  ? 0.5
                  : isMediumScreen
                  ? 1
                  : { xs: 1, sm: 2 },
              }}
            >
              <Tooltip
                title={
                  <div style={{ textAlign: "left", maxWidth: "200px" }}>
                    <Typography variant="body2">Wallet Balance</Typography>
                  </div>
                }
                placement="bottom"
              >
                <Box
                  sx={{
                    color: "#DDDDDD",
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <Typography
                    sx={{
                      fontSize: isSmallScreen
                        ? 10
                        : isMediumScreen
                        ? 14
                        : { xs: 10, sm: 18, md: 12, lg: 14, xl: 16 },
                    }}
                    fontWeight={"bold"}
                  >
                    Rs {wallet?.walletAndSubscription?.data?.walletBalance}/-
                  </Typography>
                </Box>
              </Tooltip>
              <Tooltip
                title={
                  <div style={{ textAlign: "left", maxWidth: "200px" }}>
                    <Typography variant="body2">Expected Balance</Typography>
                  </div>
                }
                placement="bottom"
              >
                <Box
                  sx={{
                    color: "#DDDDDD",
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    p: 1,
                  }}
                >
                  <Typography
                    sx={{
                      fontSize: isSmallScreen
                        ? 10
                        : isMediumScreen
                        ? 14
                        : { xs: 10, sm: 18, md: 12, lg: 14, xl: 16 },
                    }}
                    fontWeight={"bold"}
                  >
                    Rs {wallet?.expectedAmount?.data?.expectedWalletBalance}/-
                  </Typography>
                </Box>
              </Tooltip>
            </Box>
          )}
        </Box>
        <Box sx={{ position: "absolute", top: isSmallScreen ? 6 : isMediumScreen ? 10 : 19, right: isSmallScreen ? 8 : isMediumScreen ? 20 : 40, mr: 2 }}>
          <PersonIconSvg />
        </Box>
        <Box sx={{ position: "absolute", top: 0, right: 0 }}>
          <ChatIconSvg />
        </Box>
      </Box>
      <Box
        sx={{
          display: "flex",
          justifyContent: isSmallScreen
            ? "space-around"
            : isMediumScreen
            ? "space-around"
            : "space-around",
          flexDirection: "row",
          backgroundColor: "#EDEDED",
          gap: isSmallScreen
            ? 0.2
            : isMediumScreen
            ? 0.5
            : 1,
          padding: isSmallScreen
            ? 0.2
            : isMediumScreen
            ? 0.5
            : { xs: 0.5, sm: 1 },
        }}
      >
        <Tooltip
          title={
            <Box
              sx={{
                textAlign: "left",
                padding: "12px",
              }}
            >
              <Typography
                variant="body2"
                sx={{ mb: 1, fontSize: isSmallScreen ? "10px" : isMediumScreen ? "12px" : { xs: "12px", sm: "14px" } }}
              >
                {metricsBarData?.messageLimit !== undefined &&
                metricsBarData.messageLimit !== 0
                  ? metricsBarData.messageLimit
                  : ""} {" "}
                business-initiated conversations with unique customers in a
                rolling 24-hour period
              </Typography>
            </Box>
          }
          placement="bottom"
        >
          <Box
            sx={{
              display: "flex",
              justifyContent: isSmallScreen
                ? "center"
                : isMediumScreen
                ? "center"
                : { xs: "center", sm: "space-between" },
              alignItems: "center",
              minWidth: isSmallScreen
                ? "80px"
                : isMediumScreen
                ? "120px"
                : { xs: "auto", sm: "160px", md: "110px", lg: "140px" },
              gap: isSmallScreen ? 0.2 : 1,
            }}
          >
            <Box>
              <ChatIconOutlineSvg />
            </Box>
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                flexDirection: "column",
              }}
            >
              <Typography
                sx={{ fontSize: isSmallScreen ? 9 : isMediumScreen ? 12 : { xs: 10, md: 12, lg: 14 } }}
                color="#50BFA8"
                fontWeight={"bold"}
              >
                {metricsBarData?.conversationCount}/
                {getTierValue(metricsBarData?.tier)}
              </Typography>
              <Typography sx={{ fontSize: isSmallScreen ? 7 : isMediumScreen ? 9 : { xs: 8, md: 10, lg: 14 } }}>
                Remaining Quota
              </Typography>
            </Box>
          </Box>
        </Tooltip>
        <Box
          sx={{
            height: isSmallScreen ? 16 : isMediumScreen ? 24 : { xs: 20, sm: 30 },
            mr: 1,
            display: isSmallScreen ? "block" : isMediumScreen ? "block" : { xs: "none", sm: "block" },
          }}
          borderLeft={"2px solid #D7D7D7"}
        ></Box>
        <Tooltip
          title={
            <Box
              sx={{
                textAlign: "left",
                maxWidth: "500px",
                padding: "12px",
                "@media (min-width: 600px)": {
                  maxWidth: "700px",
                },
              }}
            >
              <Typography
                variant="subtitle2"
                sx={{
                  fontWeight: 600,
                  mb: 1,
                  fontSize: isSmallScreen ? "12px" : isMediumScreen ? "14px" : { xs: "14px", sm: "16px" },
                }}
              >
                Message Quality:
              </Typography>
              <Typography
                variant="body2"
                sx={{ mb: 1, fontSize: isSmallScreen ? "10px" : isMediumScreen ? "12px" : { xs: "12px", sm: "14px" } }}
              >
                - Green: High quality
              </Typography>
              <Typography
                variant="body2"
                sx={{ mb: 1, fontSize: isSmallScreen ? "10px" : isMediumScreen ? "12px" : { xs: "12px", sm: "14px" } }}
              >
                - Yellow: Medium quality
              </Typography>
              <Typography
                variant="body2"
                sx={{ mb: 1, fontSize: isSmallScreen ? "10px" : isMediumScreen ? "12px" : { xs: "12px", sm: "14px" } }}
              >
                - Red: Low quality
              </Typography>
            </Box>
          }
          placement="bottom"
        >
          <Box
            sx={{
              display: "flex",
              justifyContent: isSmallScreen
                ? "center"
                : isMediumScreen
                ? "center"
                : { xs: "center", sm: "space-between" },
              alignItems: "center",
              minWidth: isSmallScreen
                ? "60px"
                : isMediumScreen
                ? "100px"
                : { xs: "auto", sm: "140px", md: "90px", lg: "120px" },
              ml: isSmallScreen ? 0 : isMediumScreen ? 0 : { xs: 0, sm: -1.5 },
              gap: isSmallScreen ? 0.2 : 1,
            }}
          >
            <Box>
              <StartIconSvg />
            </Box>
            <Box>
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  flexDirection: "column",
                }}
              >
                <Typography
                  sx={{
                    fontSize: isSmallScreen ? 9 : isMediumScreen ? 12 : { xs: 10, md: 12, lg: 14 },
                    color: `${
                      metricsBarData?.qualityRating === 1
                        ? "#00934F"
                        : metricsBarData?.qualityRating === 2
                        ? "red"
                        : metricsBarData?.qualityRating === 3
                        ? "#FF9D0D"
                        : ""
                    }`,
                  }}
                  fontWeight={"bold"}
                >
                  {metricsBarData?.qualityRating === QualityScore.GREEN
                    ? "GREEN"
                    : metricsBarData?.qualityRating === QualityScore.RED
                    ? "RED"
                    : metricsBarData?.qualityRating === QualityScore.YELLOW
                    ? "YELLOW"
                    : "UNKNOWN"}
                </Typography>
                <Typography sx={{ fontSize: isSmallScreen ? 7 : isMediumScreen ? 9 : { xs: 8, md: 10, lg: 14 } }}>
                  Quality Rating
                </Typography>
              </Box>
            </Box>
          </Box>
        </Tooltip>
      </Box>
    </Box>
  );
};

export default AccountMetricsBar;
