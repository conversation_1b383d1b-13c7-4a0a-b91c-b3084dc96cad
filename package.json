{"name": "ahex_-_crm", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@fontsource/roboto": "^5.0.12", "@microsoft/signalr": "^8.0.0", "@mui/icons-material": "^5.15.14", "@mui/lab": "^5.0.0-alpha.169", "@mui/material": "^6.1.8", "@mui/styled-engine-sc": "^6.0.0-alpha.18", "@mui/styles": "^5.15.14", "@mui/system": "^6.4.2", "@mui/x-date-pickers": "^7.2.0", "@reduxjs/toolkit": "^2.2.2", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/date-fns": "^2.6.0", "@types/file-saver": "^2.0.7", "@types/jest": "^29.5.0", "@types/lodash": "^4.17.4", "@types/node": "^16.11.14", "@types/react-draft-wysiwyg": "^1.13.8", "@xyflow/react": "^12.4.3", "aws-sdk": "^2.1679.0", "axios": "^1.6.8", "crypto-js": "^4.2.0", "date-fns": "^3.6.0", "dayjs": "^1.11.10", "develop": "^1.0.0", "draft-js": "^0.11.7", "draftjs-to-html": "^0.9.1", "echarts": "^5.5.1", "echarts-for-react": "^3.0.2", "emoji-picker-react": "^4.10.0", "env-cmd": "^10.1.0", "firebase": "^10.12.2", "framer-motion": "^12.0.6", "gsap": "^3.12.7", "i18next": "^23.11.2", "i18next-browser-languagedetector": "^7.2.1", "immutable": "^4.3.7", "jwt-decode": "^4.0.0", "libphonenumber-js": "^1.11.5", "lodash": "^4.17.21", "lottie-react": "^2.4.0", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "mui-phone-number": "^3.0.3", "qrcode.react": "^3.1.0", "react": "^18.2.0", "react-color": "^2.19.3", "react-colorful": "^5.6.1", "react-device-frameset": "^1.3.4", "react-device-preview": "^1.0.6", "react-dom": "^18.2.0", "react-draft-wysiwyg": "^1.15.0", "react-i18next": "^15.0.1", "react-icons": "^5.2.1", "react-infinite-scroll-component": "^6.1.0", "react-loader-spinner": "^6.1.6", "react-markdown": "^9.0.1", "react-query": "^3.39.3", "react-redux": "^9.1.0", "react-responsive": "^10.0.0", "react-router-dom": "^6.22.3", "react-scripts": "^5.0.1", "react-toastify": "^10.0.5", "reactflow": "^11.11.4", "redux-persist": "^6.0.0", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "styled-components": "^6.1.8", "uuid": "^11.0.5", "uuidv4": "^6.2.13", "web-vitals": "^2.1.4"}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@eslint/js": "^9.9.0", "@types/aws-sdk": "^2.7.0", "@types/crypto-js": "^4.2.2", "@types/draftjs-to-html": "^0.8.4", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/react-scroll": "^1.8.10", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.2.0", "@typescript-eslint/parser": "^8.2.0", "autoprefixer": "^10.4.8", "eslint": "^6.8.0", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.9.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^4.6.2", "globals": "^15.9.0", "postcss": "^8.4.16", "tailwindcss": "^3.1.8", "typescript": "^5.7.2"}, "scripts": {"test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint .", "lint:fix": "eslint . --fix", "start:dev": "env-cmd -f .env.dev react-scripts start", "start:qa": "env-cmd -f .env.qa react-scripts start", "start:prod": "env-cmd -f .env.production react-scripts start", "build:dev": "env-cmd -f .env.dev react-scripts --max_old_space_size=4096 build", "build:qa": "env-cmd -f .env.qa react-scripts --max_old_space_size=4096 build", "build:prod": "env-cmd -f .env.prod react-scripts --max_old_space_size=4096 build"}, "eslintConfig": {"root": true}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}