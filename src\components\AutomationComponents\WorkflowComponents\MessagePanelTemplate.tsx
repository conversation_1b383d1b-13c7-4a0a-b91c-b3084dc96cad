import {
  Box,
  FormControl,
  MenuItem,
  Paper,
  Typography,
  IconButton,
  Switch,
  TextField,
  SelectChangeEvent,
  Select,
  InputLabel,
} from "@mui/material";
import { bgColors } from "../../../utils/bgColors";
import CloseIconSvg from "../../../assets/svgs/CloseIconSvg";
import { useReactFlow } from "reactflow";
import TemplatePopUp from "../../InboxComponents/inboxDetailsComponents/TemplatePopUp";
import { useState } from "react";

interface MessagePanelTemplateNodeProps {
  id: string;
  handleEdit: () => void;
  templateObj: any;
  localTemplate: any;
  setLocalTemplate: (template: any) => void;
  handleSendTemplatePayload: (payload: any, templateState: any) => void;
  isEnableSetTimeOut: boolean;
  handleSetTimeoutEnabled: (value: boolean) => void;
  timeoutTime: number;
  handleTimeoutTimeChange: (time: number, unit: string) => void;
  timeoutUnit: string;
}

const TIME_UNITS = [
  { value: "minutes", label: "Minutes" },
  { value: "hours", label: "Hours" },
  { value: "days", label: "Days" },
];

const MessagePanelTemplate = ({
  id,
  handleEdit,
  localTemplate,
  handleSendTemplatePayload,
  isEnableSetTimeOut,
  timeoutTime,
  handleTimeoutTimeChange,
  timeoutUnit,
  handleSetTimeoutEnabled,
}: MessagePanelTemplateNodeProps) => {
  const { setNodes } = useReactFlow();
  const [templateSelectorOpen, setTemplateSelectorOpen] = useState(false);

  const handleTemplateClick = () => {
    setTemplateSelectorOpen((prev) => !prev);
  };

  const handleTimeInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value) || 0;
    handleTimeoutTimeChange(value, timeoutUnit || "minutes");
  };

  const handleUnitChange = (e: SelectChangeEvent<string>) => {
    const unit = e.target.value;
    handleTimeoutTimeChange(timeoutTime || 1, unit);
  };

  return (
    <Paper
      elevation={4}
      sx={{
        position: "absolute",
        right: -320,
        top: 0,
        width: 300,
        backgroundColor: "white",
        border: `1px solid ${bgColors.gray2}`,
        borderRadius: 2,
        boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
        transition: "transform 0.3s ease",
        zIndex: 10,
      }}
      className="nowheel nopan nodrag"
    >
      <Box
        sx={{
          position: "sticky",
          top: 0,
          backgroundColor: bgColors.green,
          zIndex: 1000,
          padding: "8px 16px",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          borderBottom: `1px solid ${bgColors.gray2}`,
          borderTopLeftRadius: 6,
          borderTopRightRadius: 6,
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            gap: 1,
            width: "100%",
          }}
        >
          <Typography variant="h6" color="white">
            Send Template
          </Typography>
          <IconButton
            aria-label="close"
            sx={{
              color: `${bgColors.red}`,
              width: 48,
              height: 48,
              "& svg": {
                width: 40,
                height: 40,
                fill: `${bgColors.red1}`,
              },
            }}
            onClick={() => handleEdit()}
          >
            <CloseIconSvg />
          </IconButton>
        </Box>
      </Box>

      <Typography variant="h6" sx={{ fontWeight: 600, px: 2, py: 1 }}>
        Template Selection
      </Typography>
      {localTemplate ? (
        <Box
          sx={{
            mx: 2,
            my: 1,
            p:1,
            border: `1px solid ${bgColors.green}`,
            backgroundColor: "rgba(37, 211, 102, 0.05)",
            maxWidth: "100%",
            overflow: "hidden",
          }}
        >
          <Typography
            variant="body1"
            sx={{
              fontWeight: 600,
              color: bgColors.green,
              mb: 1,
            }}
          >
            Selected Template:
          </Typography>

          <Typography variant="body1">{localTemplate.templateName}</Typography>

          <Typography
            variant="body2"
            sx={{
              mt: 2,
              color: "#128C7E",
              textDecoration: "underline",
              cursor: "pointer",
            }}
            onClick={handleTemplateClick}
          >
            Change template
          </Typography>
        </Box>
      ) : (
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            border: "1px dashed #25D366",
            borderRadius: "4px",
            cursor: "pointer",
            textAlign: "center",
            transition: "all 0.2s",
            "&:hover": {
              backgroundColor: "rgba(37, 211, 102, 0.05)",
            },
            m: 2,
          }}
          height={200}
          onClick={handleTemplateClick}
        >
          <Typography variant="body2">
            Click here to select a WhatsApp template
          </Typography>
        </Box>
      )}

      {templateSelectorOpen && (
        <TemplatePopUp
          open={templateSelectorOpen}
          handleCloseTemplatePopup={handleTemplateClick}
          setSendTemplatePayload={handleSendTemplatePayload}
        />
      )}

      <Box
        sx={{
          mx: 2,
          my: 1,
          backgroundColor: "rgba(37, 211, 102, 0.05)",
          border: `1px solid ${bgColors.green}`,
        }}
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            p: 1,
          }}
        >
          <Typography
            variant="body1"
            sx={{
              fontWeight: 600,
              color: bgColors.green,
              mb: 1,
            }}
          >
            Set timeout
          </Typography>
          <Switch
            checked={isEnableSetTimeOut}
            onChange={(e) => {
              handleSetTimeoutEnabled(e.target.checked);
              if (!e.target.checked) {
                handleTimeoutTimeChange(0, "minutes");
              } else {
                handleTimeoutTimeChange(1, "minutes");
              }
            }}
            color="success"
          />
        </Box>
        {isEnableSetTimeOut && (
          <>
            <Box
              sx={{
                display: "flex",
                gap: 2,
                alignItems: "flex-end",
                p: 1,
              }}
            >
              <TextField
                label="Time"
                type="number"
                value={timeoutTime === null ? "" : timeoutTime}
                onChange={handleTimeInputChange}
                size="small"
                inputProps={{ min: 1 }}
                sx={{
                  backgroundColor: bgColors.white,
                  minHeight: 32,
                  borderRadius: 1,
                  flex: 1,
                  "& .MuiOutlinedInput-root": {
                    borderColor: bgColors.green,
                    "&:hover .MuiOutlinedInput-notchedOutline": {
                      borderColor: bgColors.green,
                    },
                    "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                      borderColor: bgColors.green,
                    },
                  },
                }}
              />
              <FormControl size="small" sx={{ flex: 1 }}>
                <InputLabel>Unit</InputLabel>
                <Select
                  value={timeoutUnit || "minutes"}
                  onChange={handleUnitChange}
                  label="Unit"
                  sx={{
                    backgroundColor: bgColors.white,
                    minHeight: 32,
                    borderRadius: 1,
                    "& .MuiOutlinedInput-notchedOutline": {
                      borderColor: bgColors.green,
                    },
                    "&:hover .MuiOutlinedInput-notchedOutline": {
                      borderColor: bgColors.green,
                    },
                    "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                      borderColor: bgColors.green,
                    },
                  }}
                >
                  {TIME_UNITS.map((unit) => (
                    <MenuItem key={unit.value} value={unit.value}>
                      {unit.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
            {timeoutUnit && (
              <Typography
                variant="body2"
                color="textSecondary"
                sx={{ mt: 1, p: 1 }}
              >
                Timeout will be set to {timeoutTime} {timeoutUnit}
              </Typography>
            )}
          </>
        )}
      </Box>
    </Paper>
  );
};

export default MessagePanelTemplate;
