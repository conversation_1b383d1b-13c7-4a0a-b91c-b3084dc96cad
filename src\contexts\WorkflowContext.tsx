import React, { createContext, useContext, useCallback } from 'react';
import { useReactFlow } from 'reactflow';

interface WorkflowContextType {
  handleNodeDelete: (nodeId: string) => void;
  setNodes: any;
}

const WorkflowContext = createContext<WorkflowContextType | null>(null);

export const WorkflowProvider: React.FC<{ children: React.ReactNode, setNodes: any }> = ({ children, setNodes }) => {
  const { setEdges, fitView } = useReactFlow();

  const handleNodeDelete = useCallback((nodeId: string) => {
    setNodes((prevNodes: any) => {
      const updatedNodes = prevNodes.filter((nd: any) => nd.id !== nodeId);
      setEdges((prevEdges: any) =>
        prevEdges.filter(
          (edge: any) => edge.source !== nodeId && edge.target !== nodeId
        )
      );
      setTimeout(() => {
        fitView({
          nodes: updatedNodes,
          duration: 800,
          padding: 0.2,
          minZoom: 0.3,
          maxZoom: 0.7,
          includeHiddenNodes: true,
        });
      }, 100);
      return updatedNodes;
    });
  }, [setNodes, setEdges, fitView]);

  return (
    <WorkflowContext.Provider value={{ handleNodeDelete, setNodes }}>
      {children}
    </WorkflowContext.Provider>
  );
};

export const useWorkflow = () => {
  const context = useContext(WorkflowContext);
  if (!context) {
    throw new Error('useWorkflow must be used within a WorkflowProvider');
  }
  return context;
}; 