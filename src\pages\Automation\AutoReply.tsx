import {
  <PERSON>,
  Grid,
  I<PERSON><PERSON><PERSON>on,
  Typo<PERSON>,
  <PERSON>,
  CardContent,
  Chip,
} from "@mui/material";
import { Edit as EditIcon, Delete as DeleteIcon } from "@mui/icons-material";
import { makeStyles } from "@mui/styles";
import { useEffect, useState } from "react";
import { bgColors } from "../../utils/bgColors";
import AddAutoReplyPopup from "./AddAutoReply";
import { useAppDispatch, useAppSelector } from "../../utils/redux-hooks";
import { getAutoReply } from "../../redux/slices/Automation/getAutoReplySlice";
import { useSelector } from "react-redux";
import { toastActions } from "../../utils/toastSlice";
import { deleteAutoReply } from "../../redux/slices/Automation/deleteAutoReplySlice";
import DeletePopUp from "../../components/common/DeletePopup";
import { parseTextToHtml } from "../../utils/functions";
import { checkAutoReplyPermission } from "../../utils/permissions";
import NoAccessPage from "../../components/common/NoAccess";
import CommonTable from "../../components/common/CommonTable";

const useStyles = makeStyles({
  mainContainer: {
    backgroundColor: bgColors.white,
    width: "100%",
  },
  chatArea: {
    padding: "20px",
  },
  bgContainer: {
    backgroundColor: bgColors.white,
    height: "100%",
    width: "100%",
  },
  manageContainer: {
    display: "flex",

    justifyContent: "space-between",
  },
  blackColor: {
    color: `${bgColors.black1} !important`,
  },
  SaveChangesButton: {
    color: bgColors.green,
    border: `1px solid ${bgColors.green}`,
    borderRadius: "8px",
    width: "140px",
    height: "32px",
    cursor: "pointer",
  },
  textColor: {
    color: bgColors.gray1,
    fontSize: "16px",
  },
  table: {
    borderCollapse: "separate",
    borderSpacing: "0",
    textAlign: "center",
    minWidth: "500px",
    overflowX: "auto",
    borderColor: "lightgray",
    "& th, & td": {
      borderBottom: "1px solid #f0f0f0",
      padding: "4px",
    },
    "& th:first-child, & td:first-child": {
      borderLeft: "none",
    },
    "& th:last-child, & td:last-child": {
      borderRight: "none",
    },
  },
  grayColor: {
    color: "#303030",
    opacity: "60%",
    fontSize: "13px",
  },
  editButtonContainer: {
    border: "2px solid #DBDBDB",
    padding: "8px",
    borderRadius: "12px",
    backgroundColor: "#F4F4F4",
    width: "50px",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    cursor: "pointer",
  },
  row: {
    transition: "box-shadow 0.3s",
  },
  rowHovered: {
    boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.2)",
  },
});

const AutoReply = () => {
  const classes = useStyles();
  const userProfileSlice = useAppSelector((state: any) => state?.adminLogin);
  const dispatch = useAppDispatch();
  const { status, data } = useSelector((state: any) => state.getAutoReply);

  const userData = userProfileSlice?.data;

  const getuserPermissionSlice = useAppSelector(
    (state: any) => state.getUserPermissions
  );
  const getuserPermissionData = getuserPermissionSlice?.data;
  const automationPermissionsArray = getuserPermissionData?.automation;
  const autoReplyObject = automationPermissionsArray?.find((item: any) =>
    Object.prototype.hasOwnProperty.call(item, "autoReply")
  );
  const autoReplyActions = autoReplyObject ? autoReplyObject.autoReply : [];

  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<any>(null);
  const [isDeletePopupOpen, setIsDeletePopupOpen] = useState(false);
  const [deletingItemId, setDeletingItemId] = useState(null);
  const [isDeleteLoading, setIsDeleteLoading] = useState(false);
  const [addAutoReplyTooltip, setAddAutoReplyTooltip] = useState(false);
  const [editAutoReplyTooltip, setEditAutoReplyTooltip] = useState("");
  const [deleteAutoReplyTooltip, setDeleteAutoReplyTooltip] = useState("");

  const hasAutoReplyPermission = checkAutoReplyPermission(
    automationPermissionsArray
  );
  const hasAccess = (permission: any) => {
    if (autoReplyActions?.includes(permission)) {
      return true;
    }
    return false;
  };

  const handleClosePopup = () => {
    setIsPopupOpen(false);
  };

  const handleSaveAutoReply = () => {
    // Implement logic to save the new auto reply
  };

  const handleOpenPopup = (item: any) => {
    if (item !== null) {
      const hasPermission = hasAccess("editAutoReply");
      if (hasPermission) {
        setEditingItem(item?.originalData || item);
        setIsPopupOpen(true);
      } else {
        setEditAutoReplyTooltip(item);
        setTimeout(() => {
          setEditAutoReplyTooltip("");
        }, 2000);
      }
    } else {
      const hasPermission = hasAccess("addAutoReply");
      if (hasPermission) {
        setEditingItem(null);
        setIsPopupOpen(true);
      } else {
        setAddAutoReplyTooltip(true);
        setTimeout(() => {
          setAddAutoReplyTooltip(false);
        }, 2000);
      }
    }
  };
  const handleConfirmDelete = async () => {
    if (!deletingItemId) return;

    setIsDeleteLoading(true);
    try {
      const data = {
        companyId: userData?.companyId,
        autoReplyAutomationId: deletingItemId,
      };
      const response: any = await dispatch(deleteAutoReply(data));
      setIsDeleteLoading(false);
      const payload = {
        companyId: userData?.companyId,
      };
      dispatch(getAutoReply(payload));
      if (response.payload.success) {
        dispatch(
          toastActions.setToaster({
            type: "success",
            message:
              response?.payload?.message ||
              "Auto reply custom message deleted successfully",
          })
        );
      } else {
        dispatch(
          toastActions.setToaster({
            type: "error",
            message:
              response?.payload?.message ||
              "Failed to delete auto reply custom message",
          })
        );
      }
    } catch (error: any) {
      dispatch(
        toastActions.setToaster({
          type: "error",
          message:
            error?.payload?.message ||
            "Failed to delete auto reply custom message",
        })
      );
    } finally {
      setIsDeletePopupOpen(false);
      setDeletingItemId(null);
      setIsDeleteLoading(false);
    }
  };
  const handleDelete = (autoReplyAutomationId: any) => {
    const hasPermission = hasAccess("deleteAutoReply");
    if (hasPermission) {
      setDeletingItemId(autoReplyAutomationId);
      setIsDeletePopupOpen(true);
    } else {
      setDeleteAutoReplyTooltip(autoReplyAutomationId);
      setTimeout(() => {
        setDeleteAutoReplyTooltip("");
      }, 2000);
    }
  };

  useEffect(() => {
    const payload = {
      companyId: userData?.companyId,
    };
    dispatch(getAutoReply(payload));
  }, [dispatch, userData?.companyId]);

  // Transform data for table display
  const transformedData = data?.map((row: any) => ({
    id: row?.autoReplyAutomation?.id,
    input: row?.autoReplyAutomation?.input,
    bodyMessage: row?.bodyMessage,
    formattedBodyMessage: parseTextToHtml(row?.bodyMessage),
    automationResponseType:
      row?.autoReplyAutomation?.automationResponseType === 1
        ? "Custom Message"
        : "Workflow",
    originalData: row,
  }));

  const columns: any[] = [
    {
      id: "input",
      label: "Input",
      align: "left",
    },
    {
      id: "bodyMessage",
      label: "Auto Response Preview",
      align: "left",
      format: (value: string) => (
        <div
          style={{
            maxWidth: "300px",
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
          }}
          dangerouslySetInnerHTML={{ __html: parseTextToHtml(value) }}
        />
      ),
    },
    {
      id: "automationResponseType",
      label: "Response Type",
      align: "left",
    },
  ];

  const renderActions = (row: any) => (
    <Box sx={{ display: "flex", gap: 1, justifyContent: "flex-end" }}>
      <IconButton
        onClick={() => handleOpenPopup(row)}
        size="small"
        sx={{ color: "#666", "&:hover": { color: "#3b82f6" } }}
      >
        <EditIcon fontSize="small" />
      </IconButton>
      <IconButton
        onClick={() => handleDelete(row?.id)}
        size="small"
        sx={{ color: "#666", "&:hover": { color: "#ef4444" } }}
      >
        <DeleteIcon fontSize="small" />
      </IconButton>
    </Box>
  );

  const renderOnMobile = (row: any) => (
    <Card sx={{ padding: 2, mb: 2 }}>
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          marginBottom: 8,
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
          <Typography variant="subtitle1" fontWeight="500">
            {row.input}
          </Typography>
          <Box sx={{ display: "flex", gap: 0.25, ml: 0.5 }}>
            <IconButton
              onClick={() => handleOpenPopup(row)}
              size="small"
              sx={{
                color: "#666",
                "&:hover": { color: "#3b82f6" },
                padding: "2px",
                "& .MuiSvgIcon-root": {
                  fontSize: "1.1rem",
                },
              }}
            >
              <EditIcon />
            </IconButton>
            <IconButton
              onClick={() => handleDelete(row?.id)}
              size="small"
              sx={{
                color: "#666",
                "&:hover": { color: "#ef4444" },
                padding: "2px",
                "& .MuiSvgIcon-root": {
                  fontSize: "1.1rem",
                },
              }}
            >
              <DeleteIcon />
            </IconButton>
          </Box>
        </Box>
        <Box>
          <Chip
            label={row.automationResponseType}
            sx={{
              backgroundColor:
                row.automationResponseType === "Custom Message"
                  ? "#4CAF50"
                  : "#2196F3",
              color: "#fff",
              "&:hover": {
                backgroundColor:
                  row.automationResponseType === "Custom Message"
                    ? "#4CAF50"
                    : "#2196F3",
              },
            }}
            size="small"
          />
        </Box>
      </div>

      <CardContent sx={{ padding: 0, "&:last-child": { paddingBottom: 0 } }}>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Typography variant="caption" color="text.secondary">
              Auto Response Preview
            </Typography>
            <div
              style={{
                maxWidth: "100%",
                overflow: "hidden",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap",
                marginTop: 4,
              }}
              dangerouslySetInnerHTML={{
                __html: parseTextToHtml(row.bodyMessage),
              }}
            />
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
  return (
    <>
      {hasAutoReplyPermission ? (
        <Grid container className={classes.mainContainer}>
          <Grid item className={classes.bgContainer}>
            <CommonTable
              columns={columns}
              actions={renderActions}
              renderOnMobile={renderOnMobile}
              data={transformedData || []}
              rowIdKey="id"
              isLoading={status === "loading"}
              title="Custom Auto Replies"
              count={data?.length}
              primaryAction={{
                label: "Add Auto Reply",
                onClick: () => handleOpenPopup(null),
                disabled: !hasAccess("addAutoReply"),
                tooltip: !hasAccess("addAutoReply")
                  ? "You don't have permission to add auto reply"
                  : "",
              }}
              showPagination={false}
              optionalTypographyInRenderToolbar="This section allows users to create and manage Custom Auto
                Replies in response to customer chats."
            />
            <AddAutoReplyPopup
              open={isPopupOpen}
              onClose={handleClosePopup}
              onSave={handleSaveAutoReply}
              companyId={userData?.companyId}
              userId={userData?.userId}
              editData={editingItem}
              setEditingItem={setEditingItem}
            />
            <DeletePopUp
              open={isDeletePopupOpen}
              handleDelete={handleConfirmDelete}
              handleClose={() => setIsDeletePopupOpen(false)}
              title="Auto Reply"
              handleLoad={isDeleteLoading}
            />
          </Grid>
        </Grid>
      ) : (
        <NoAccessPage />
      )}
    </>
  );
};

export default AutoReply;
