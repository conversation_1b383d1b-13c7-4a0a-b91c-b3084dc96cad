import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { INBOX_APIS } from "../../../Apis/Inbox/InboxApis";

export interface IData {
  status: "loading" | "succeeded" | "failed" | "idle";
  data: any;
}

const initialState: IData = {
  status: "idle",
  data: null,
};

export const fetchInboxContacts = createAsyncThunk(
  "fetchInboxContacts",
  async (data: any) => {
    const response = await INBOX_APIS.fetchInboxContacts(data);
    return response?.data;
  }
);

export const InboxContactsSlice = createSlice({
  name: "InboxContactsSlice",
  initialState,
  reducers: {
    setData: (state, action) => {
      state.data = action.payload;
    },
  },
  extraReducers(builder) {
    builder
      .addCase(fetchInboxContacts.pending, (state) => {
        state.status = "loading";
        //   state.error = ""
        state.data = [];
      })
      .addCase(fetchInboxContacts.fulfilled, (state, action) => {
        state.status = "succeeded";
        //   state.error = ""
        state.data = action.payload;
      })
      .addCase(fetchInboxContacts.rejected, (state) => {
        state.status = "failed";
        state.data = [];
        //   state.error = action.error.message || "";
      });
  },
});

export const inboxContactsActions = InboxContactsSlice.actions;
export default InboxContactsSlice.reducer;
