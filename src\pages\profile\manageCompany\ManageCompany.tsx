import React from "react";
import {
  Avatar,
  Box,
  Button,
  Grid,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  ThemeProvider,
  Tooltip,
  Typography,
  createTheme,
  keyframes,
} from "@mui/material";
import { makeStyles } from "@mui/styles";
import { useEffect, useState } from "react";
import EditSvg from "../../../assets/svgs/EditSvg";
import EditIconSvg from "../../../assets/svgs/EditIconSvg";
import { bgColors } from "../../../utils/bgColors";
import { useNavigate, useParams } from "react-router-dom";
import AddNewTeamMember from "../../../components/ManageCompanyComponents/AddNewTeamMember";
import EditTeamMember from "../../../components/ManageCompanyComponents/EditTeamMember";
import { useAppDispatch, useAppSelector } from "../../../utils/redux-hooks";
import LoadingComponent from "../../../components/common/LoadingComponent";
import { fetchCompanyDetails } from "../../../redux/slices/ManageCompany/CompanyDetailsSlice";
import { ADMIN_MANAGE_COMPANY_APIS } from "../../../Apis/ManageCompany/ManageCompany";
import { toastActions } from "../../../utils/toastSlice";
import { fetchGetAllTeamMembersByCompanyId } from "../../../redux/slices/ManageCompany/GetAllTeamMembersByCompanyId";
import SearchIconSvg2 from "../../../assets/svgs/SearchIconSvg2";
import PreviewPopover from "../../../components/ManageCompanyComponents/PreviewPopOver";
import DeletePopUp from "../../../components/common/DeletePopup";
import useDebouncedFetch from "../../../utils/debounceHook";
import RoomPreferencesIcon from "@mui/icons-material/RoomPreferences";
import GroupsIcon from "@mui/icons-material/Groups";
import { TablePagination } from "@mui/material";
import DeleteSvg from "../../../assets/svgs/DeleteSvg";
import { hasManageCompanyPermission } from "../../../utils/permissions";
import NoAccessPage from "../../../components/common/NoAccess";
import CommonHeader from "../../../components/common/CommonHeader";
import CommonButton from "../../../components/common/CommonButton";
import { formatDate } from "date-fns";
import CommonTable from "../../../components/common/CommonTable";

const useStyles = makeStyles({
  mainContainer: {
    backgroundColor: bgColors.white1,
    height: "100vh",
    width: "100%",
  },

  bgContainer: {
    backgroundColor: bgColors.white,
  },

  headerContainer: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    borderBottom: "1px solid #f0f0f0",
    width: "100%",
    paddingLeft: "24px",
    paddingRight: "24px",
    paddingTop: "8px",
    paddingBottom: "8px",
  },
  mainBorderStyles: {
    display: "flex",
    flexDirection: "column",
    flexWrap: "wrap",
    borderRadius: "18px",
    border: `2px solid ${bgColors.gray5}`,
  },
  blackColor: {
    // color: `${bgColors.black1} !important`,
    // fontWeight: "600 !important",
  },
  grayColor: {
    color: `${bgColors.black1} !important`,
    opacity: "60% !important",
    fontWeight: "600 !important",
  },
  planButton: {
    backgroundColor: bgColors.green1,
    color: bgColors.green,
    // fontWeight: "700",
    paddingTop: 6,
    paddingRight: 15,
    paddingBottom: 6,
    paddingLeft: 15,
    borderRadius: 25,
  },
  expiredButton: {
    backgroundColor: "#F64E601A",
    color: "#F64E60",
    // fontWeight: "700",
    paddingTop: 6,
    paddingRight: 18,
    paddingBottom: 6,
    paddingLeft: 18,
    borderRadius: 25,
  },
  editButtonContainer: {
    // backgroundColor: bgColors.blue,
    borderRadius: "8px",
    border: `1px solid ${bgColors.green}`,
    height: "32px",
    display: "flex",
    flexDirection: "row",
    // alignItems: "center",
    justifyContent: "space-evenly",
    width: "130px",
    cursor: "pointer",
    // padding: "10px",
  },
  editButton: {
    backgroundColor: "transparent",
    color: bgColors.green,
    cursor: "pointer",
    borderRadius: "34px",
    marginLeft: 10,
  },
  emailContainer: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  emailContainer1: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    borderBottom: `1px solid ${bgColors.gray5}`,
  },
  searchField: {
    // width: "60%",
    borderRadius: "12px",
    // backgroundColor: bgColors.white2,
    backgroundColor: "white",
    // border: "2px solid black",
    "& input::placeholder": {
      textAlign: "left",
      fontSize: "14px",
      fontFamily: "Inter",
      color: bgColors.black,
      // fontWeight: "500 !important",
    },
  },
  SaveChangesButton: {
    // backgroundColor: bgColors.green,
    border: `1px solid ${bgColors.green}`,
    width: "120px",
    height: "32px",
    color: bgColors.green,
    borderRadius: "8px",
    cursor: "pointer",
  },
  table: {
    borderCollapse: "separate",
    borderSpacing: "0 8px",
    textAlign: "center",
    borderColor: "lightgray",
    "& th, & td": {
      // borderTop: '1px solid gray',
      borderBottom: "1px solid #f0f0f0",
      padding: "3px",
    },
    "& th:first-child, & td:first-child": {
      paddingLeft: "20px",
    },
    "& th:last-child, & td:last-child": {
      paddingRight: "20px",
    },
  },
  profileTextContainer: {
    display: "flex",

    flexDirection: "row",
    width: "100%",
  },
  warnText: {
    marginLeft: "auto",
    display: "flex",
    flexDirection: "row",
    textAlign: "center",
  },
  teamProfileContainer: {
    display: "flex",
    alignItems: "center",
  },
  manageTeamContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    gap: "20px",
    width: "full",
  },
  flexCenter: {
    display: "flex",
    alignItems: "center",
    marginRight: 10,
  },
});

const ManageCompany = () => {
  const navigate = useNavigate();
  const classes = useStyles();
  const dispatch = useAppDispatch();
  const params = useParams();
  const [isDeleteLoading, setIsDeleteLoading] = useState(false);
  const companyData = useAppSelector((state: any) => state.companyData);
  const companyDetails = companyData?.data && companyData?.data;
  const planDetails = companyDetails?.plan;
  const loginData = useAppSelector((state: any) => state?.adminLogin?.data);
  const getuserPermissionData = useAppSelector(
    (state: any) => state.getUserPermissions?.data
  );
  const hasCompanyPermission = hasManageCompanyPermission(
    getuserPermissionData?.profile
  );
  const manageCompanyObject = getuserPermissionData?.profile?.find(
    (item: any) =>
      Object?.prototype?.hasOwnProperty?.call(item, "manageCompany")
  );
  const manageCompanyActions = manageCompanyObject
    ? manageCompanyObject?.manageCompany
    : [];
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const handleChangePage = (event: any, newPage: any) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: any) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const hasAcess = (permission: any) => {
    if (manageCompanyActions?.includes(permission)) {
      return true;
    }
    return false;
  };

  // const manageAccountActions = manageAccountObject ? manageAccountObject.manageAccount : [];
  const getAllTeamMembers = useAppSelector(
    (state: any) => state?.getAllTeamMembersByCompanyId
  );
  const teamMembersStatus = getAllTeamMembers?.status;
  const getAllTeamMembersData = getAllTeamMembers?.data?.filter(
    (item: any) => item?.status === true
  );
  const debouncedFetchTeamMembers = useDebouncedFetch(
    fetchGetAllTeamMembersByCompanyId,
    1500
  );
  const [isHovered, setIsHovered] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [openPreviewDialog, setOpenPreviewDialog] = useState(false);
  const [animate, setAnimate] = useState(true);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [editTeamMember, setEditTeamMember] = useState("");
  const [searchTeamMember, setSearchTeamMember] = useState("");
  const [deleteId, setDeleteId] = useState("");
  const [editTooltip, setEditTooltip] = useState(false);
  const [addMemberTooltip, setAddMemberTooltip] = useState(false);
  const [editTeamMemberTooltip, setEditTeamMemberTooltip] = useState(null);
  const [deleteTeamMemberTooltip, setDeleteTeamMemberTooltip] = useState(null);

  const handleClickEdit = () => {
    const hasPermission = hasAcess("editCompanyDetails");
    if (hasPermission) {
      navigate(`/profile/manage-company/${loginData?.companyId}/edit`);
    } else {
      setEditTooltip(true);
      setTimeout(() => {
        setEditTooltip(false);
      }, 2000);
    }
  };

  const handleOpenDeleteDialog = (row?: any) => {
    const hasPermission = hasAcess("deleteTeamMember");
    if (hasPermission) {
      if (row !== undefined && row !== null) {
        setDeleteId(row?.id);
      }
      setOpenDeleteDialog(true);
    } else {
      setDeleteTeamMemberTooltip(row);
      setTimeout(() => {
        setDeleteTeamMemberTooltip(null);
      }, 2000);
    }
  };

  const handleCloseDelete = () => {
    setOpenDeleteDialog(false);
  };

  const handleOpenDialog = () => {
    const hasPermission = hasAcess("addTeamMember");
    if (hasPermission) {
      setOpenDialog(true);
    } else {
      setAddMemberTooltip(true);
      setTimeout(() => {
        setAddMemberTooltip(false);
      }, 2000);
    }
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handlePreviewOpenDialog = () => {
    setOpenPreviewDialog(true);
    setAnimate(false);
  };
  const handlePreviewCloseDialog = () => {
    setOpenPreviewDialog(false);
  };

  const handleOpenEditDialog = (row?: any) => {
    const hasPermission = hasAcess("editTeamMember");
    if (hasPermission) {
      if (row !== undefined && row !== null) {
        setEditTeamMember(row);
      }
      setOpenEditDialog(true);
    } else {
      setEditTeamMemberTooltip(row);
      setTimeout(() => {
        setEditTeamMemberTooltip(null);
      }, 2000);
    }
  };

  const handleCloseEditDialog = () => {
    setOpenEditDialog(false);
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
  };

  const handleDeleteTeamMember = async () => {
    if (deleteId) {
      setIsDeleteLoading(true);
      try {
        const response = await ADMIN_MANAGE_COMPANY_APIS.deleteTeamMember(
          deleteId
        );
        if (response.status === 200) {
          dispatch(
            toastActions.setToaster({
              type: "success",
              message: response?.data?.message,
            })
          );
          const postData = {
            search: searchTeamMember,
            companyId: loginData?.companyId,
          };
          dispatch(fetchGetAllTeamMembersByCompanyId(postData));
          handleCloseDeleteDialog();
          // window.location.reload();
        }
      } catch (err: any) {
        dispatch(
          toastActions.setToaster({
            type: "error",
            message: err?.response?.data?.message,
          })
        );
        // window.location.reload();
      }
      setIsDeleteLoading(false);
    }
  };

  useEffect(() => {
    dispatch(fetchCompanyDetails(params?.id));
  }, [dispatch]);

  useEffect(() => {
    const postData = {
      search: searchTeamMember,
      companyId: loginData?.companyId,
    };

    debouncedFetchTeamMembers(postData);
  }, [dispatch, searchTeamMember]);

  const pulse = keyframes`
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(68, 71, 70, 0.4);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 15px 5px rgba(68, 71, 70, 0.4);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(68, 71, 70, 0.4);
  }
`;

  const [hoveredRow, setHoveredRow] = useState(null);

  const handleRowHover = (rowId: any) => {
    setHoveredRow(rowId);
  };

  const splitCamelCaseAndJoin = (str: string): string => {
    // Replace special characters with a space
    const cleanedStr = str.replace(/[^a-zA-Z0-9]+/g, " ");
    // Split the string at capital letters and join with a space
    const splitStr = cleanedStr.replace(/([a-z])([A-Z])/g, "$1 $2");
    // Capitalize the first letter of each word
    const result = splitStr.replace(/\b\w/g, (char: string) =>
      char.toUpperCase()
    );
    return result;
  };

  const manageTeamColumns = [
    {
      id: "member",
      label: "Member",
      format: (row: any) => {
        return (
          <Box className={classes.teamProfileContainer}>
            <Avatar
              alt={row?.name}
              src={row?.image}
              style={{
                width: "30px",
                height: "30px",
              }}
            />
            <Tooltip title={row?.name} arrow>
              <Typography
                className={classes.blackColor}
                sx={{
                  ml: 1,
                  fontSize: "inherit",
                  whiteSpace: "nowrap",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  maxWidth: { xs: 80, sm: 150 },
                }}
              >
                {row?.name}
              </Typography>
            </Tooltip>
          </Box>
        );
      },
    },
    {
      id: "createdBy",
      label: "Created By",
    },
    {
      id: "email",
      label: "Email",
    },
    {
      id: "contact",
      label: "Contact",
    },
    {
      id: "createdOn",
      label: "Created On",
    },
    {
      id: "role",
      label: "Role",
    },
  ];

  const renderActions = (row: any) => {
    const isFirstCreated = row.roleId === "Owner";
    return (
      <Box sx={{ display: "flex", gap: 1 }}>
        <Tooltip
          title="Access Denied"
          open={editTeamMemberTooltip === row}
          placement="top"
          onClose={() => setEditTeamMemberTooltip(null)}
        >
          <Box
            mr={1}
            onClick={() => !isFirstCreated && handleOpenEditDialog(row)}
            sx={{
              cursor: isFirstCreated ? "not-allowed" : "pointer",
              opacity: isFirstCreated ? 0.3 : 1,
            }}
          >
            <EditIconSvg />
          </Box>
        </Tooltip>
        <Tooltip
          title="Access Denied"
          open={deleteTeamMemberTooltip === row}
          placement="top"
          onClose={() => setDeleteTeamMemberTooltip(null)}
        >
          <Box
            onClick={() => !isFirstCreated && handleOpenDeleteDialog(row)}
            sx={{
              cursor: isFirstCreated ? "not-allowed" : "pointer",
              opacity: isFirstCreated ? 0.3 : 1,
            }}
          >
            <DeleteSvg />
          </Box>
        </Tooltip>
      </Box>
    );
  };

  const transformedData = getAllTeamMembersData?.map((item: any) => {
    return {
      ...item,
      member: { image: item.image, name: item.name },
      createdBy: item.createdBy,
      email: item.emailAddress,
      contact: item.countryCode + " " + item.phoneNumber,
      createdOn: item.creationDate
        ? new Date(item?.creationDate).toLocaleDateString("en-US", {
            month: "short",
            day: "2-digit",
            year: "numeric",
          })
        : "N/A",
      role: item.roleId,
    };
  });

  return (
    <>
      {hasCompanyPermission ? (
        <Grid
          className={classes.mainContainer}
          sx={{ height: getAllTeamMembersData?.length > 1 ? "100%" : "auto" }}
        >
          {companyData.status === "loading" ? (
            <Box sx={{ height: "100vh" }}>
              {" "}
              <LoadingComponent height="100%" color={bgColors.blue} />
            </Box>
          ) : (
            <Grid className={classes.bgContainer}>
              <Box className={classes.headerContainer}>
                <CommonHeader title="Manage Company" />
              </Box>
              <>
                <Box m={{ xs: 1, md: 3 }}>
                  <Box
                    p={{ xs: 2, md: 3 }}
                    className={classes.mainBorderStyles}
                  >
                    <Box
                      sx={{
                        display: "flex",
                        // flexWrap: { xs: "wrap" },
                        alignItems: "center",
                        flexDirection: { xs: "column", sm: "row" },
                        justifyContent: "space-between",
                      }}
                      gap={2}
                    >
                      <Box>
                        <img
                          alt="profile"
                          src={
                            companyDetails?.company?.companyLogoLink ||
                            "/images/companyLogo.png"
                          }
                          style={{
                            width: "70px",
                            height: "70px",
                            borderRadius: "14px",
                          }}
                        />
                      </Box>

                      <Box className={classes.profileTextContainer}>
                        <Box ml={{ xs: 0, md: 1 }} mt={1} flexWrap="wrap">
                          <Typography
                            variant="body2"
                            className={classes.blackColor}
                          >
                            {companyDetails?.company?.businessName || ""}
                          </Typography>
                          <Typography
                            variant="body2"
                            mt={1}
                            className={classes.grayColor}
                          >
                            {companyDetails?.company?.businessCategory
                              ? splitCamelCaseAndJoin(
                                  companyDetails?.company?.businessCategory
                                )
                              : ""}
                          </Typography>
                        </Box>

                        <Box
                          sx={{
                            ml: { xs: 0, mb: 1 },
                            mr: { xs: 2, mb: 0 },
                            width: "180px",
                          }}
                          mt={1}
                        >
                          <button
                            className={
                              planDetails?.subscriptionPlan?.isActive
                                ? classes.planButton
                                : classes.expiredButton
                            }
                          >
                            {planDetails?.subscriptionPlan?.isActive
                              ? `${planDetails?.subscriptionPlan?.planName?.toUpperCase()} PLAN`
                              : `${planDetails?.subscriptionPlan?.planName?.toUpperCase()} PLAN EXPIRED`}
                          </button>
                        </Box>
                      </Box>

                      <Box
                        sx={{
                          display: "flex",
                          flexDirection: "column",
                          alignItems: "center",
                          justifyContent: "center",
                        }}
                      >
                        <Tooltip
                          title={
                            loginData?.roleName === "Owner" ||
                            loginData?.roleName === "Admin"
                              ? ""
                              : "Access Denied"
                          }
                          placement="top"
                          open={editTooltip}
                          onClose={() => setEditTooltip(false)}
                        >
                          <CommonButton
                            primaryAction={{
                              label: "Edit Details",
                              icon: <EditSvg />,
                              onClick:
                                loginData?.roleName === "Owner" ||
                                loginData?.roleName === "Admin"
                                  ? handleClickEdit
                                  : undefined,
                              disabled:
                                loginData?.roleName !== "Owner" &&
                                loginData?.roleName !== "Admin",
                            }}
                          />
                        </Tooltip>
                        <Box className={classes.warnText}>
                          <Typography
                            sx={{ fontSize: 10 }}
                            className={classes.grayColor}
                          >
                            *Only owner & admins can edit
                          </Typography>
                        </Box>
                      </Box>
                    </Box>

                    <Box
                      mt={{ xs: 2, md: 3 }}
                      // mr={3}
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        flexDirection: { xs: "column", sm: "row", md: "row" },
                        // flexWrap: { xs: "wrap", md: "nowrap" },
                        gap: { xs: 1, sm: 0 },
                      }}
                    >
                      <Box
                        // mb={{ xs: 0, md: 0 }}
                        sx={{
                          width: { xs: "100%", sm: "38%" },
                        }}
                      >
                        <Typography
                          variant="body2"
                          // mb={1}
                          className={classes.grayColor}
                        >
                          Company Email
                        </Typography>
                        <Typography
                          variant="body2"
                          className={classes.blackColor}
                        >
                          {companyDetails?.company?.businessEmail || ""}
                        </Typography>
                      </Box>

                      <Box
                        mb={{ xs: 0, md: 0 }}
                        mx={{ xs: 0, md: 4 }}
                        sx={{
                          width: { sm: "30%", md: "25%" },
                        }}
                      >
                        <Typography
                          variant="body2"
                          className={classes.grayColor}
                        >
                          Phone
                        </Typography>
                        <Typography
                          variant="body2"
                          className={classes.blackColor}
                        >
                          {companyDetails?.company?.countryCode &&
                          companyDetails?.company?.phoneNumber
                            ? companyDetails?.company?.countryCode +
                                " " +
                                companyDetails?.company?.phoneNumber || ""
                            : ""}
                          {/* djnnfuf cnfnffu */}
                        </Typography>
                      </Box>

                      <Box
                        mb={{ xs: 0, md: 0 }}
                        mx={{ xs: 0, md: 4 }}
                        sx={{
                          width: "27%",
                        }}
                      >
                        <Typography
                          variant="body2"
                          className={classes.grayColor}
                        >
                          GST
                        </Typography>
                        <Tooltip
                          title={companyDetails?.company?.gstNumber || ""}
                        >
                          <Typography
                            variant="body2"
                            className={classes.blackColor}
                          >
                            {companyDetails?.company?.gstNumber || ""}
                          </Typography>
                        </Tooltip>
                      </Box>
                    </Box>

                    <Box
                      mt={{ xs: 2, md: 3 }}
                      // mr={3}
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        flexDirection: { xs: "column", sm: "row", md: "row" },
                        // flexWrap: { xs: "wrap", md: "nowrap" },
                        gap: { xs: 1, sm: 0 },
                      }}
                      gap={1}
                    >
                      <Box
                        mt={{ xs: 2, md: 0 }}
                        sx={{
                          width: { xs: "80%", sm: "38%" },
                        }}
                      >
                        <Typography
                          variant="body2"
                          className={classes.grayColor}
                        >
                          Company Website
                        </Typography>
                        <Typography
                          variant="body2"
                          className={classes.blackColor}
                        >
                          {companyDetails?.company?.businessWebsite || ""}
                          {/* ahextexhndshcdshcnjk */}
                        </Typography>
                      </Box>
                      <Box
                        mt={{ xs: 2, md: 0 }}
                        mx={{ xs: 0, md: 4 }}
                        sx={{
                          width: { sm: "30%", md: "25%" },
                        }}
                      >
                        <Typography
                          variant="body2"
                          // mb={1}
                          className={classes.grayColor}
                        >
                          Country
                        </Typography>
                        <Typography
                          variant="body2"
                          className={classes.blackColor}
                        >
                          {companyDetails?.company?.countryName || ""}
                        </Typography>
                      </Box>

                      <Box
                        mt={{ xs: 2, md: 0 }}
                        mx={{ xs: 0, md: 4 }}
                        sx={{
                          width: "27%",
                        }}
                      >
                        <Typography
                          variant="body2"
                          // mb={1}
                          // className={classes.grayColor}
                        ></Typography>
                        <Button
                          // className={classes.blackColor}
                          onClick={() => {
                            handlePreviewOpenDialog();
                          }}
                          //  className={classes.editButtonContainer}
                          sx={{
                            width: "120px",
                            height: "32px",
                            fontSize: 10,
                            "&:hover": {
                              cursor: "pointer",
                              backgroundColor: "rgba(68, 71, 70, 0.08)",
                            },
                            //  color: `${bgColors.black1} !important`,
                            opacity: "60% !important",
                            // fontWeight: "600 !important",
                            borderRadius: "8px",
                            border: `1px solid ${bgColors?.green}`,
                            color: bgColors?.green,
                            animation: animate ? `${pulse} 2s` : "none",
                          }}
                        >
                          {/* {companyDetails?.company?.businessWebsite || ""} */}
                          {/* ahextexhndshcdshcnjk */}
                          Profile preview
                        </Button>
                      </Box>
                    </Box>

                    <Box
                      sx={{
                        display: "flex",
                        flexDirection: "row",
                      }}
                    >
                      <Box>
                        <Box
                          mt={3}
                          sx={{
                            width: "80%",
                          }}
                        >
                          {/* <Box mb={{ xs: 2, md: 0 }} sx={{ width: "130px" }}> */}
                          <Typography
                            variant="body2"
                            mb={1}
                            className={classes.grayColor}
                          >
                            Company Address
                          </Typography>
                          <Typography
                            variant="body2"
                            className={classes.blackColor}
                          >
                            {companyDetails?.company?.companyAddress || ""}
                          </Typography>
                          {/* </Box> */}
                        </Box>

                        <Box mt={3}>
                          <Typography
                            variant="body2"
                            mb={1}
                            className={classes.grayColor}
                          >
                            Description
                          </Typography>
                          <Typography
                            variant="body2"
                            className={classes.blackColor}
                          >
                            {companyDetails?.company?.description || ""}
                          </Typography>
                        </Box>
                      </Box>
                      <Box>
                        <PreviewPopover
                          open={openPreviewDialog}
                          handleClose={handlePreviewCloseDialog}
                        />
                        {/* <DevicePreview /> */}
                      </Box>
                    </Box>
                  </Box>

                  {/* {loginData && !loginData?.roleName?.includes("Admin") && ( */}
                  <Box mt={3}>
                    <Box p={2} className={classes.mainBorderStyles} gap={2}>
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                          flexDirection: "column",
                          flexWrap: { xs: "wrap", md: "nowrap" },
                          // width: { xs: "100%", sm: "100%", md: "42%" },
                        }}
                      >
                        <Box>
                          <Typography
                            className={classes.blackColor}
                            variant="body2"
                          >
                            Compliance Info
                          </Typography>
                        </Box>
                        <Box
                          sx={{
                            display: { xs: "flex", md: "flex" },
                            justifyContent: "space-between",
                            flexDirection: { xs: "column", sm: "row" },
                            width: { xs: "100%" },
                          }}
                          mt={3}
                          pb={3}
                          // className={classes.emailContainer1}
                          gap={1}
                        >
                          <Box
                          // sx={{
                          //   width: "38%",
                          // }}
                          >
                            <Typography
                              variant="body2"
                              mb={1}
                              className={classes.grayColor}
                            >
                              Legal name of Company
                            </Typography>
                            <Typography
                              variant="body2"
                              className={classes.blackColor}
                            >
                              {companyDetails?.company?.companyLegalName || ""}
                            </Typography>
                          </Box>
                          <Box
                            // sx={{
                            //   width: "30%",
                            // }}
                            mx={{ xs: 0, md: 4 }}
                          >
                            <Typography
                              variant="body2"
                              mb={1}
                              className={classes.grayColor}
                            >
                              Company Type
                            </Typography>
                            <Typography
                              variant="body2"
                              className={classes.blackColor}
                            >
                              {splitCamelCaseAndJoin(
                                companyDetails?.company?.companyType || ""
                              )}
                            </Typography>
                          </Box>
                          <Box
                          // sx={{
                          //   width: "23%",
                          // }}
                          >
                            <Typography
                              variant="body2"
                              mb={1}
                              className={classes.grayColor}
                            >
                              Company Registered
                            </Typography>
                            <Typography
                              variant="body2"
                              className={classes.blackColor}
                            >
                              {companyDetails?.company?.companyRegistered
                                ? "Yes"
                                : "No"}
                            </Typography>
                          </Box>
                        </Box>
                      </Box>

                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "space-between",
                          flexDirection: "column",
                          flexWrap: { xs: "wrap", md: "nowrap" },
                          width: { xs: "100%" },
                        }}
                      >
                        <Box mt={3}>
                          <Typography
                            className={classes.blackColor}
                            variant="body2"
                          >
                            Customer Care Information
                          </Typography>
                        </Box>
                        <Box
                          mt={1}
                          pb={3}
                          // className={classes.emailContainer1}
                          sx={{
                            display: "flex",
                            justifyContent: "space-between",
                            flexDirection: {
                              xs: "column",
                              sm: "row",
                              md: "row",
                            },
                          }}
                          gap={1}
                        >
                          <Box
                          // sx={{
                          //   width: "38%",
                          // }}
                          >
                            <Typography
                              variant="body2"
                              mb={1}
                              className={classes.grayColor}
                            >
                              Customer Care Email
                            </Typography>
                            <Typography
                              variant="body2"
                              className={classes.blackColor}
                            >
                              {companyDetails?.company?.customerCareEmail || ""}
                            </Typography>
                          </Box>
                          <Box
                            // sx={{
                            //   width: "30%",
                            // }}
                            mx={{ xs: 0, md: 4 }}
                          >
                            <Typography
                              variant="body2"
                              mb={1}
                              className={classes.grayColor}
                            >
                              Phone
                            </Typography>
                            <Typography
                              variant="body2"
                              className={classes.blackColor}
                            >
                              {companyDetails?.company
                                ?.customerCareCountryCode &&
                              companyDetails?.company?.customerCarePhone
                                ? companyDetails?.company
                                    ?.customerCareCountryCode +
                                    " " +
                                    companyDetails?.company
                                      ?.customerCarePhone || ""
                                : ""}
                            </Typography>
                          </Box>

                          <Box
                            sx={{
                              width: "23%",
                            }}
                          ></Box>
                          {/* <Box></Box> */}
                        </Box>

                        <Box mt={3}>
                          <Typography
                            className={classes.blackColor}
                            variant="body2"
                          >
                            Grievance Officer Information
                          </Typography>
                        </Box>
                        <Box
                          mt={1}
                          pb={3}
                          sx={{
                            display: "flex",
                            justifyContent: "space-between",
                            flexDirection: {
                              xs: "column",
                              sm: "row",
                              md: "row",
                            },
                          }}
                          // className={classes.emailContainer1}
                          gap={1}
                        >
                          <Box
                            sx={{
                              width: "38%",
                            }}
                          >
                            <Typography
                              variant="body2"
                              mb={1}
                              className={classes.grayColor}
                            >
                              Name
                            </Typography>
                            <Typography
                              variant="body2"
                              className={classes.blackColor}
                            >
                              {companyDetails?.company?.grievanceOfficerName ||
                                ""}
                            </Typography>
                          </Box>
                          <Box
                            sx={{
                              width: "30%",
                            }}
                            mx={{ xs: 0, md: 4 }}
                          >
                            <Typography
                              variant="body2"
                              mb={1}
                              className={classes.grayColor}
                            >
                              Email
                            </Typography>
                            <Typography
                              variant="body2"
                              className={classes.blackColor}
                            >
                              {/* sfvbhsvus */}
                              {companyDetails?.company?.grievanceOfficerEmail ||
                                ""}
                            </Typography>
                          </Box>
                          <Box
                            sx={{
                              width: "23%",
                            }}
                            // sx={{
                            //   marginRight: companyDetails?.company?.grievanceOfficerPhone
                            //     ? "0px"
                            //     : "15px",
                            // }}
                          >
                            <Typography
                              variant="body2"
                              mb={1}
                              className={classes.grayColor}
                            >
                              Phone
                            </Typography>
                            <Typography
                              variant="body2"
                              className={classes.blackColor}
                            >
                              {companyDetails?.company
                                ?.grievanceOfficerCountryCode &&
                              companyDetails?.company?.grievanceOfficerPhone
                                ? companyDetails?.company
                                    ?.grievanceOfficerCountryCode +
                                    " " +
                                    companyDetails?.company
                                      ?.grievanceOfficerPhone || ""
                                : ""}
                            </Typography>
                          </Box>
                          {/* <Box></Box> */}
                        </Box>
                      </Box>
                    </Box>
                  </Box>
                  {/* )} */}
                </Box>
              </>

              <Box>
                <CommonTable
                  columns={manageTeamColumns}
                  data={transformedData}
                  actions={renderActions}
                  rowIdKey="id"
                  isLoading={teamMembersStatus === "loading"}
                  title="Manage Team"
                  searchProps={{
                    value: searchTeamMember,
                    onChange: setSearchTeamMember,
                    placeholder: "Search team members",
                  }}
                  primaryAction={{
                    label: "Add Member",
                    onClick: handleOpenDialog,
                    // disabled: !hasAccess("newTeamMember"),
                    // tooltip: !hasAccess("newTeamMember")
                    //   ? "You don't have permission to add team member"
                    //   : "",
                  }}
                  heightOfTable="auto"
                  showPagination={true}
                  page={page}
                  onPageChange={handleChangePage}
                  totalPages={Math.ceil(
                    getAllTeamMembersData?.total / rowsPerPage
                  )}
                  count={getAllTeamMembersData?.total}
                  perPage={rowsPerPage}
                />
                {/* <Box
                  m={3}
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    flexDirection: "column",
                    flexWrap: { xs: "wrap", md: "nowrap" },
                  }}

                  // className={classes.manageTeamContainer}
                >
                  
                </Box> */}
                {/* <TablePagination
                  rowsPerPageOptions={[10, 25]}
                  component="div"
                  count={
                    getAllTeamMembersData ? getAllTeamMembersData.length : 0
                  }
                  rowsPerPage={rowsPerPage}
                  page={page}
                  onPageChange={handleChangePage}
                  onRowsPerPageChange={handleChangeRowsPerPage}
                /> */}
              </Box>
              <AddNewTeamMember
                open={openDialog}
                handleClose={handleCloseDialog}
              />
              <DeletePopUp
                open={openDeleteDialog}
                handleClose={handleCloseDeleteDialog}
                handleDelete={handleDeleteTeamMember}
                isLoading={isDeleteLoading}
                title="Delete Team Member"
                description="Are you sure you want to delete this team member?"
              />
              <EditTeamMember
                open={openEditDialog}
                handleClose={handleCloseEditDialog}
                data={editTeamMember}
              />
            </Grid>
          )}
        </Grid>
      ) : (
        <NoAccessPage />
      )}
    </>
  );
};

export default ManageCompany;
