import {
  Autocomplete,
  Box,
  Button,
  Checkbox,
  Chip,
  CircularProgress,
  FormControl,
  FormControlLabel,
  IconButton,
  InputLabel,
  ListItemText,
  MenuItem,
  Pagination,
  PaginationItem,
  Paper,
  Select,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
} from "@mui/material";
import { useEffect, useState } from "react";
import FilterIconSvg from "../../assets/svgs/FilterIconSvg";
import { useAppSelector } from "../../utils/redux-hooks";
import { useDispatch } from "react-redux";
import { toastActions } from "../../utils/toastSlice";
import DeleteIconSvg from "../../assets/svgs/DeleteIconSvg";
import { CAMPAIGN_API } from "../../Apis/Campaign/Campaign";

interface ContactSelectionOptionProps {
  classes: any;
  formData: any;
  setFormData: any;
  formErrors: any;
  setFormErrors: any;
  setAnchorElement: any;
  choosedAudience: any;
  setChoosedAudience: any;
  contacts: any;
  setContacts: any;
  loadingContacts: boolean;
  setloadingContacts: any;
  searchContactQuery: string;
  setSearchContactQuery: any;
  contactsPage: number;
  setContactsPage: any;
  contactsPageCount: number;
  setContactsPageCount: any;
  filterData: any;
  setFilterData: any;
  chatStatusFilter: any;
  setChatStatusFilter: any;
  setFilter: any;
  setStatusFilter: any;
}

function ContactSelectionOption(props: ContactSelectionOptionProps) {
  const {
    classes,
    formData,
    setFormData,
    formErrors,
    setFormErrors,
    setAnchorElement,
    choosedAudience,
    setChoosedAudience,
    contacts,
    setContacts,
    loadingContacts,
    setloadingContacts,
    searchContactQuery,
    setSearchContactQuery,
    contactsPage,
    setContactsPage,
    contactsPageCount,
    setContactsPageCount,
    filterData,
    setFilterData,
    chatStatusFilter,
    setChatStatusFilter,
    setFilter,
    setStatusFilter,
  } = props;

  const [selectedContactFile, setSelectedContactFile] = useState<any>(null);

  const dispatch = useDispatch();
  const tagsArray = useAppSelector(
    (state: any) => state.getContactTagsData.data
  );
  const contactsSlice: any = useAppSelector(
    (state: any) => state?.contactsData
  );

  //redux state

  //handler Functions
  function handleAudienceSelection(e: any) {
    const name = e?.target?.name;
    if (name === "autoComplete") {
      setChoosedAudience((prev: any) => {
        if (prev.showAutoComplete) {
          setFormData((prev: any) => ({
            ...prev,
            contacts: [],
          }));
        }
        if (prev.showAutoComplete && !prev.showFileUpload) {
          setFormErrors((errors: any) => ({
            ...errors,
            chooseYourAudience: "Audience is required",
            contacts: "",
          }));
        } else if (prev.showAutoComplete && prev.showFileUpload) {
          setFormErrors((errors: any) => ({
            ...errors,
            contacts: "",
          }));
        } else {
          setFormErrors((errors: any) => ({
            ...errors,
            chooseYourAudience: "",
          }));
        }
        return {
          ...prev,
          showAutoComplete: !prev?.showAutoComplete,
        };
      });
    } else if (name === "fileUpload") {
      setChoosedAudience((prev: any) => {
        if (prev.showAutoComplete) {
          setFormData((prev: any) => ({
            ...prev,
            importedContactsFile: "",
            importedContactsFileId: "",
            importedContactsFileUrl: "",
          }));
        }

        if (prev.showFileUpload && !prev.showAutoComplete) {
          setFormErrors((errors: any) => ({
            ...errors,
            chooseYourAudience: "Audience is required",
            importedContacts: "",
          }));
        } else if (prev.showAutoComplete && prev.showFileUpload) {
          setFormErrors((errors: any) => ({
            ...errors,
            importedContacts: "",
          }));
        } else {
          setFormErrors((errors: any) => ({
            ...errors,
            chooseYourAudience: "",
          }));
        }
        return {
          ...prev,
          showFileUpload: !prev?.showFileUpload,
        };
      });
    }
  }

  const toggleContactSelection = (contact: any) => {
    if (formData.contacts.some((c: any) => c.contact === contact.contact)) {
      let newContacts = formData.contacts.filter(
        (c: any) => c.contact !== contact.contact
      );
      setFormData({ ...formData, contacts: newContacts });
    } else {
      let newContacts = [...formData.contacts, contact];
      setFormData({ ...formData, contacts: newContacts });
    }
  };
  function hasAllContacts(array1: any, array2: any): boolean {
    const contactSet = new Set(array1.map((contact: any) => contact.contactId));
    return array2?.every((contact: any) => contactSet.has(contact.contactId));
  }

  const handleContactsPageChange = (
    event: React.ChangeEvent<unknown>,
    value: number
  ) => {
    setContactsPage(value);
  };
  function removeMatchingContacts(array1: any, array2: any) {
    const contactSet = new Set(array2.map((contact: any) => contact.contactId));
    return array1.filter((contact: any) => !contactSet.has(contact.contactId));
  }

  const handleSelectAll = () => {
    if (hasAllContacts(formData.contacts, contacts)) {
      let newContacts = removeMatchingContacts(formData.contacts, contacts);
      setFormData({ ...formData, contacts: newContacts });
    } else {
      let newContacts = removeMatchingContacts(formData.contacts, contacts);
      setFormData((prev: any) => ({
        ...formData,
        contacts: [...newContacts, ...contacts],
      }));
      setFormErrors({ ...formErrors, contacts: "" });
    }
  };

  const capitalizeFirstLetter = (string: any) => {
    try {
      return string.toString().charAt(0).toUpperCase() + string.slice(1);
    } catch {
      return "";
    }
  };
  const handleMainFilterClick = (event: any) => {
    setAnchorElement(event.currentTarget);
  };

  const handleSaveFileInBytes = (selectedFiles: any) => {
    if (selectedFiles.length === 0) {
      return;
    }
    const reader = new FileReader();
    reader.onload = async (event) => {
      try {
        const binaryString = event.target?.result;
        if (!binaryString) {
          return;
        }

        const bytes = new Uint8Array(binaryString as ArrayBuffer);

        const blob = new Blob([bytes], { type: selectedFiles[0].type });
        const data = {
          File: blob,
          fileName: selectedFiles[0].name,
          module: "campaigns",
        };
        const response = await CAMPAIGN_API.createBlobId(data);

        setFormData((prev: any) => ({
          ...prev,
          importedContactsFile: response.data.data.name,
          importedContactsFileId: response.data.data.id,
          importedContactsFileUrl: response.data.data.url,
        }));
      } catch (error: any) {
        dispatch(
          toastActions.setToaster({
            type: "error",
            message: `${error?.response?.data?.message}`,
          })
        );
      }
    };

    reader.readAsArrayBuffer(selectedFiles[0]);
  };

  async function handleContactsFileImport(
    event: React.ChangeEvent<HTMLInputElement>
  ) {
    const files = Array.from(event?.target?.files || []);
    const file = files[0];

    if (file) {
      const validMimeTypes = [
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "application/vnd.ms-excel",
      ];
      if (!validMimeTypes.includes(file?.type)) {
        dispatch(
          toastActions.setToaster({
            type: "error",
            message: "File is not supported, only upload Excel files",
          })
        );
        return;
      }

      handleSaveFileInBytes(files);
      setSelectedContactFile(file.name);
      setFormErrors((errors: any) => ({
        ...errors,
        importedContacts: "",
      }));
    }
  }

  function handleDeleteContactsFile() {
    setSelectedContactFile("");
    setFormData((prev: any) => ({
      ...prev,

      importedContactsFile: "",
      importedContactsFileId: "",
      importedContactsFileUrl: "",
    }));
  }

  function onClearAllFilter() {
    setFilter("");
    setFilterData("");
    setStatusFilter("");
    setChatStatusFilter("");
  }

  useEffect(() => {
    setSelectedContactFile(() => {
      return formData?.importedContactsFile;
    });
  }, [formData]);

  return (
    <>
      <Box mb={2}>
        <InputLabel className={classes.blackColor}>
          Choose Your Audience
        </InputLabel>
        <Box
          sx={{
            display: "flex",
            flexDirection: { xs: "column", sm: "row" },
            marginBottom: 2,
            marginLeft: 2,
          }}
        >
          <FormControlLabel
            control={
              <Checkbox
                name="autoComplete"
                checked={choosedAudience?.showAutoComplete}
                onChange={handleAudienceSelection}
              />
            }
            label="Select from Contacts"
          />
          <FormControlLabel
            control={
              <Checkbox
                name="fileUpload"
                checked={choosedAudience?.showFileUpload}
                onChange={handleAudienceSelection}
              />
            }
            label="Upload a File"
          />
        </Box>
        {formErrors?.chooseYourAudience && (
          <Typography variant="body2" sx={{ color: "red" }}>
            {formErrors?.chooseYourAudience}
          </Typography>
        )}
      </Box>

      {choosedAudience.showAutoComplete && (
        <Box mb={2}>
          <InputLabel className={classes.blackColor}>Audience</InputLabel>
          <Box sx={{ mb: 2, display: "flex", gap: 2 }}>
            <TextField
              label="Search contacts"
              variant="outlined"
              value={searchContactQuery}
              onChange={(e) => {
                setContactsPage(1);
                setSearchContactQuery(e.target.value);
              }}
              size="small"
              sx={{ flexGrow: 1 }}
              InputProps={{
                endAdornment: (
                  <>
                    <IconButton
                      onClick={handleMainFilterClick}
                      color="success"
                      sx={{
                        position: "absolute",
                        right: "10px",
                        textTransform: "none",
                        height: "20px",
                      }}
                    >
                      <FilterIconSvg />
                    </IconButton>
                  </>
                ),
              }}
            />
          </Box>
          {(filterData || chatStatusFilter) && (
            <Box sx={{ mb: 2 }}>
              {/* Header Section */}
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                }}
              >
                <Typography variant="subtitle2" gutterBottom>
                  Applied Filters:
                </Typography>

                <Button
                  size="small"
                  onClick={onClearAllFilter}
                  sx={{
                    textTransform: "none",
                    color: "text.secondary",
                    "&:hover": { color: "text.primary" },
                  }}
                >
                  Clear all
                </Button>
              </Box>

              {/* Filter Chips */}
              <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1, mt: 1 }}>
                {chatStatusFilter && (
                  <Chip
                    label={chatStatusFilter}
                    onDelete={() => {
                      setChatStatusFilter("");
                      setStatusFilter("");
                    }}
                    variant="outlined"
                    size="small"
                  />
                )}
                {filterData && (
                  <Chip
                    label={
                      tagsArray?.find((tag: any) => tag.id === filterData)?.tag
                    }
                    onDelete={() => {
                      setFilterData("");
                      setFilter("");
                    }}
                    variant="outlined"
                    size="small"
                  />
                )}
              </Box>
            </Box>
          )}
          {formData.contacts.length > 0 && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Selected Contacts: {formData.contacts.length}
              </Typography>
              <Stack
                direction="row"
                spacing={1}
                sx={{ flexWrap: "wrap", gap: 1 }}
              >
                {formData.contacts
                  .filter((_: any, index: number) => index < 2)
                  .map((contact: any) => (
                    <Chip
                      key={contact.name}
                      label={contact.name}
                      onDelete={() => toggleContactSelection(contact)}
                    />
                  ))}
                {formData.contacts.length > 2 && (
                  <Chip
                    label={`+${formData.contacts.length - 2} more`}
                    onDelete={() => {
                      setFormData({ ...formData, contacts: [] });
                    }}
                  />
                )}
              </Stack>
            </Box>
          )}

          <TableContainer
            component={Paper}
            sx={{ flexGrow: 1, maxHeight: "50vh", overflow: "auto" }}
          >
            <Table stickyHeader size="small">
              <TableHead>
                <TableRow>
                  <TableCell padding="checkbox">
                    <Checkbox
                      checked={hasAllContacts(formData.contacts, contacts)}
                      onChange={() => handleSelectAll()}
                    />
                  </TableCell>
                  <TableCell>Name</TableCell>
                  <TableCell>Phone</TableCell>
                  <TableCell>Status</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {contacts?.map((contact: any) => (
                  <TableRow
                    key={contact.contactId}
                    selected={formData.contacts.some(
                      (c: any) => c.contactId === contact.contactId
                    )}
                    hover
                    onClick={() => toggleContactSelection(contact)}
                  >
                    <TableCell padding="checkbox">
                      <Checkbox
                        checked={formData.contacts.some(
                          (c: any) => c.contactId === contact.contactId
                        )}
                        onChange={() => toggleContactSelection(contact)}
                      />
                    </TableCell>
                    <TableCell>{contact.name}</TableCell>
                    <TableCell>{contact.contact}</TableCell>
                    <TableCell>
                      <Box
                        className={
                          contact?.chatStatus === 0
                            ? classes?.greenButton
                            : contact?.chatStatus === 2
                            ? classes?.yellowButton
                            : contact?.chatStatus === 3
                            ? classes?.darkGreenButton
                            : classes?.redButton
                        }
                      >
                        <Typography
                          sx={{ fontSize: "10px" }}
                          alignItems="center"
                        >
                          {capitalizeFirstLetter(
                            contact?.chatStatus === 0
                              ? "Open"
                              : contact?.chatStatus === 2
                              ? "Expired"
                              : contact?.chatStatus === 3
                              ? "New"
                              : "Resolved"
                          )}
                        </Typography>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          {loadingContacts && (
            <Box sx={{ display: "flex", justifyContent: "center", p: 2 }}>
              <CircularProgress size={24} />
            </Box>
          )}

          <Box sx={{ display: "flex", justifyContent: "center", mt: 2, mb: 2 }}>
            <Pagination
              count={contactsPageCount}
              page={contactsPage}
              onChange={handleContactsPageChange}
              color="primary"
              showFirstButton
              showLastButton
              renderItem={(item) => (
                <PaginationItem
                  {...item}
                  disabled={loadingContacts || item.disabled}
                />
              )}
            />
          </Box>
        </Box>
      )}
      {choosedAudience?.showFileUpload && (
        <Box mb={2}>
          {contactsSlice?.data?.exampleForImport && (
            <Box
              style={{
                display: "flex",
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "center",
                marginTop: "10px",
              }}
            >
              <img
                style={{ width: "30px", height: "30px" }}
                src="/images/xlsxIcon.png"
                alt="excel-file"
              />
              <a
                className={classes.blackColor}
                href={contactsSlice?.data?.exampleForImport}
                download="excel_file.xlsx"
              >
                Click here to download a sample file
              </a>
            </Box>
          )}
          <InputLabel className={classes.blackColor}>
            Import from Device
          </InputLabel>
          <Box>
            <Box>
              <Button
                onClick={() => document.getElementById("file-input")?.click()}
              >
                {selectedContactFile ? selectedContactFile : "Choose File"}
              </Button>
              <IconButton onClick={handleDeleteContactsFile} color="warning">
                <DeleteIconSvg />
              </IconButton>
            </Box>
            <input
              id="file-input"
              hidden
              type="file"
              accept=".csv, .xls, .xlsx"
              onChange={handleContactsFileImport}
            />
          </Box>
          {formErrors?.importedContacts && (
            <Typography variant="body2" sx={{ color: "red" }}>
              {formErrors?.importedContacts}
            </Typography>
          )}
        </Box>
      )}
    </>
  );
}

export default ContactSelectionOption;
