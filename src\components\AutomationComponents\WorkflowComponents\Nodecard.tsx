import React from 'react';
import { Box, Typography, Tooltip, useTheme } from '@mui/material';
import { bgColors } from '../../../utils/bgColors';

interface NodeCardProps {
  icon: React.ReactNode;
  label: string;
  description: string;
  isCollapsed: boolean;
  onDragStart: (event: React.DragEvent) => void;
  draggable: boolean;
  onClick?: () => void;
  sx?: any;
  disabled?: boolean;
  disabledReason?: string;
}

const NodeCard: React.FC<NodeCardProps> = ({
  icon,
  label,
  description,
  isCollapsed,
  onDragStart,
  draggable,
  onClick,
  sx,
  disabled = false,
  disabledReason,
}) => {
  const theme = useTheme();

  const cardContent = (
    <Box
      onDragStart={!disabled ? onDragStart : undefined}
      draggable={!disabled && draggable}
      onClick={!disabled ? onClick : undefined}
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        gap: 0.5,
        padding: { xs: '8px', sm: '12px' },
        border: `1px solid ${bgColors.gray1}`,
        borderRadius: '8px',
        backgroundColor: 'white',
        cursor: disabled ? 'not-allowed' : 'pointer',
        opacity: disabled ? 0.6 : 1,
        transition: 'all 0.2s ease',
        '&:hover': !disabled ? {
          boxShadow: theme.shadows[2],
          transform: 'translateY(-2px)',
        } : {},
        ...sx,
      }}
    >
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: { xs: '32px', sm: '24px' },
          height: { xs: '32px', sm: '24px' },
          color: disabled ? theme.palette.text.disabled : theme.palette.primary.main,
        }}
      >
        {icon}
      </Box>
      <Box
        sx={{
          display: { xs: 'flex', sm: 'flex' },
          flexDirection: 'column',
          alignItems: 'center',
          textAlign: 'center',
          width: '100%',
        }}
      >
        <Typography
          variant="subtitle2"
          sx={{
            fontWeight: 600,
            fontSize: { xs: '12px', sm: '14px' },
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            width: '100%',
            color: disabled ? theme.palette.text.disabled : 'inherit',
          }}
        >
          {label}
        </Typography>
        <Typography
          variant="caption"
          sx={{
            color: disabled ? theme.palette.text.disabled : theme.palette.text.secondary,
            fontSize: '9px !important',
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            width: '100%',
          }}
        >
          {description}
        </Typography>
      </Box>
    </Box>
  );

  if (disabled && disabledReason) {
    return (
      <Tooltip 
        title={disabledReason}
        placement="right"
        arrow
      >
        <Box sx={{ cursor: 'not-allowed' }}>
          {cardContent}
        </Box>
      </Tooltip>
    );
  }

  return cardContent;
};

export default NodeCard;
