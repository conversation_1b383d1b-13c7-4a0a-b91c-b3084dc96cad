import React, { useEffect, useState } from "react";
import { Grid, Box, Typography, Popover, Tooltip, Paper } from "@mui/material";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import { makeStyles } from "@mui/styles";
import { toastActions } from "../../utils/toastSlice";
import { useAppDispatch, useAppSelector } from "../../utils/redux-hooks";
import { removeTemplate } from "../../redux/slices/Templates/DeleteTemplateSlice";
import LoadingComponent from "../common/LoadingComponent";
import { bgColors } from "../../utils/bgColors";
import DeletePopUp from "../common/DeletePopup";
import CampaignIcon from "@mui/icons-material/Campaign";
import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
import EditIcon from "@mui/icons-material/Edit";
import { useLocation, useNavigate } from "react-router-dom";
import { parseTextToHtml, unescapeJsonString } from "../../utils/functions";
import { BiLeftArrowAlt, BiRightArrowAlt } from "react-icons/bi";
import PhoneIcon from "@mui/icons-material/Phone";
import LaunchIcon from "@mui/icons-material/Launch";
import ReplyAllIcon from "@mui/icons-material/ReplyAll";

const useStyles = makeStyles({
  container: {
    boxShadow: "2px 2px 4px 2px #cdcdcd",
    borderRadius: "8px",
    minWidth: "220px",
    width: "100%",
    padding: "10px",
    textAlign: "center",
    height: "100%",
  },
  header: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  popoverContent: {
    padding: "10px",
    display: "flex",
    flexDirection: "column",
    gap: "10px",
    textAlign: "left",
  },
  carouselContainer: {
    position: "relative",
    width: "100%",
    overflow: "hidden",
    marginBottom: "10px",
  },
  carouselTrack: {
    display: "flex",
    transition: "transform 300ms ease",
    width: "100%",
  },
  carouselItem: {
    flex: "0 0 100%",
    transition: "opacity 300ms ease, transform 300ms ease",
  },
  navigationButton: {
    position: "absolute",
    top: "50%",
    transform: "translateY(-50%)",
    background: "rgba(255, 255, 255, 0.8)",
    border: "none",
    borderRadius: "50%",
    width: "32px",
    height: "32px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    cursor: "pointer",
    zIndex: 2,
    "&:hover": {
      background: "rgba(255, 255, 255, 0.9)",
    },
  },
  carouselIndicators: {
    display: "flex",
    justifyContent: "center",
    gap: "4px",
    marginTop: "8px",
  },
  carouselIndicator: {
    width: "8px",
    height: "8px",
    borderRadius: "50%",
    background: "#BDC7D8",
    cursor: "pointer",
    transition: "background-color 300ms ease",
    "&.active": {
      background: "#4CAF50",
    },
  },
});

const TemplateCardView = ({
  templateData,
  userData,
  handleEditToggle,
  hasAccessCheck,
  editLibraryTemplatePermissionTooltipOpen,
  setEditLibraryTemplatePermissionTooltipOpen,
}: any) => {
  const classes = useStyles();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const removeTemplateSlice = useAppSelector(
    (state: any) => state?.removeTemplateData
  );
  const tenantId = useAppSelector((state: any) => state.adminLogin.tenantId);

  const [anchorEl, setAnchorEl] = useState(null);
  const [openDeletePopup, setOpenDeletePopup] = useState(false);
  const [bodyText, setBodyText] = useState("");
  const [isLibDeleteTemplteLoading, setIsLibDeleteTemplteLoading] = useState(false);
  const [deleteLibraryTemplatePermissionTooltipOpen, setDeleteLibraryTemplatePermissionTooltipOpen] = useState(false);
  const [startCampaignPermissionTooltipOpen, setStartCampaignPermissionTooltipOpen] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);

  const handleDeletePopup = () => {
    const hasPermission = hasAccessCheck("deleteLibraryTemplate");
    if (hasPermission) {
      setOpenDeletePopup(true);
      handleClose();
    } else {
      setDeleteLibraryTemplatePermissionTooltipOpen(true);
      setTimeout(() => {
        setDeleteLibraryTemplatePermissionTooltipOpen(false);
      }, 2000);
    }
  };

  const handleDeletePopupClose = () => {
    setOpenDeletePopup(false);
  };

  const handleMoreHorizClick = (event: any) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleStartCampaign = () => {
    const hasPermission = hasAccessCheck("startLibraryCampaign");
    if (hasPermission) {
      navigate("/campaigns/one-time");
    } else {
      setStartCampaignPermissionTooltipOpen(true);
      setTimeout(() => {
        setStartCampaignPermissionTooltipOpen(false);
      }, 2000);
    }
  };

  const handleDeleteTemplate = async () => {
    setIsLibDeleteTemplteLoading(true);
    const deleteData = {
      businessId: userData?.companyId,
      userId: userData?.userId,
      templateId: templateData?.templateId,
    };

    try {
      const deleteResponse: any = await dispatch(removeTemplate(deleteData));
      if (deleteResponse?.meta?.requestStatus === "fulfilled") {
        dispatch(
          toastActions.setToaster({
            type: "success",
            message: deleteResponse?.payload?.message,
          })
        );
      } else {
        dispatch(
          toastActions.setToaster({
            type: "error",
            message: deleteResponse?.payload,
          })
        );
      }
    } catch (err: any) {
      dispatch(
        toastActions.setToaster({
          type: "error",
          message: err?.message,
        })
      );
    }
    setIsLibDeleteTemplteLoading(false);
  };

  const goToPrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
    }
  };

  const goToNext = () => {
    if (currentIndex < templateData?.carouselCards?.length - 1) {
      setCurrentIndex(currentIndex + 1);
    }
  };

  const open = Boolean(anchorEl);

  useEffect(() => {
    const htmlText: any = parseTextToHtml(
      unescapeJsonString(templateData.body)
    );
    setBodyText(htmlText);
  }, [templateData.body]);

  const renderCarouselContent = () => {
    if (!templateData?.carouselCards || templateData?.carouselCards.length === 0) {
      return null;
    }

    const parsedCarouselCards = templateData.carouselCards.map((card: string) => {
      try {
        return JSON.parse(card);
      } catch (error) {
        console.error('Error parsing carousel card:', error);
        return null;
      }
    }).filter(Boolean);

    return (
      <Box className={classes.carouselContainer}>
        {/* Main Body Content */}
        <Box sx={{ mb: 2 }}>
          <Typography sx={{ fontSize: "12px", textAlign: "left" }}>
            {templateData?.header}
          </Typography>
          <Typography
            sx={{
              mt:1,
              fontSize: "12px",
              textAlign: "left",
              overflowWrap: "break-word",
            }}
            dangerouslySetInnerHTML={{ __html: parseTextToHtml(unescapeJsonString(templateData.body)) }}
          />
          <Typography sx={{ fontSize: "12px", textAlign: "left" }}>
            {templateData?.footer}
          </Typography>
        </Box>

        <Box
          className={classes.carouselTrack}
          sx={{
            transform: `translateX(-${currentIndex * 100}%)`,
          }}
        >
          {parsedCarouselCards.map((carousel: any, index: number) => (
            <Box
              key={index}
              className={classes.carouselItem}
              sx={{
                opacity: index === currentIndex ? 1 : 0.7,
                zIndex: index === currentIndex ? 2 : 1,
              }}
            >
              <Paper
                elevation={2}
                sx={{
                  p: 0,
                  borderRadius: 2,
                  maxHeight: "320px",
                  width: "100%",
                  display: "flex",
                  flexDirection: "column",
                  boxShadow: index === currentIndex
                    ? "0 4px 12px rgba(76, 175, 80, 0.25)"
                    : "0 2px 8px rgba(0,0,0,0.1)",
                  backgroundColor: "#fff",
                  transform: index === currentIndex ? "scale(1.02)" : "scale(1)",
                  transition: "transform 300ms ease, box-shadow 300ms ease",
                }}
              >
                {/* Media Section */}
                <Box sx={{ position: "relative", width: "100%" }}>
                  {carousel.MediaUrlType === 4 ? (
                    <video
                      style={{
                        width: "100%",
                        height: "160px",
                        objectFit: "cover",
                        borderTopLeftRadius: "8px",
                        borderTopRightRadius: "8px",
                      }}
                      src={carousel.HeaderMediaUrl}
                      controls={false}
                    />
                  ) : (
                    <Box
                      component="img"
                      src={carousel.HeaderMediaUrl}
                      sx={{
                        width: "100%",
                        height: "160px",
                        objectFit: "cover",
                        borderTopLeftRadius: "8px",
                        borderTopRightRadius: "8px",
                      }}
                    />
                  )}
                </Box>

                {/* Content Section */}
                <Box
                  sx={{
                    p: 1.5,
                    flex: "1 1 auto",
                    overflowY: "auto",
                    "&::-webkit-scrollbar": {
                      width: "4px",
                    },
                    "&::-webkit-scrollbar-thumb": {
                      backgroundColor: "#BDC7D8",
                      borderRadius: "4px",
                    },
                  }}
                >
                  <Typography
                    variant="subtitle1"
                    component="h2"
                    sx={{
                      fontWeight: "600",
                      fontSize: "13px",
                      color: "#1C1E21",
                      mb: 0.5,
                      minHeight: "32px",
                      maxHeight: "none",
                      overflow: "visible",
                    }}
                  >
                    {carousel.Body}
                  </Typography>
                </Box>

                {/* Buttons Section */}
                <Box
                  sx={{
                    borderTop: "1px solid #E4E6EB",
                    width: "100%",
                    marginTop: "auto",
                    flex: "0 0 auto",
                  }}
                >
                  {carousel.CallButtonName && (
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        padding: "8px 12px",
                        fontSize: "12px",
                        color: "#4CAF50",
                        borderBottom: carousel.UrlButtonName ? "1px solid #E4E6EB" : "none",
                        "&:hover": {
                          backgroundColor: "rgba(76, 175, 80, 0.05)",
                        },
                      }}
                    >
                      <PhoneIcon sx={{ fontSize: "16px", mr: 1 }} />
                      {carousel.CallButtonName}
                    </Box>
                  )}
                  {carousel.UrlButtonName && (
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        padding: "8px 12px",
                        fontSize: "12px",
                        color: "#4CAF50",
                        "&:hover": {
                          backgroundColor: "rgba(76, 175, 80, 0.05)",
                        },
                      }}
                    >
                      <LaunchIcon sx={{ fontSize: "16px", mr: 1 }} />
                      {carousel.UrlButtonName}
                    </Box>
                  )}
                  {carousel.QuickReply && (
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        padding: "8px 12px",
                        fontSize: "12px",
                        color: "#4CAF50",
                        "&:hover": {
                          backgroundColor: "rgba(76, 175, 80, 0.05)",
                        },
                      }}
                    >
                      <ReplyAllIcon sx={{ fontSize: "16px", mr: 1 }} />
                      {carousel.QuickReply}
                    </Box>
                  )}
                </Box>
              </Paper>
            </Box>
          ))}
        </Box>

        {/* Navigation Arrows */}
        {currentIndex > 0 && (
          <Box
            className={classes.navigationButton}
            sx={{ 
              left: "4px",
              color: "#4CAF50",
              "&:hover": {
                backgroundColor: "rgba(76, 175, 80, 0.1)",
              }
            }}
            onClick={goToPrevious}
          >
            <BiLeftArrowAlt />
          </Box>
        )}

        {currentIndex < parsedCarouselCards.length - 1 && (
          <Box
            className={classes.navigationButton}
            sx={{ 
              right: "4px",
              color: "#4CAF50",
              "&:hover": {
                backgroundColor: "rgba(76, 175, 80, 0.1)",
              }
            }}
            onClick={goToNext}
          >
            <BiRightArrowAlt />
          </Box>
        )}

        {/* Navigation Dots */}
        <Box className={classes.carouselIndicators}>
          {parsedCarouselCards.map((_: any, index: number) => (
            <Box
              key={index}
              className={`${classes.carouselIndicator} ${
                index === currentIndex ? "active" : ""
              }`}
              onClick={() => setCurrentIndex(index)}
              sx={{
                "&.active": {
                  background: "#4CAF50",
                },
              }}
            />
          ))}
        </Box>
      </Box>
    );
  };

  return (
    <Grid
      className={classes.container}
      sx={{ breakInside: "avoid", margin: "0 0 1.5rem" }}
    >
      <Box className={classes.header}>
        <Tooltip title={templateData?.templateName}>
          <Typography
            sx={{
              color: "#707070",
              fontWeight: "bold",
              fontSize: "14px",
              overflow: "hidden",
              whiteSpace: "nowrap",
              textOverflow: "ellipsis",
              textAlign: "left",
              maxHeight: 22,
            }}
          >
            {templateData?.templateName}
          </Typography>
        </Tooltip>
        <MoreVertIcon
          sx={{ cursor: "pointer", height: "16px", color: "#777" }}
          onClick={handleMoreHorizClick}
        />
      </Box>

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
      >
        <Box className={classes.popoverContent}>
          {!tenantId && (
            <Tooltip
              title="Access Denied"
              placement="top"
              open={startCampaignPermissionTooltipOpen}
              onClose={() => setStartCampaignPermissionTooltipOpen(false)}
            >
              <Typography
                sx={{
                  cursor: "pointer",
                  fontSize: "14px",
                  display: "flex",
                  alignItems: "center",
                }}
                onClick={handleStartCampaign}
              >
                <CampaignIcon sx={{ height: "20px" }} /> &nbsp; Start Campaign
              </Typography>
            </Tooltip>
          )}

          {Number(templateData?.mediaType) !== 6 && (
            <Tooltip
              title="Access Denied"
              placement="top"
              open={editLibraryTemplatePermissionTooltipOpen}
              onClose={() => setEditLibraryTemplatePermissionTooltipOpen(false)}
            >
              <Typography
                sx={{
                  cursor: "pointer",
                  fontSize: "14px",
                  display: "flex",
                  alignItems: "center",
                }}
                onClick={(e: any) => {
                  e.preventDefault();
                  navigate(`/templates/${templateData?.templateId}`);
                  handleClose();
                }}
              >
                <EditIcon sx={{ height: "20px" }} />
                &nbsp; Edit Template
              </Typography>
            </Tooltip>
          )}

          {removeTemplateSlice?.status === "loading" ? (
            <LoadingComponent height="auto" color={bgColors?.blue} />
          ) : (
            <Tooltip
              title="Access Denied"
              placement="top"
              open={deleteLibraryTemplatePermissionTooltipOpen}
              onClose={() => setDeleteLibraryTemplatePermissionTooltipOpen(false)}
            >
              <Typography
                sx={{
                  color: "red",
                  cursor: "pointer",
                  fontSize: "14px",
                  display: "flex",
                  alignItems: "center",
                }}
                onClick={handleDeletePopup}
              >
                <DeleteOutlineIcon sx={{ height: "20px" }} />
                &nbsp; Delete
              </Typography>
            </Tooltip>
          )}
        </Box>
      </Popover>

      {Number(templateData?.mediaType) === 6 ? (
        renderCarouselContent()
      ) : (
        <>
          <Typography sx={{ fontSize: "12px", mb: 2, textAlign: "left" }}>
            {templateData?.header}
          </Typography>
          <Typography
            sx={{
              fontSize: "12px",
              mb: 2,
              textAlign: "left",
              overflowWrap: "break-word",
            }}
            dangerouslySetInnerHTML={{ __html: bodyText }}
          ></Typography>
          <Typography sx={{ fontSize: "12px", mb: 2, textAlign: "left" }}>
            {templateData?.footer}
          </Typography>

          {templateData?.mediaAwsUrl ? (
            templateData?.mediaAwsUrl?.includes("mp4") ? (
              <Box>
                <iframe
                  src={templateData?.mediaAwsUrl}
                  style={{ height: 140, width: "100%" }}
                ></iframe>
              </Box>
            ) : templateData?.mediaAwsUrl?.includes("pdf") ? (
              <Box>
                <iframe
                  src={templateData?.mediaAwsUrl}
                  style={{ height: 140, width: "100%" }}
                ></iframe>
              </Box>
            ) : (
              <Box>
                <img
                  src={templateData?.mediaAwsUrl}
                  alt=""
                  style={{ height: 140, width: "100%" }}
                />
              </Box>
            )
          ) : (
            <></>
          )}

          {/* Buttons Section for Normal Templates */}
          {templateData?.buttons && templateData.buttons.length > 0 && (
            <Box
              sx={{
                borderTop: "1px solid #E4E6EB",
                width: "100%",
                marginTop: "auto",
                flex: "0 0 auto",
              }}
            >
              {templateData.buttons.map((button: any, btnIndex: number) => (
                <Box
                  key={btnIndex}
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    padding: "8px 12px",
                    fontSize: "12px",
                    color: "#4CAF50",
                    borderBottom: btnIndex < templateData.buttons.length - 1
                      ? "1px solid #E4E6EB"
                      : "none",
                    "&:hover": {
                      backgroundColor: "rgba(76, 175, 80, 0.05)",
                    },
                  }}
                >
                  {button?.buttonType === "PHONE_NUMBER" ? (
                    <PhoneIcon sx={{ fontSize: "16px", mr: 1 }} />
                  ) : button?.buttonType === "URL" ? (
                    <LaunchIcon sx={{ fontSize: "16px", mr: 1 }} />
                  ) : button?.buttonType === "QUICK_REPLY" ? (
                    <ReplyAllIcon sx={{ fontSize: "16px", mr: 1 }} />
                  ) : null}
                  {button?.buttonName || button?.buttonValue}
                </Box>
              ))}
            </Box>
          )}
        </>
      )}

      <DeletePopUp
        title="Template"
        open={openDeletePopup}
        handleClose={handleDeletePopupClose}
        handleDelete={handleDeleteTemplate}
        handleLoad={isLibDeleteTemplteLoading}
      />
    </Grid>
  );
};

export default TemplateCardView;
