import React, { useState, useEffect } from "react";
import {
  Autocomplete,
  Box,
  FormControl,
  FormHelperText,
  TextField,
  Typography,
  Stepper,
  Step,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@mui/material";
import TextFeildWithBorderComponet from "../common/TextFieldWithBorderComponent";
import { makeStyles } from "@mui/styles";
import { useAppSelector, useAppDispatch } from "../../utils/redux-hooks";
import { bgColors } from "../../utils/bgColors";
import PersonAddIcon from "@mui/icons-material/PersonAdd";
import { useNavigate } from "react-router-dom";
import { fetchGetAllCountries } from "../../redux/slices/ManageAccount/GetAllCountryDetails";

const useStyles = makeStyles({
  container: {
    backgroundColor: "#ffffff",
    borderRadius: "20px",
    padding: "32px",
    boxShadow: "0px 4px 20px rgba(0, 0, 0, 0.1)",
    minWidth: "500px",
    maxWidth: "600px",
  },
  header: {
    display: "flex",
    alignItems: "center",
    marginBottom: "24px",
  },
  title: {
    color: "#000000",
    fontWeight: "600",
    fontSize: "24px",
    marginLeft: "8px",
  },
  stepperContainer: {
    marginBottom: "32px",
  },
  buttonContainer: {
    display: "flex",
    justifyContent: "space-between",
    marginTop: "32px",
    gap: "16px",
  },
  button: {
    backgroundColor: "#3C3C3C",
    color: "#ffffff",
    height: "40px",
    borderRadius: "12px",
    fontWeight: "600",
    cursor: "pointer",
    border: "none",
    padding: "0 24px",
    fontSize: "14px",
    "&:hover": {
      backgroundColor: "#2C2C2C",
    },
  },
  backButton: {
    backgroundColor: "transparent",
    color: "#3C3C3C",
    height: "40px",
    borderRadius: "12px",
    fontWeight: "600",
    cursor: "pointer",
    border: "2px solid #3C3C3C",
    padding: "0 24px",
    fontSize: "14px",
    "&:hover": {
      backgroundColor: "#f5f5f5",
    },
  },
  loginLink: {
    textAlign: "center",
    marginTop: "16px",
    color: "#666",
    fontSize: "14px",
    cursor: "pointer",
    "&:hover": {
      textDecoration: "underline",
    },
  },
});

const steps = ["Company Information", "Contact Details"];

const SignupFormContainer = () => {
  const classes = useStyles();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const getAllCountries = useAppSelector(
    (state: any) => state.getAllCountries?.data
  );

  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState({
    companyName: "",
    clientName: "",
    email: "",
    businessEmail: "",
    website: "",
    address: "",
    country: "",
  });
  const [formErrors, setFormErrors] = useState({
    companyName: "",
    clientName: "",
    email: "",
    businessEmail: "",
    website: "",
    address: "",
    country: "",
  });

  useEffect(() => {
    dispatch(fetchGetAllCountries());
  }, [dispatch]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    setFormErrors({ ...formErrors, [name]: "" });
  };

  const handleSelectChange = (event: any, newValue: { countryName: any }) => {
    const value = newValue ? newValue.countryName : "";
    setFormData({ ...formData, country: value });
    setFormErrors({ ...formErrors, country: "" });
  };

  const validateStep1 = () => {
    const errors = { ...formErrors };
    let isValid = true;

    if (!formData.companyName.trim()) {
      errors.companyName = "Company name is required.";
      isValid = false;
    } else if (formData.companyName.length > 100) {
      errors.companyName = "Company name cannot be more than 100 characters.";
      isValid = false;
    }

    if (!formData.clientName.trim()) {
      errors.clientName = "Client name is required.";
      isValid = false;
    } else if (formData.clientName.length > 100) {
      errors.clientName = "Client name cannot be more than 100 characters.";
      isValid = false;
    }

    if (!formData.website.trim()) {
      errors.website = "Website is required.";
      isValid = false;
    } else if (
      !/^(https?:\/\/)(?!localhost|127(?:\.\d{1,3}){0,3})(www\.)?[\w-]+(\.[\w-]+)+(:\d+)?(\/[\w-.]*)*\/?$/.test(
        formData.website.trim()
      )
    ) {
      errors.website =
        "Invalid website URL. Ensure it starts with 'https://' and does not use localhost or local IPs.";
      isValid = false;
    }

    setFormErrors(errors);
    return isValid;
  };

  const validateStep2 = () => {
    const errors = { ...formErrors };
    let isValid = true;

    if (!formData.email.trim()) {
      errors.email = "Email address is required.";
      isValid = false;
    } else if (
      !/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(formData.email)
    ) {
      errors.email = "Invalid email address.";
      isValid = false;
    }

    if (!formData.address.trim()) {
      errors.address = "Address is required";
      isValid = false;
    }

    if (!formData.country) {
      errors.country = "Country is required";
      isValid = false;
    }

    setFormErrors(errors);
    return isValid;
  };

  const handleNext = () => {
    if (activeStep === 0 && validateStep1()) {
      setActiveStep(1);
    } else if (activeStep === 1 && validateStep2()) {
      handleSignup();
    }
  };

  const handleBack = () => {
    setActiveStep(0);
  };

  const handleSignup = async () => {
    // TODO: Implement signup API call
    console.log("Signup data:", formData);
    // For now, just navigate to login
    navigate("/login");
  };

  const selectedCountry = formData?.country
    ? getAllCountries?.find(
        (country: { countryName: string }) =>
          country?.countryName === formData?.country
      )
    : null;

  const renderStep1 = () => (
    <Box>
      <Box mb={2}>
        <TextFeildWithBorderComponet
          label="Enter company name"
          name="companyName"
          placeholder=""
          value={formData?.companyName}
          onChange={handleInputChange}
          error={!!formErrors?.companyName}
          helperText={formErrors?.companyName}
        />
      </Box>

      <Box
        sx={{
          display: "flex",
          gap: "20px",
          marginBottom: "16px",
        }}
      >
        <Box flex={1}>
          <TextFeildWithBorderComponet
            label="Enter client name"
            name="clientName"
            value={formData?.clientName}
            onChange={handleInputChange}
            placeholder=""
            error={!!formErrors?.clientName}
            helperText={formErrors?.clientName}
          />
        </Box>

        <Box flex={1}>
          <TextFeildWithBorderComponet
            label="Enter company website"
            name="website"
            value={formData?.website}
            onChange={handleInputChange}
            placeholder=""
            error={!!formErrors?.website}
            helperText={formErrors?.website}
          />
        </Box>
      </Box>
    </Box>
  );

  const renderStep2 = () => (
    <Box>
      <Box mb={2}>
        <TextFeildWithBorderComponet
          label="Enter owner email address"
          name="email"
          value={formData?.email}
          onChange={handleInputChange}
          placeholder=""
          error={!!formErrors?.email}
          helperText={formErrors?.email}
        />
      </Box>

      <Box mb={2}>
        <TextFeildWithBorderComponet
          label="Enter company email address (Optional)"
          name="businessEmail"
          value={formData?.businessEmail}
          onChange={handleInputChange}
          placeholder=""
          error={!!formErrors?.businessEmail}
          helperText={formErrors?.businessEmail}
        />
      </Box>

      <Box mb={2}>
        <TextFeildWithBorderComponet
          label="Enter company address"
          name="address"
          placeholder=""
          value={formData?.address}
          onChange={handleInputChange}
          error={!!formErrors?.address}
          helperText={formErrors?.address}
        />
      </Box>

      <Box mb={2}>
        <FormControl fullWidth size="small" error={!!formErrors?.country}>
          <Autocomplete
            id="country-autocomplete"
            options={getAllCountries || []}
            getOptionLabel={(option) => option.countryName}
            value={selectedCountry}
            onChange={handleSelectChange}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Select Country"
                error={!!formErrors?.country}
                sx={{
                  "& .MuiInputLabel-root": {
                    fontSize: { xs: 12, md: 14 },
                    top: "-5px",
                  },
                  "& .MuiInputLabel-root.Mui-focused": {
                    top: "0px",
                  },
                  "& .MuiFormLabel-filled": {
                    top: "0px",
                  },
                  "& .MuiOutlinedInput-root": {
                    height: "38px",
                    borderRadius: "8px",
                    fontSize: "14px",
                  },
                }}
                InputProps={{
                  ...params.InputProps,
                  style: {
                    fontSize: 14,
                    paddingTop: "0px",
                    paddingBottom: "0px",
                  },
                }}
              />
            )}
            renderOption={(props, option) => (
              <li {...props}>
                <Typography sx={{ fontSize: 14 }}>
                  {option.countryName}
                </Typography>
              </li>
            )}
          />
          {formErrors?.country && (
            <FormHelperText>{formErrors?.country}</FormHelperText>
          )}
        </FormControl>
      </Box>
    </Box>
  );

  return (
    <Box
      className={classes.container}
      sx={{ width: { xs: "90%", sm: "70%", md: "60%", lg: "50%" } }}
    >
      <Box className={classes.header}>
        <PersonAddIcon sx={{ height: "24px", color: "#3C3C3C" }} />
        <Typography className={classes.title}>Create Account</Typography>
      </Box>

      <Box className={classes.stepperContainer}>
        <Stepper activeStep={activeStep} alternativeLabel>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>
      </Box>

      {activeStep === 0 ? renderStep1() : renderStep2()}

      <Box className={classes.buttonContainer}>
        {activeStep === 1 && (
          <button onClick={handleBack} className={classes.backButton}>
            Back
          </button>
        )}
        <button
          onClick={handleNext}
          className={classes.button}
          style={{ marginLeft: activeStep === 0 ? "auto" : "0" }}
        >
          {activeStep === steps.length - 1 ? "Create Account" : "Next"}
        </button>
      </Box>

      <Typography
        className={classes.loginLink}
        onClick={() => navigate("/login")}
      >
        Already have an account? Log in
      </Typography>
    </Box>
  );
};

export default SignupFormContainer;
