import React from "react";
import { Box, Typography, Paper, Button } from "@mui/material";
import PhoneIcon from "@mui/icons-material/Phone";
import LaunchIcon from "@mui/icons-material/Launch";
import ReplyAllIcon from "@mui/icons-material/ReplyAll";
import { BiLeftArrowAlt } from "react-icons/bi";
import { BiRightArrowAlt } from "react-icons/bi";
import {
  NavigationButton,
  CarouselItem,
  CarouselTrack,
  CarouselContent,
  CarouselContainer,
  CarouselIndicators,
  CarouselIndicator,
} from "../../../utils/StyledComponents";

interface DevicePreviewCarouselProps {
  carouselCards: any[];
  currentIndex: number;
  setCurrentIndex: (index: number) => void;
}

const DevicePreviewCarousel: React.FC<DevicePreviewCarouselProps> = ({
  carouselCards,
  currentIndex,
  setCurrentIndex,
}) => {
  const goToPrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
    }
  };

  const goToNext = () => {
    if (currentIndex < carouselCards.length - 1) {
      setCurrentIndex(currentIndex + 1);
    }
  };

  if (!carouselCards || carouselCards.length === 0) {
    return null;
  }

  return (
    <CarouselContainer>
      <CarouselContent>
        <CarouselTrack
          sx={{
            transition: "transform 0.4s cubic-bezier(0.4, 0, 0.2, 1)",
            transform:
              carouselCards.length === 1
                ? "translateX(calc(50% - 120px))" // Center if only one card (half container width - half card width)
                : `translateX(calc(50% - 120px - ${currentIndex * 248}px))`, // Center the current card
            justifyContent: "flex-start",
            mb: 2,
          }}
        >
          {carouselCards.map((carousel: any, index: number) => (
            <CarouselItem
              key={index}
              sx={{
                opacity: index === currentIndex ? 1 : 0.7, // Reduce opacity of non-active cards
                zIndex: index === currentIndex ? 2 : 1, // Bring active card to front
              }}
            >
              <Paper
                key={index}
                elevation={2}
                sx={{
                  p: 0,
                  borderRadius: 2,
                  height: 320,
                  width: "100%",
                  display: "flex",
                  flexDirection: "column",
                  boxShadow:
                    index === currentIndex
                      ? "0 4px 12px rgba(24, 119, 242, 0.25)" // Highlight current card with blue shadow
                      : "0 2px 8px rgba(0,0,0,0.1)",
                  backgroundColor: "#fff",
                  transform:
                    index === currentIndex ? "scale(1.02)" : "scale(1)", // Slightly enlarge current card
                  transition: "transform 300ms ease, box-shadow 300ms ease",
                }}
              >
                {/* Media Section */}
                <Box sx={{ position: "relative", width: "100%" }}>
                  {carousel.mediaUrlType === 4 ? (
                    <video
                      style={{
                        width: "100%",
                        height: "160px", // Increased height for media
                        objectFit: "cover",
                        borderTopLeftRadius: "8px",
                        borderTopRightRadius: "8px",
                      }}
                      src={
                        carousel.headerMediaUrl instanceof File
                          ? URL.createObjectURL(carousel.headerMediaUrl)
                          : carousel.headerMediaUrl
                      }
                      controls={false}
                    />
                  ) : (
                    <Box
                      component="img"
                      src={carousel.headerMediaUrl}
                      sx={{
                        width: "100%",
                        height: "160px", // Increased height for media
                        objectFit: "cover",
                        borderTopLeftRadius: "8px",
                        borderTopRightRadius: "8px",
                      }}
                    />
                  )}
                </Box>

                {/* Content Section */}
                <Box
                  sx={{
                    p: 1.5,
                    flex: "1 1 auto",
                    minHeight: 0,
                    overflow: "hidden",
                  }}
                >
                  <Box
                    sx={{
                      height: "100%",
                    overflowY: "auto",
                      wordBreak: "break-word",
                      mb: 1,
                      pr: 1,
                    "&::-webkit-scrollbar": {
                      width: "4px",
                    },
                    "&::-webkit-scrollbar-thumb": {
                      backgroundColor: "#BDC7D8",
                      borderRadius: "4px",
                    },
                  }}
                >
                  <Typography
                    variant="subtitle1"
                    component="h2"
                    sx={{
                      fontWeight: "600",
                      fontSize: "13px",
                      color: "#1C1E21",
                      mb: 0.5,
                      minHeight: "32px",
                        maxHeight: "none",
                        overflow: "visible",
                    }}
                  >
                    {carousel.body}
                  </Typography>
                  </Box>
                </Box>

                {/* Buttons Section */}
                <Box
                  sx={{
                    borderTop: "1px solid #E4E6EB",
                    width: "100%",
                    marginTop: "auto", // Push to bottom of flex container
                    flex: "0 0 auto", // Don't allow flex to shrink or grow this section
                  }}
                >
                  {carousel.carouselButtons?.map(
                    (button: any, btnIndex: number) => (
                      <Button
                        key={btnIndex}
                        startIcon={
                          button?.buttonType === "PHONE_NUMBER" ? (
                            <PhoneIcon />
                          ) : button?.buttonType === "URL" ? (
                            <LaunchIcon />
                          ) : button?.buttonType === "QUICK_REPLY" ? (
                            <ReplyAllIcon />
                          ) : undefined
                        }
                        sx={{
                          fontSize: 12,
                          width: "100%",
                          justifyContent: "center",
                          textTransform: "none",
                          py: 0.75,
                          borderRadius: 0,
                          borderBottom:
                            btnIndex < carousel.carouselButtons.length - 1
                              ? "1px solid #E4E6EB"
                              : "none",
                          color: "#4CAF50",
                          "&:hover": {
                            backgroundColor: "rgba(76, 175, 80, 0.05)",
                          },
                        }}
                      >
                        {button?.buttonType === "PHONE_NUMBER"
                          ? button?.buttonName
                          : button?.buttonType === "URL"
                          ? button?.buttonName
                          : button?.buttonType === "QUICK_REPLY"
                          ? button?.buttonValue || button?.buttonName
                          : button.buttonValue}
                      </Button>
                    )
                  )}
                </Box>
              </Paper>
            </CarouselItem>
          ))}
        </CarouselTrack>
      </CarouselContent>

      {/* Navigation Arrows - Only show when needed */}
      {currentIndex > 0 && (
        <NavigationButton
          onClick={goToPrevious}
          aria-label="Previous slide"
          sx={{
            left: "4px",
            color: "#4CAF50",
            "&:hover": {
              backgroundColor: "rgba(76, 175, 80, 0.4)",
            },
          }}
        >
          <BiLeftArrowAlt />
        </NavigationButton>
      )}

      {currentIndex < carouselCards.length - 1 && (
        <NavigationButton
          onClick={goToNext}
          aria-label="Next slide"
          sx={{
            right: "4px",
            color: "#4CAF50",
            "&:hover": {
              backgroundColor: "rgba(76, 175, 80, 0.4)",
            },
          }}
        >
          <BiRightArrowAlt />
        </NavigationButton>
      )}

      {/* Navigation Dots */}
      <CarouselIndicators>
        {carouselCards.map((_: any, index: number) => (
          <CarouselIndicator
            key={index}
            active={index === currentIndex}
            onClick={() => setCurrentIndex(index)}
          />
        ))}
      </CarouselIndicators>
    </CarouselContainer>
  );
};

export default DevicePreviewCarousel;
