import axios from "axios";
import { getStoredTokens } from "../../utils/authUtils";

const getAuthHeader = () => {
  const tokens = getStoredTokens();
  return tokens?.token ? `Bearer ${tokens?.token}` : "";
};
  
const USER_API_URL = process.env.REACT_APP_BASE_URL;

const WidgetsData = (data: any) => {
  return axios({
    url: `${USER_API_URL}/Widgets/create`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: getAuthHeader(),
    },
    data: data,
  });
};

export const MANAGE_INTEGRATIONS_APIS = {
  WidgetsData,
};
