import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { WORKFLOW_API } from "../../../Apis/AdminLogin/Automation/Workflows";

export interface IData {
    getWorkflowAllKeywordsStatus: "loading" | "succeeded" | "failed" | "idle";
    getWorkflowAllKeywordsData: any;
  }

  const initialState: IData = {
    getWorkflowAllKeywordsStatus: "idle",
    getWorkflowAllKeywordsData: null,
  };

  export const getWorkflowAllKeywords = createAsyncThunk(
    "workflow/getWorkflowAllKeywords",
    async (data: any, { rejectWithValue }) => {
      try {
        const response = await WORKFLOW_API.getWorkflowAllKeywords(data);
        return response?.data;
      } catch (error: any) {
        // Provide a descriptive error message
        return rejectWithValue(error.response || 'An error occurred');
      }
    }
  );


  export const getWorkflowAllKeywordsSlice = createSlice({
    name: "getWorkflowAllKeywords",
    initialState,
    reducers: {
       
    },
    extraReducers: (builder) => {
      builder
        .addCase(getWorkflowAllKeywords.pending, (state) => {
          state.getWorkflowAllKeywordsStatus = "loading";
          state.getWorkflowAllKeywordsData = null;
        })
        .addCase(getWorkflowAllKeywords.fulfilled, (state, action) => {
          state.getWorkflowAllKeywordsStatus = "succeeded";
          state.getWorkflowAllKeywordsData = action.payload;
        })
        .addCase(getWorkflowAllKeywords.rejected, (state) => {
          state.getWorkflowAllKeywordsStatus = "failed";
        });
    },
  });


export const getWorkflowAllKeywordsActions = getWorkflowAllKeywordsSlice.actions;
export default getWorkflowAllKeywordsSlice.reducer;
