import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  TextField,
  Button,
  Typography,
  IconButton,
  Box,
  InputAdornment,
  RadioGroup,
  FormControlLabel,
  Radio,
  Select,
  MenuItem,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { bgColors } from "../../utils/bgColors";
import { DraftEditorComponent } from "../../components/common/DraftEditorComponent";
import { EditorState, Modifier } from "draft-js";
import { useDispatch, useSelector } from "react-redux";
import {
  AppDispatch,
  RootState,
  useAppSelector,
} from "../../utils/redux-hooks";
import { postAutoReply } from "../../redux/slices/Automation/autoReplySlice";
import { toastActions } from "../../utils/toastSlice";
import { reactDraftWysiwygToolbarOptionsarticle } from "../../utils/react-draft-wysiwyg-options";
import LoadingComponent from "../../components/common/LoadingComponent";
import { updateAutoReply } from "../../redux/slices/Automation/updateAutoReplySlice";
import { getAutoReply } from "../../redux/slices/Automation/getAutoReplySlice";
import { formatContent, parseTextToDraft } from "../../utils/functions";
import WorkflowLibraryPopup from "./workflowLibraryPopup";
import { getNextVariableCount } from "../../components/AutomationComponents/WorkflowComponents/functions";

interface AddAutoReplyPopupProps {
  open: boolean;
  onClose: () => void;
  onSave: (payload: any) => void;
  companyId: string;
  userId: string;
  editData?: any;
  setEditingItem: any;
}

interface Variable {
  variable: string;
  value: string;
  fallbackValue: string;
}

interface AutoReplyAutomationProps {
  automationResponseType: number;
  companyId: string;
  id: string;
  inputvariations: String[];
  input: string;
  userId: string;
  workflowname: any;
}

interface AutoReplyStatePorps {
  autoReplyAutomation: AutoReplyAutomationProps;
  bodyMessage: string;
  buttonvalues: string[];
  veriables: Variable[];
}

const AddAutoReplyPopup: React.FC<AddAutoReplyPopupProps> = ({
  open,
  onClose,
  companyId,
  userId,
  editData,
  onSave,
  setEditingItem,
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const { status } = useSelector((state: any) => state.autoReply);
  const { updateStatus } = useSelector((state: any) => state.updateAutoReply);
  const [workflowLibraryOpen, setWorkflowLibraryOpen] = useState(false);
  const [inputError, setInputError] = useState(false);
  const radioValues = ["Custom Message", "Workflow"];
  const [editorState, setEditorState] = useState(() =>
    EditorState.createEmpty()
  );
  const [errors, setErrors] = useState<string[]>([]);

  const dictionary = {
    Name: "Name",
    CountryCode: "CountryCode",
    CountryName: "CountryName",
    Contact: "Contact",
    Email: "Email",
  };
  const dictionaryOptions = Object.entries(dictionary).map(([key, value]) => ({
    key,
    value,
  }));

  const userProfileSlice = useAppSelector((state: any) => state?.adminLogin);
  const userData = userProfileSlice?.data;

  const [autoReplyState, setAutoReplyState] = useState<AutoReplyStatePorps>({
    autoReplyAutomation: {
      automationResponseType: 1,
      companyId: userData?.companyId,
      inputvariations: [],
      input: "",
      id: "",
      userId: userData?.userId,
      workflowname: "",
    },
    bodyMessage: "",
    buttonvalues: [],
    veriables: [],
  });

  const resetForm = () => {
    setAutoReplyState({
      autoReplyAutomation: {
        automationResponseType: 1,
        companyId: userData?.companyId,
        inputvariations: [],
        input: "",
        id: "",
        userId: userData?.userId,
        workflowname: "",
      },
      bodyMessage: "",
      buttonvalues: [],
      veriables: [],
    });
    setEditorState(EditorState.createEmpty());
    setInputError(false);
    setErrors([]);
  };

  useEffect(() => {
    if (open) {
      if (editData) {
        setAutoReplyState({
          autoReplyAutomation: {
            automationResponseType:
              editData?.autoReplyAutomation?.automationResponseType || 1,
            companyId:
              editData?.autoReplyAutomation?.companyId || userData?.companyId,
            inputvariations:
              editData?.autoReplyAutomation?.inputVariations || [],
            input: editData?.autoReplyAutomation?.input || "",
            id: editData?.autoReplyAutomation?.id || "",
            userId: editData?.autoReplyAutomation?.userId || userData?.userId,
            workflowname: editData?.autoReplyAutomation?.workflowName
              ? {
                  workflowName: editData?.autoReplyAutomation?.workflowName,
                }
              : "",
          },
          bodyMessage: editData?.bodyMessage || "",
          buttonvalues: editData?.buttonValue?.map((b: any) => b?.value) || [],
          veriables:
            editData?.veriables?.map((v: any) => ({
              variable: v?.veriable,
              value: v?.value,
              fallbackValue: v?.fallbackValue,
            })) || [],
        });

        if (editData?.bodyMessage) {
          setEditorState(
            EditorState.createWithContent(
              parseTextToDraft(editData.bodyMessage)
            )
          );
        }
      } else {
        resetForm();
      }
    }
  }, [editData, open]);

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value, type } = e.target;

    const parsedValue = type === "number" ? Number(value) : value;

    if (name.includes(".")) {
      const [parentKey, childKey] = name.split(".") as [
        keyof AutoReplyStatePorps,
        keyof AutoReplyStatePorps["autoReplyAutomation"]
      ];

      // Add type guard to ensure parentKey is autoReplyAutomation
      if (parentKey === "autoReplyAutomation") {
        setAutoReplyState((prev) => ({
          ...prev,
          [parentKey]: {
            ...prev[parentKey],
            [childKey]: parsedValue,
          },
        }));
      }
    } else {
      // Handle top-level properties with type safety
      const key = name as keyof AutoReplyStatePorps;
      setAutoReplyState((prev) => ({
        ...prev,
        [key]: parsedValue,
      }));
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setAutoReplyState((prev) => ({
      ...prev,
      autoReplyAutomation: {
        ...prev.autoReplyAutomation,
        input: newValue,
      },
    }));
    setInputError(newValue.trim() === "");
  };

  const validateInput = () => {
    const isValid = autoReplyState?.autoReplyAutomation?.input?.trim() !== "";
    setInputError(!isValid);
    return isValid;
  };

  const insertTextAtCursor = (
    editorState: EditorState,
    text: string
  ): EditorState => {
    const contentState = editorState.getCurrentContent();
    const selectionState = editorState.getSelection();
    const newContentState = Modifier.insertText(
      contentState,
      selectionState,
      text
    );
    return EditorState.push(editorState, newContentState, "insert-characters");
  };

  const extractVariables = (
    editorState: EditorState,
    existingVariables: Variable[] = []
  ): Variable[] => {
    const content = editorState?.getCurrentContent().getPlainText();
    const variableRegex = /\{\{(\d+)\}\}/g;
    const matches = content.match(variableRegex);

    if (!matches)
      return existingVariables?.filter((v) => content.includes(v?.variable));

    return matches?.map((match) => {
      const variable = match.replace(/\{\{|\}\}/g, "");
      const existingVar = existingVariables.find(
        (v) => v?.variable === `{{${variable}}}`
      );
      return (
        existingVar || {
          variable: `{{${variable}}}`,
          value: "",
          fallbackValue: "",
        }
      );
    });
  };

  const handleAddVariation = () => {
    // setVariations([...variations, ""]);
    setAutoReplyState({
      ...autoReplyState,
      autoReplyAutomation: {
        ...autoReplyState?.autoReplyAutomation,
        inputvariations: [
          ...autoReplyState?.autoReplyAutomation?.inputvariations,
          "",
        ],
      },
    });
  };
  const handleAddButton = () => {
    setAutoReplyState({
      ...autoReplyState,
      buttonvalues: [...autoReplyState?.buttonvalues, ""],
    });
    setErrors([...errors, ""]);
  };

  const handleButtonChange = (index: number, value: string) => {
    const newButtons = [...autoReplyState?.buttonvalues];
    newButtons[index] = value;
    setAutoReplyState({
      ...autoReplyState,
      buttonvalues: newButtons,
    });

    // Validate uniqueness
    const newErrors = [...errors];
    newErrors[index] = newButtons.some(
      (button, idx) => idx !== index && button === value
    )
      ? "Please try a different value."
      : "";
    setErrors(newErrors);
  };

  const handleRemoveButton = (index: number) => {
    const newButtons = autoReplyState?.buttonvalues?.filter(
      (_, idx) => idx !== index
    );
    const newErrors = errors?.filter((_, idx) => idx !== index);
    setAutoReplyState({
      ...autoReplyState,
      buttonvalues: newButtons,
    });
    setErrors(newErrors);
  };

  const handleVariationChange = (index: number, value: string) => {
    const newVariations = [
      ...autoReplyState?.autoReplyAutomation?.inputvariations,
    ];
    newVariations[index] = value;
    setAutoReplyState({
      ...autoReplyState,
      autoReplyAutomation: {
        ...autoReplyState?.autoReplyAutomation,
        inputvariations: newVariations,
      },
    });
  };

  const handleRemoveVariation = (index: number) => {
    const newVariations =
      autoReplyState?.autoReplyAutomation?.inputvariations?.filter(
        (_, i) => i !== index
      );
    setAutoReplyState({
      ...autoReplyState,
      autoReplyAutomation: {
        ...autoReplyState?.autoReplyAutomation,
        inputvariations: newVariations,
      },
    });
  };

  const handleResponseTypeChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = event.target.value;
    const newResponseType = value === "Custom Message" ? 1 : 2;

    // Reset states based on the new response type
    if (newResponseType === 1) {
      // Switching to Custom Message - reset workflow related states
      setAutoReplyState((prev) => ({
        ...prev,
        autoReplyAutomation: {
          ...prev.autoReplyAutomation,
          workflowname: "",
        },
      }));
    } else {
      // Switching to Workflow - reset message related states
      setEditorState(EditorState.createEmpty());
      setAutoReplyState((prev) => ({
        ...prev,
        bodyMessage: "",
        buttonvalues: [],
        veriables: [],
      }));
    }

    // Update only the automationResponseType in autoReplyState
    setAutoReplyState((prev) => ({
      ...prev,
      autoReplyAutomation: {
        ...prev.autoReplyAutomation,
        automationResponseType: newResponseType,
      },
    }));
  };

  const handleAddVariable = () => {
    const nextCount = getNextVariableCount(editorState, "editor");
    const variableText = `{{${nextCount}}}`;
    const newEditorState = insertTextAtCursor(editorState, variableText);
    setEditorState(newEditorState);
    setAutoReplyState({
      ...autoReplyState,
      bodyMessage: newEditorState.getCurrentContent().getPlainText(),
      veriables: [
        ...autoReplyState?.veriables,
        {
          variable: variableText,
          value: "",
          fallbackValue: "",
        },
      ],
    });
  };

  const handleRemoveVariable = (index: number) => {
    const variableToRemove = autoReplyState?.veriables[index];
    const newVariables = autoReplyState?.veriables?.filter(
      (_, i) => i !== index
    );

    // Remove the variable from the editor content
    const contentState = editorState.getCurrentContent();
    const contentText = contentState.getPlainText();
    const newContentText = contentText.replace(variableToRemove.variable, "");

    const newEditorState = EditorState.createWithContent(
      parseTextToDraft(newContentText)
    );

    setEditorState(newEditorState);
    setAutoReplyState({
      ...autoReplyState,
      veriables: newVariables,
      bodyMessage: newContentText,
    });
  };

  const handleVariableChange = (
    index: number,
    field: keyof Variable,
    value: string
  ) => {
    const newVariables = [...autoReplyState?.veriables];
    newVariables[index] = { ...newVariables[index], [field]: value };
    setAutoReplyState({
      ...autoReplyState,
      veriables: newVariables,
    });
  };

  const handleEditorStateChange = (newEditorState: EditorState) => {
    setEditorState(newEditorState);
    const bodyText = newEditorState.getCurrentContent().getPlainText();
    const variables = extractVariables(
      newEditorState,
      autoReplyState?.veriables
    );
    setAutoReplyState((prevState: any) => ({
      ...prevState,
      bodyMessage: bodyText,
      veriables: variables,
    }));
  };

  const handleSaveWithWorkflow = (selectedWorkflow: any) => {
    setAutoReplyState((prevState: any) => ({
      ...prevState,
      autoReplyAutomation: {
        ...prevState.autoReplyAutomation,
        workflowname: selectedWorkflow,
      },
    }));
  };

  const handleWorkflowSelection = (workflow: any) => {
    setAutoReplyState((prev) => ({
      ...prev,
      autoReplyAutomation: {
        ...prev.autoReplyAutomation,
        workflowname: workflow,
      },
    }));
  };

  const handleWorkLibraryClose = () => {
    setWorkflowLibraryOpen(false);
  };

  const handleSave = async (Workflow: any) => {
    if (!validateInput()) {
      dispatch(
        toastActions.setToaster({
          type: "error",
          message: "Input field is required",
        })
      );
      return;
    }
    // Validate body message
    const bodyMessage = formatContent(editorState.getCurrentContent());
    if (
      !bodyMessage.trim() &&
      autoReplyState?.autoReplyAutomation?.automationResponseType == 1
    ) {
      dispatch(
        toastActions.setToaster({
          type: "error",
          message: "Body message is required",
        })
      );
      return;
    }
    // Check if all variable fields are filled

    const allFieldsFilled = autoReplyState?.veriables?.every(
      (variable) =>
        variable?.variable && variable?.value && variable?.fallbackValue
    );

    if (autoReplyState?.veriables && !allFieldsFilled) {
      dispatch(
        toastActions.setToaster({
          type: "error",
          message: "Please fill all the variable fields",
        })
      );
      return;
    }
    const hasDuplicateButtons = autoReplyState?.buttonvalues?.some(
      (button, index) => autoReplyState?.buttonvalues?.indexOf(button) !== index
    );

    if (hasDuplicateButtons) {
      dispatch(
        toastActions.setToaster({
          type: "error",
          message:
            "Duplicate button values are not allowed. Please use unique values for each button.",
        })
      );
      return;
    }

    if (hasDuplicateButtons) {
      dispatch(
        toastActions.setToaster({
          type: "error",
          message:
            "Duplicate button values are not allowed. Please use unique values for each button.",
        })
      );
      return;
    }

    if (autoReplyState?.veriables && !allFieldsFilled) {
      dispatch(
        toastActions.setToaster({
          type: "error",
          message: "Please fill all the fields",
        })
      );
      return;
    }

    const payload = {
      autoReplyAutomation: {
        id: editData ? editData?.autoReplyAutomation?.id : undefined,
        companyId:
          autoReplyState?.autoReplyAutomation?.companyId || userData?.companyId,
        userId: autoReplyState?.autoReplyAutomation?.userId || userData?.userId,
        input: autoReplyState?.autoReplyAutomation?.input,
        automationResponseType:
          autoReplyState?.autoReplyAutomation?.automationResponseType,
        inputVariations: autoReplyState?.autoReplyAutomation?.inputvariations,
        workflowName:
          autoReplyState?.autoReplyAutomation?.workflowname?.workflowName,
      },
      bodyMessage: bodyMessage,
      veriables: autoReplyState?.veriables?.map((v, index) => ({
        id: editData ? editData?.veriables[index]?.id : undefined,
        referenceId: editData
          ? editData?.veriables[index]?.referenceId
          : undefined,
        index: index + 1,
        veriable: v?.variable,
        value: v?.value,
        fallbackValue: v?.fallbackValue,
        referenceTableType: 1,
      })),
      buttonValue: autoReplyState?.buttonvalues?.map((button) => ({
        key: "OK",
        value: button,
      })),
    };
    try {
      // const response: any = await dispatch(postAutoReply(payload));
      const response: any = await dispatch(
        editData ? updateAutoReply(payload) : postAutoReply(payload)
      );

      setAutoReplyState({
        ...autoReplyState,
        buttonvalues: [],
        veriables: [],
        bodyMessage: "",
        autoReplyAutomation: {
          input: "",
          inputvariations: [],
          automationResponseType: 1,
          workflowname: "",
          userId: "",
          companyId: "",
          id: "",
        },
      });
      handleClose();
      if (response?.payload?.success) {
        const payload = {
          companyId: userData?.companyId,
        };
        dispatch(getAutoReply(payload));
        dispatch(
          toastActions.setToaster({
            type: "success",
            message:
              response?.payload?.data?.message ||
              "Auto reply custom message created successfully",
          })
        );
      } else {
        dispatch(
          toastActions.setToaster({
            type: "error",
            message:
              response?.payload?.data?.message ||
              "Failed to create auto reply custom message",
          })
        );
      }
    } catch (error: any) {
      dispatch(
        toastActions.setToaster({
          type: "error",
          message:
            error?.payload?.message ||
            "An error occurred while saving the auto reply custom message",
        })
      );
    }
    handleClose();
  };

  const handleViewWorkflowLibrary = () => {
    setWorkflowLibraryOpen(true);
  };

  return (
    <Box>
      <Dialog
        open={open}
        onClose={onClose}
        maxWidth="md"
        fullWidth
        disableEnforceFocus
        disableAutoFocus
        sx={{
          borderRadius: "16px",
          "& .MuiPaper-root": {
            borderRadius: "16px",
            maxWidth: { xs: "95%", sm: "600px", md: "800px" },
            width: "100%",
            margin: { xs: "8px", sm: "16px" },
          },
        }}
      >
        <DialogTitle
          sx={{
            bgcolor: "white",
            color: "black",
            display: "flex",
            alignItems: "center",
            borderTopLeftRadius: "16px",
            borderTopRightRadius: "16px",
            borderBottomLeftRadius: "0",
            borderBottomRightRadius: "0",
            padding: { xs: "12px 16px", sm: "16px 24px" },
          }}
        >
          <Box
            component="span"
            sx={{ display: "flex", alignItems: "center", fontWeight: "bold" }}
          >
            <span role="img" aria-label="robot" style={{ marginRight: "8px" }}>
              🤖
            </span>
            {editData ? "Update Custom Auto Reply" : "Setup Custom Auto Reply"}
          </Box>
          <IconButton
            aria-label="close"
            onClick={handleClose}
            sx={{ marginLeft: "auto", color: "black" }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <Typography
            variant="h6"
            sx={{ mt: 2, mb: 1, fontWeight: "bold", fontSize: "20px" }}
          >
            Customer Input
          </Typography>
          <Typography variant="body2" sx={{ mb: 2, fontSize: "15px" }}>
            Add the customer input for which you want the Custom Auto Reply to
            be triggered. As a user, you can enter the Input and up to 5
            variations of the same. In case the customer types in either of
            them, the Auto response gets triggered. Click <a href="#">here</a>{" "}
            to see an example of how this works
          </Typography>
          <Box sx={{ width: "70%" }}>
            <Typography
              variant="subtitle1"
              sx={{ mb: 1, fontWeight: "bold", fontSize: "16px" }}
            >
              Input
            </Typography>
            <Typography variant="body2" sx={{ mb: 1, fontSize: "14px" }}>
              While the Auto replies are triggered both for Customer input and
              its variations. It is the input here that gets shown when the
              Engagetocrm List messages are set up
            </Typography>
            <Box sx={{ mb: 2 }}>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  borderRadius: "20px",
                  transition: "all 0.3s ease-in-out",
                }}
              >
                <TextField
                  fullWidth
                  variant="outlined"
                  placeholder="Input"
                  name="autoReplyAutomation.input"
                  value={autoReplyState?.autoReplyAutomation?.input}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    handleInputChange(e)
                  }
                  onBlur={validateInput}
                  inputProps={{ maxLength: 70 }}
                  size="small"
                  required
                  error={inputError}
                  helperText={inputError ? "This field is required" : ""}
                  sx={{
                    width: { xs: "100%", sm: "60%" },
                    height: "40px",
                    borderRadius: "10px",
                    "& .MuiOutlinedInput-root": {
                      borderRadius: "10px",
                    },
                  }}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <Box
                          sx={{
                            color: inputError ? "error.main" : "text.secondary",
                            fontSize: "0.75rem",
                            mr: 1,
                          }}
                        >
                          {`${autoReplyState?.autoReplyAutomation?.input?.length}/70`}
                        </Box>
                      </InputAdornment>
                    ),
                    sx: {
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: inputError ? "error.main" : "inherit",
                      },
                      "&:hover .MuiOutlinedInput-notchedOutline": {
                        borderColor: inputError ? "error.main" : "inherit",
                      },
                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderColor: inputError ? "error.main" : "primary.main",
                      },
                    },
                  }}
                />
              </Box>
            </Box>
            <Typography
              variant="subtitle1"
              sx={{ mb: 1, fontWeight: "bold", fontSize: "16px" }}
            >
              Input Variations
            </Typography>
            <Typography variant="body2" sx={{ mb: 1, fontSize: "14px" }}>
              Users can set up to 5 variations of the customer input. The Auto
              replies get triggered in case of an exact match with the input or
              any of the variation here
            </Typography>
            {autoReplyState?.autoReplyAutomation?.inputvariations?.map(
              (variation: any, index: number) => (
                <Box
                  key={index}
                  sx={{ display: "flex", alignItems: "center", mb: 1 }}
                >
                  <TextField
                    fullWidth
                    variant="outlined"
                    placeholder={`variation ${index + 1}`}
                    value={
                      autoReplyState?.autoReplyAutomation?.inputvariations[
                        index
                      ]
                    }
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                      handleVariationChange(index, e.target.value)
                    }
                    size="small"
                    sx={{
                      width: "60%",
                      height: "40px",
                      borderRadius: "10px",
                      "& .MuiOutlinedInput-root": {
                        borderRadius: "10px",
                      },
                    }}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <Box
                            sx={{
                              color: "text.secondary",
                              fontSize: "0.75rem",
                              mr: 1,
                            }}
                          >
                            {`${autoReplyState?.autoReplyAutomation?.inputvariations[index]?.length}/70`}
                          </Box>
                        </InputAdornment>
                      ),
                      sx: {
                        "& .MuiOutlinedInput-root": {
                          borderRadius: "12px",
                        },
                        "& .MuiInputBase-input": {
                          borderRadius: "12px",
                          paddingRight: "40px",
                        },
                      },
                    }}
                    inputProps={{ maxLength: 70 }}
                  />
                  <IconButton
                    onClick={() => handleRemoveVariation(index)}
                    sx={{ ml: 1 }}
                  >
                    <CloseIcon />
                  </IconButton>
                </Box>
              )
            )}
            {autoReplyState?.autoReplyAutomation?.inputvariations?.length <
              5 && (
              <Button
                variant="outlined"
                onClick={handleAddVariation}
                style={{
                  color: "green",
                  fontWeight: "600",
                  border: "1px solid green",
                  borderRadius: "8px",
                  textTransform: "capitalize",
                  fontSize: "14px",
                  cursor: "pointer",
                  marginBottom: 2,
                }}
              >
                + Add another variation
              </Button>
            )}
          </Box>

          <Typography
            variant="h6"
            sx={{ mt: 2, mb: 1, fontWeight: "bold", fontSize: "20px" }}
          >
            Custom Auto Reply
          </Typography>
          <Typography variant="body2" sx={{ mb: 2, fontSize: "14px" }}>
            Configure the type of Auto reply you want to be triggered when a
            customer sends the user input or one of its variations in the chat
          </Typography>
          <Box sx={{ width: "70%" }}>
            <Typography
              variant="subtitle1"
              sx={{ mb: 1, fontWeight: "bold", fontSize: "16px" }}
            >
              Select response type
            </Typography>
            <RadioGroup
              row
              value={
                autoReplyState?.autoReplyAutomation?.automationResponseType ===
                1
                  ? "Custom Message"
                  : "Workflow"
              }
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                handleResponseTypeChange(e)
              }
              sx={{
                flexDirection: { xs: "column", sm: "row" },
                gap: { xs: 1, sm: 2 },
                "& .MuiFormControlLabel-root": {
                  margin: { xs: "0", sm: "0 16px 0 0" },
                },
              }}
            >
              {radioValues?.map((label: string) => (
                <FormControlLabel
                  key={label}
                  value={label}
                  control={
                    <Radio
                      sx={{
                        "&.Mui-checked": {
                          color: "green",
                        },
                      }}
                    />
                  }
                  label={label}
                />
              ))}
            </RadioGroup>
            <Box>
              {autoReplyState?.autoReplyAutomation?.automationResponseType ===
              1 ? (
                <>
                  <DraftEditorComponent
                    editorState={editorState}
                    handleEditorStateChange={handleEditorStateChange}
                    reactDraftWysiwygToolbarOptionsarticle={
                      reactDraftWysiwygToolbarOptionsarticle
                    }
                    handleAddVariable={handleAddVariable}
                  />

                  <Box sx={{ marginTop: "15px" }}>
                    {autoReplyState?.veriables?.map(
                      (variable: any, index: number) => (
                        <Box
                          key={index}
                          sx={{ display: "flex", alignItems: "center", mb: 1 }}
                        >
                          <TextField
                            variant="outlined"
                            placeholder="Variable"
                            value={autoReplyState?.veriables[index]?.variable}
                            onChange={(e) =>
                              handleVariableChange(
                                index,
                                "variable",
                                e.target.value
                              )
                            }
                            size="small"
                            sx={{
                              mr: 1,
                              width: "40%",
                              height: "40px",
                              borderRadius: "10px",
                              "& .MuiOutlinedInput-root": {
                                borderRadius: "10px",
                              },
                            }}
                            required
                          />
                          <Select
                            value={autoReplyState?.veriables[index]?.value}
                            onChange={(e: any) =>
                              handleVariableChange(
                                index,
                                "value",
                                e.target.value
                              )
                            }
                            displayEmpty
                            variant="outlined"
                            size="small"
                            sx={{
                              mr: 1,
                              width: "40%",
                              height: "40px",
                              borderRadius: "10px",
                              "& .MuiOutlinedInput-root": {
                                borderRadius: "10px",
                              },
                            }}
                            required
                          >
                            <MenuItem value="" disabled>
                              Select Value
                            </MenuItem>
                            {dictionaryOptions?.map(({ key, value }: any) => (
                              <MenuItem key={key} value={value}>
                                {key}
                              </MenuItem>
                            ))}
                          </Select>
                          <TextField
                            variant="outlined"
                            placeholder="Fallback Value"
                            value={
                              autoReplyState?.veriables[index]?.fallbackValue
                            }
                            onChange={(e) =>
                              handleVariableChange(
                                index,
                                "fallbackValue",
                                e.target.value
                              )
                            }
                            size="small"
                            sx={{
                              mr: 1,
                              width: "40%",
                              height: "40px",
                              borderRadius: "10px",
                              "& .MuiOutlinedInput-root": {
                                borderRadius: "10px",
                              },
                            }}
                            required
                          />
                          <IconButton
                            onClick={() => handleRemoveVariable(index)}
                          >
                            <CloseIcon />
                          </IconButton>
                        </Box>
                      )
                    )}
                  </Box>
                  <Typography
                    variant="subtitle1"
                    sx={{ mb: 1, fontWeight: "bold", fontSize: "16px" }}
                  >
                    Buttons (Optional)
                  </Typography>
                  <Box sx={{ marginTop: "10px" }}>
                    {autoReplyState?.buttonvalues?.length < 3 && (
                      <Button
                        variant="outlined"
                        onClick={handleAddButton}
                        style={{
                          color: "green",
                          fontWeight: "600",
                          border: "1px solid green",
                          borderRadius: "8px",
                          textTransform: "capitalize",
                          fontSize: "14px",
                          cursor: "pointer",
                          marginBottom: 2,
                        }}
                      >
                        + Add Button
                      </Button>
                    )}
                    {autoReplyState?.buttonvalues?.map(
                      (button: any, index: number) => (
                        <Box
                          key={index}
                          sx={{
                            display: "flex",
                            flexDirection: "column",
                            alignItems: "flex-start",
                            marginTop: "10px",
                            width: "250px",
                          }}
                        >
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              width: "100%",
                            }}
                          >
                            <TextField
                              variant="outlined"
                              value={autoReplyState?.buttonvalues[index]}
                              onChange={(e) =>
                                handleButtonChange(index, e.target.value)
                              }
                              inputProps={{ maxLength: 20 }}
                              placeholder={`Button text`}
                              sx={{
                                height: "40px",
                                borderRadius: "10px",
                                "& .MuiOutlinedInput-root": {
                                  borderRadius: "10px",
                                },
                              }}
                              size="small"
                              InputProps={{
                                endAdornment: (
                                  <InputAdornment position="end">
                                    <Box
                                      sx={{
                                        color: "text.secondary",
                                        fontSize: "0.75rem",
                                        mr: 1,
                                      }}
                                    >
                                      {`${autoReplyState?.buttonvalues[index].length}/20`}
                                    </Box>
                                  </InputAdornment>
                                ),
                                sx: {
                                  "& .MuiOutlinedInput-root": {
                                    borderRadius: "12px",
                                  },
                                  "& .MuiInputBase-input": {
                                    borderRadius: "12px",
                                    // paddingRight: "40px",
                                  },
                                },
                              }}
                            />
                            <IconButton
                              onClick={() => handleRemoveButton(index)}
                            >
                              <CloseIcon />
                            </IconButton>
                          </Box>
                          {errors[index] && (
                            <Box
                              sx={{
                                color: "error.main",
                                fontSize: "0.75rem",
                                mt: 1,
                              }}
                            >
                              {errors[index]}
                            </Box>
                          )}
                        </Box>
                      )
                    )}
                  </Box>
                </>
              ) : (
                <>
                  <Button
                    variant="outlined"
                    onClick={handleViewWorkflowLibrary}
                    style={{
                      color: "green",
                      fontWeight: "600",
                      border: "1px solid green",
                      borderRadius: "8px",
                      textTransform: "capitalize",
                      fontSize: "14px",
                      cursor: "pointer",
                      marginTop: "10px",
                    }}
                  >
                    View Workflow Library
                  </Button>
                  <WorkflowLibraryPopup
                    open={workflowLibraryOpen}
                    onClose={handleWorkLibraryClose}
                    setWorkflowLibraryOpen={setWorkflowLibraryOpen}
                    onSave={handleSaveWithWorkflow}
                    selectedWorkflow={
                      autoReplyState?.autoReplyAutomation?.workflowname
                    }
                    setSelectedWorkflow={handleWorkflowSelection}
                    disableEnforceFocus
                    disableAutoFocus
                  />
                </>
              )}
            </Box>
            <Box>
              {autoReplyState?.autoReplyAutomation?.workflowname &&
              autoReplyState?.autoReplyAutomation?.workflowname !== "" ? (
                <Typography
                  sx={{
                    fontWeight: 500,
                    fontSize: "16px",
                    color: "black",
                    margin: "8px 0",
                  }}
                >
                  Selected workflow:{" "}
                  <Box
                    component="span"
                    sx={{
                      fontWeight: 600,
                      fontSize: "16px",
                      color: "gray",
                    }}
                  >
                    {
                      autoReplyState?.autoReplyAutomation?.workflowname
                        ?.workflowName
                    }
                  </Box>
                </Typography>
              ) : (
                <></>
              )}
            </Box>
          </Box>
        </DialogContent>
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: { xs: "120px", sm: "160px" },
            padding: { xs: "16px", sm: "24px" },
          }}
        >
          {status === "loading" || updateStatus === "loading" ? (
            <LoadingComponent height="50px" color={bgColors.blue} />
          ) : (
            <Button
              onClick={() => handleSave(null)}
              sx={{
                backgroundColor: "#3C3C3C",
                color: "#ffffff",
                cursor: "pointer",
                height: "36px",
                borderRadius: "8px",
                width: { xs: "100%", sm: "30%" },
                minWidth: { xs: "unset", sm: "250px" },
                fontSize: "14px",
                fontWeight: "600",
                "&:hover": {
                  backgroundColor: "#3C3C3C",
                  opacity: 1,
                },
              }}
            >
              {editData ? "Update custom auto reply" : "Save custom auto reply"}
            </Button>
          )}
        </Box>
      </Dialog>
    </Box>
  );
};

export default AddAutoReplyPopup;
