import {
  Popover,
  List,
  ListItem,
  <PERSON>ItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
} from "@mui/material";
import GetIconComponent, { GetIconProps } from "./GetIconComponent";
import { useState } from "react";

export interface SelectedFilter {
  id: string;
  value: string;
}

export interface CustomMainFilterPopoverProps {
  anchorEl: HTMLElement | null;
  handleClose: () => void;
  mainFilterOptions: { id: string; value: string }[];
  subFilterOptions?: { id: string; value: string } | any;
  nestedFilterOptions?: { id: string; value: string } | any;
  handleOptionClick: (option: SelectedFilter) => void;
  setSelectedFilter: React.Dispatch<React.SetStateAction<SelectedFilter>>;
}

const CustomMainFilterPopover = ({
  anchorEl,
  handleClose,
  mainFilterOptions,
  subFilterOptions,
  nestedFilterOptions,
  handleOptionClick,
  setSelectedFilter,
}: CustomMainFilterPopoverProps) => {
  const [nestedAnchorEl, setNestedAnchorEl] = useState<HTMLElement | null>(
    null
  );
  const [nestedPopoverType, setNestedPopoverType] = useState<
    "Category" | "Status" | null
  >(null);

  // Check if the nested popover is open
  const nestedOpen = Boolean(nestedAnchorEl);
  const nestedId = nestedOpen ? "nested-popover" : undefined;

  const handleNestedPopoverOpen = (
    type: "Category" | "Status",
    event: React.MouseEvent<HTMLElement>
  ) => {
    setNestedPopoverType(type);
    setNestedAnchorEl(event.currentTarget);
  };

  const handleNestedPopoverClose = () => {
    setNestedAnchorEl(null);
    setNestedPopoverType(null);
  };

  const handleNestedOptionClick = (option: SelectedFilter) => {
    const prefixedId = `${nestedPopoverType}-${option.id}`;
    handleOptionClick({ id: prefixedId, value: option.value });
    setSelectedFilter({
      id: prefixedId,
      value: option.value,
    });
    handleNestedPopoverClose();
    handleClose();
  };

  return (
    <>
      <Popover
        open={Boolean(anchorEl)}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
      >
        <List>
          {mainFilterOptions?.map((option) => (
            <ListItem
              key={option.id}
              onClick={(e) => {
                if (option.value === "Category" || option.value === "Status") {
                  // Open nested popover for Category or Status
                  handleNestedPopoverOpen(
                    option.value as "Category" | "Status",
                    e
                  );
                } else {
                  handleOptionClick({ id: option.id, value: option.value });
                }
              }}
              sx={{
                cursor: "pointer",
                padding: "0",
                margin: "0",
                height: "30px",
                fontSize: "14px",
              }}
            >
              <ListItemButton
                sx={{
                  cursor: "pointer",
                  padding: "0",
                  margin: "0",
                  height: "30px",
                  fontSize: "14px",
                  pr: 1,
                }}
              >
                <ListItemIcon
                  sx={{
                    cursor: "pointer",
                    padding: "0",
                    margin: "0",
                    width: "20px",
                    height: "20px",
                    transform: "scale(0.8)",
                    marginLeft: "5px",
                  }}
                >
                  <GetIconComponent
                    column={option?.value}
                    option={option?.id}
                  />
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Typography
                      sx={{
                        cursor: "pointer",
                        fontSize: "14px",
                        display: "flex",
                        alignItems: "center",
                        paddingTop: "2px",
                      }}
                    >
                      {option.value}
                    </Typography>
                  }
                  sx={{
                    cursor: "pointer",
                    padding: "0",
                    margin: "0",
                    height: "30px",
                    transform: "scale(0.9)",
                    marginLeft: "-30px",
                    paddingTop: "3px",
                    minWidth: 0,
                  }}
                />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
      </Popover>

      <Popover
        id={nestedId}
        open={Boolean(nestedAnchorEl)}
        anchorEl={nestedAnchorEl}
        onClose={handleNestedPopoverClose}
        anchorOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "left",
        }}
        sx={{
          zIndex: 1300,
        }}
      >
        <List>
          {(nestedPopoverType === "Category"
            ? subFilterOptions
            : nestedFilterOptions
          )?.map((option: any) => (
            <ListItem
              key={option?.id}
              onClick={() =>
                handleNestedOptionClick({
                  id: option.id,
                  value: option.value || option.option,
                })
              }
              sx={{
                cursor: "pointer",
                padding: "0",
                margin: "0",
                height: "30px",
                fontSize: "14px",
                paddingRight: "5px",
              }}
            >
              <ListItemButton
                sx={{
                  cursor: "pointer",
                  padding: "0",
                  margin: "0",
                  height: "30px",
                  fontSize: "14px",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <ListItemIcon
                  sx={{
                    cursor: "pointer",
                    padding: "0",
                    margin: "0",
                    width: "20px",
                    height: "20px",
                    transform: "scale(0.9)",
                    marginLeft: "5px",
                    paddingTop: "2px",
                  }}
                >
                  <GetIconComponent
                    column={nestedPopoverType || ""}
                    option={option.id}
                  />
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Typography
                      sx={{
                        cursor: "pointer",
                        fontSize: "14px",
                        display: "flex",
                        alignItems: "center",
                      }}
                    >
                      {option?.value || option?.option}
                    </Typography>
                  }
                  sx={{
                    cursor: "pointer",
                    padding: "0",
                    margin: "0",
                    height: "30px",
                    transform: "scale(0.9)",
                    marginLeft: "-30px",
                    paddingTop: "3px",
                    minWidth: 0,
                  }}
                />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
      </Popover>
    </>
  );
};

export default CustomMainFilterPopover;
