import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { WORKFLOW_API } from "../../../Apis/AdminLogin/Automation/Workflows";

export interface IData {
  createWorkflowReactflowStatus: "loading" | "succeeded" | "failed" | "idle";
  createWorkflowReactflowData: any;
}

const initialState: IData = {
  createWorkflowReactflowStatus: "idle",
  createWorkflowReactflowData: null,
};

export const createWorkflowReactflow = createAsyncThunk(
  "createWorkflowReactflow",
  async (data: any, { rejectWithValue }) => {
    try {
      const response = await WORKFLOW_API.createWorkflowReactflow(data);
      return response?.data;
    } catch (error: any) {
      // Provide a descriptive error message
      return rejectWithValue(error.response || "An error occurred");
    }
  }
) as any;

export const createWorkflowReactflowSlice = createSlice({
  name: "createWorkflowReactflowSlice",
  initialState,
  reducers: {
    resetCreateWorkflowReactflow: (state) => {
      state.createWorkflowReactflowStatus = "idle";
      state.createWorkflowReactflowData = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(createWorkflowReactflow.pending, (state) => {
        state.createWorkflowReactflowStatus = "loading";
        state.createWorkflowReactflowData = null;
      })
      .addCase(createWorkflowReactflow.fulfilled, (state, action) => {
        state.createWorkflowReactflowStatus = "succeeded";
        state.createWorkflowReactflowData = action.payload;
      })
      .addCase(createWorkflowReactflow.rejected, (state) => {
        state.createWorkflowReactflowStatus = "failed";
      });
  },
});

export const { resetCreateWorkflowReactflow } = createWorkflowReactflowSlice.actions;
export default createWorkflowReactflowSlice.reducer;
