import React, { useEffect, useState } from "react";
import {
  Routes,
  Route,
  Navigate,
  useNavigate,
  useLocation,
} from "react-router-dom";
import Home from "../pages/home/<USER>";
import ManageAccount from "../pages/profile/manageAccount/ManageAccount";
import ManageCompany from "../pages/profile/manageCompany/ManageCompany";
import ManageClients from "../pages/profile/manageClients/ManageClients";
import ManagePermissions from "../pages/profile/managePermissions/ManagePermissions";
import ManageNotifications from "../pages/profile/manageNotifications/ManageNotifications";
import HelpCenter from "../pages/profile/helpCenter/HelpCenter";
import LoginForgotPassword from "../pages/ForgotPassword/LoginForgotPassword";
import AdminLogin from "../pages/Login/AdminLogin";
import NewPassword from "../pages/ForgotPassword/NewPassword";
import VerificationCode from "../pages/Verification/VerificationCode";
import ResetPassword from "../pages/ForgotPassword/ResetPassword";
import EditManageAccount from "../pages/profile/editManageAccount/EditManageAccount";
import ManagePayments from "../pages/profile/manageAccount/ManagePayments";
import EditManageCompany from "../pages/profile/EditManageCompany/EditManageCompany";
import Subscription from "../pages/profile/Subscription/Subscription";
import ManageClientDetails from "../pages/profile/manageClients/ManageClientDetails";
import Inbox from "../pages/Inbox/Inbox";
import Campaigns from "../pages/Campaigns/Campaigns";
import Contacts from "../pages/Contacts/Contacts";
import Automation from "../pages/Automation/Automation";
import Wallet from "../pages/Wallet/Wallet";
import UpgradePlan from "../pages/profile/Subscription/UpgradePlan";
import ProtectedRoute from "../components/LoginComponents/ProtectedRoute";
import EditManageClient from "../pages/profile/EditManageClient/EditManageClient";
import Scheduled from "../pages/Campaigns/Scheduled";
import AnalyticsOverview from "../pages/Analytics/AnalyticsOverview";
import AnalyticsAgentPerformance from "../pages/Analytics/AnalyticsAgentPerformance";
import InboxAnalytics from "../pages/Analytics/InboxAnalytics";
import AdPerformance from "../pages/Analytics/AdPerformance";
import TemplateLibrary from "../pages/Templates/Library";
import AllTemplates from "../pages/Templates/All";
import BillingDetails from "../pages/Wallet/BillingDetails";
import BillingDetailsEdit from "../pages/Wallet/BillingDetails_Edit";
import { useAppDispatch, useAppSelector } from "../utils/redux-hooks";
import EmbeddedSignUp from "../pages/Login/EmbeddedSignup";
import axios, { InternalAxiosRequestConfig } from "axios";
import { toastActions } from "../utils/toastSlice";
import WorkFlow from "../pages/Automation/WorkFlow";
import ProfilePaymentMethod from "../pages/profile/Subscription/PaymentMethod";
import Bot from "../pages/Automation/Bot";
import OptinManagement from "../pages/Automation/OptinManagement";
import Discover from "../pages/Integrations/Discover";
import Tags from "../pages/profile/ManageTags/Tags";
import WhatsappLink from "../pages/Integrations/WhatsappLink";
import WhatsappWidget from "../pages/Integrations/WhatsappWidget";
import RenderBillingCallbackPage from "../pages/profile/RenderBillingCallbackPage/RenderBillingCallbackPage";
import TemplateComponent from "../components/TemplateComponents/TemplateForm";
import { adminLoginSliceActions } from "../redux/slices/AdminLoginSlice";
import NoAccessPage from "../components/common/NoAccess";
import AccountsContainer from "../components/HomeComponents/AccountsConatiner";
import { fetchAccountMetaStatus } from "../redux/slices/ManageAccount/AccountMetaStatusSlice";
import { fetchAccountDetails } from "../redux/slices/ManageAccount/AccountDetailsSlice";
import { getWalletAndSubscription } from "../redux/slices/Wallet/WalletSlice";
import { TENANT_ACCOUNTS_API } from "../Apis/Utility/getTenantAccountsApi";
import NoAccessDesignPage from "../components/common/NoAccessDesignPage";
import { fetchUserPermission } from "../redux/slices/ManagePermissions/GetUserPermissionSlice";
import EngagetoLogoSvg from "../assets/svgs/EngagetoLogoSvg";
import { Box } from "@mui/material";
import WorkflowReactFlow from "../pages/Automation/WorkflowReactFlow";
import {
  getStoredRefreshToken,
  getStoredTokens,
  isTokenExpired,
  refreshAccessToken,
  storeTokens,
} from "../utils/authUtils";

const AppRoutes = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const tokens = getStoredTokens();
  const token = tokens?.token ? `Bearer ${tokens?.token}` : "";
  const [currentPathname, setCurrentPathname] = useState(
    window.location.pathname
  );
  const location = useLocation();
  const [isIFrame, setIsIframe] = useState(false);
  const [emailContent, setEmailContent] = useState(null);
  const [accounts, setAccounts] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const tenantId = useAppSelector((state: any) => state.adminLogin.tenantId);
  const isRefreshingRef = React.useRef(false);
  const refreshTimeoutRef = React.useRef<NodeJS.Timeout | null>(null);

  // Store pending requests that should be retried after token refresh
  const pendingRequestsRef = React.useRef<
    Array<{
      config: InternalAxiosRequestConfig;
      resolve: (value: any) => void;
      reject: (reason: any) => void;
    }>
  >([]);

  const loginData = useAppSelector((state: any) => state.adminLogin.data);
  const isAuthenticated = !!token;

  const currentPlanDetails = useAppSelector(
    (state: any) => state?.wallet?.walletAndSubscription?.data
  );
  const getLoginToken = async (businessId: any) => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `${process.env.REACT_APP_BASE_URL}/Authentication/loginWithTenant?businessId=${businessId}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
      if (!response.ok) {
        throw new Error(`Error: ${response.statusText}`);
      }
      const data = await response.json();
      // Store token using storeTokens
      storeTokens({
        token: data.token,
        refreshToken: data.refreshToken,
      });
      // Dispatch actions
      dispatch(adminLoginSliceActions.setData(data));
      await dispatch(
        fetchUserPermission({
          roleId: data?.roleId,
          companyId: data?.companyId,
        })
      );
      await dispatch(fetchAccountMetaStatus(data.companyId));
      await dispatch(fetchAccountDetails(data.userId));
      await dispatch(getWalletAndSubscription(data.companyId));
      if (currentPathname !== "/") {
        navigate(currentPathname);
      } else {
        navigate("inbox/help");
      }
    } catch (error) {
      localStorage.removeItem("token");
      localStorage.removeItem("refreshToken");
    } finally {
      setIsLoading(false);
    }
  };
  const loadtenantAccounts = async (eTenantId: any) => {
    const response = await TENANT_ACCOUNTS_API.getTenantAccounts(eTenantId);

    if (response.status === 200) {
      if (response.data.data.length === 1) {
        await getLoginToken(response.data.data[0].businessId);
        return;
      }
      setAccounts(response.data.data);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    setIsIframe(window.self !== window.top);
    if (window.self !== window.top) {
      //
      const messageHandler = (event: any) => {
        // Clear local storage
        localStorage.clear();

        // Clear session storage
        sessionStorage.clear();

        // Clear cookies (only from your domain)
        document.cookie.split(";").forEach((c) => {
          document.cookie =
            c.trim().split("=")[0] +
            "=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/";
        });
        if (event?.data?.tenantId) {
          let hasNoPathname = window.location.pathname === "/";

          localStorage.removeItem("token");
          setEmailContent(event?.data?.emailContent);
          dispatch(
            adminLoginSliceActions.setIsMainSidebarAccessible(hasNoPathname)
          );
          dispatch(adminLoginSliceActions.setTenantId(event?.data?.tenantId));
          loadtenantAccounts(event?.data?.tenantId);

          window.removeEventListener("message", messageHandler);
        }
      };

      // Add the event listener with the `once` option
      window.addEventListener("message", messageHandler);
      // messageHandler({ data: { tenantId: "sleep" } });
    } else {
      setIsLoading(false);
    }
  }, []);
  const hideLoadingScreen = () => {
    const loadingScreen = document.getElementById("loading-screen");
    if (loadingScreen) {
      loadingScreen.style.display = "none";
    }
  };

  useEffect(() => {
    if (!isLoading) {
      hideLoadingScreen();
    }
  }, [isLoading]);

  // Setup axios interceptors
  axios.interceptors.request.use(async (config) => {
    const tokens = getStoredTokens();
    if (!tokens?.token) {
      navigate("/login");
      return config;
    }

    const api = [
      "login",
      "send-otp",
      "verify-otp",
      "update-password",
      "refresh-token",
      "projects",
      "getallstatusanonymous",
    ];
    const includesApi = api.some((apiEndpoint) =>
      config?.url?.includes(apiEndpoint)
    );

    if (!includesApi && !tenantId) {
      if (tokens?.token) {
        // Only check token expiration, remove timeSet check

        if (isTokenExpired(tokens.token)) {
          // If a refresh is already in progress, queue this request

          if (isRefreshingRef.current) {
            return new Promise<InternalAxiosRequestConfig>(
              (resolve, reject) => {
                pendingRequestsRef.current.push({
                  config,
                  resolve,
                  reject,
                });
              }
            );
          }

          isRefreshingRef.current = true;

          // Set a timeout to reset the refresh state if it takes too long
          refreshTimeoutRef.current = setTimeout(() => {
            pendingRequestsRef.current.forEach(({ reject }) => {
              reject(new Error("Token refresh timeout"));
            });
            pendingRequestsRef.current = [];
          }, 30000);

          try {
            const data = {
              token: tokens?.token,
              refreshToken: getStoredRefreshToken(),
            };

            const newTokens: any = await refreshAccessToken(data);

            // Process any pending requests
            pendingRequestsRef.current.forEach(({ config, resolve }) => {
              if (newTokens?.token) {
                config.headers.Authorization = `Bearer ${newTokens.token}`;
              }
              resolve(config);
            });
            pendingRequestsRef.current = [];

            // Update authorization header with new token
            if (newTokens?.token) {
              config.headers.Authorization = `Bearer ${newTokens.token}`;
              storeTokens({
                token: newTokens.token,
                refreshToken: newTokens.refreshToken,
              });
              isRefreshingRef.current = false;
            }
            return config;
          } catch (error) {
            console.error(
              "[Request Interceptor] Error refreshing token:",
              error
            );

            // Reject all pending requests
            pendingRequestsRef.current.forEach(({ reject }) => {
              reject(error);
            });
            pendingRequestsRef.current = [];

            dispatch(
              toastActions.setToaster({
                type: "error",
                message: "Session Expired",
              })
            );
            localStorage.clear();
            localStorage.removeItem("persist:root");
            localStorage.removeItem("token");
            sessionStorage.clear();
            navigate("/login");
            return Promise.reject(error);
          }
        }
        config.headers.Authorization = `Bearer ${tokens?.token}`;
        return config;
      } else {
        navigate("/login");
      }
    }
    return config;
  });

  axios.interceptors.response.use(
    (response) => response,
    async (error) => {
      const originalRequest: InternalAxiosRequestConfig & {
        _retry?: boolean;
      } = error.config;
      // If the error is not related to authentication or has already been retried, reject it
      if (
        !error.response ||
        error.response.status !== 401 ||
        originalRequest._retry
      ) {
        return Promise.reject(error);
      }
      // Mark this request as retried to prevent infinite loops
      originalRequest._retry = true;

      // If a refresh is already in progress, queue this request
      if (isRefreshingRef.current) {
        return new Promise((resolve, reject) => {
          pendingRequestsRef.current.push({
            config: originalRequest,
            resolve,
            reject,
          });
        });
      }

      isRefreshingRef.current = true;

      // Set a timeout to reset the refresh state if it takes too long
      refreshTimeoutRef.current = setTimeout(() => {
        pendingRequestsRef.current.forEach(({ reject }) => {
          reject(new Error("Token refresh timeout"));
        });
        pendingRequestsRef.current = [];
      }, 30000); // Use 30 seconds consistently

      try {
        const tokens = getStoredTokens();
        const data = {
          token: tokens?.token,
          refreshToken: getStoredRefreshToken(),
        };
        const newTokens: any = await refreshAccessToken(data);

        // Process any pending requests
        pendingRequestsRef.current.forEach(({ config, resolve }) => {
          if (newTokens?.token) {
            config.headers.Authorization = `Bearer ${newTokens.token}`;
          }
          resolve(config);
        });
        pendingRequestsRef.current = [];

        // If token refresh was successful, retry the original request
        if (newTokens?.token) {
          originalRequest.headers.Authorization = `Bearer ${newTokens.token}`;
          return axios(originalRequest);
        }

        // If token refresh failed and we have no token, reject the request
        return Promise.reject(error);
      } catch (refreshError) {
        // Reject all pending requests
        pendingRequestsRef.current.forEach(({ reject }) => {
          reject(refreshError);
        });
        pendingRequestsRef.current = [];

        return Promise.reject(refreshError);
      }
    }
  );

  if (isLoading)
    return (
      <Box sx={{ justifyContent: "center", width: "100%", height: "100vh" }}>
        <div id="loading-screen">
          <EngagetoLogoSvg />
        </div>
      </Box>
    );
  if (tenantId && !accounts.length && !token) {
    return <NoAccessDesignPage emailContent={emailContent} />;
  }

  if (tenantId && accounts && accounts.length >= 2 && !token) {
    return (
      <AccountsContainer accounts={accounts} getLoginToken={getLoginToken} />
    );
  }

  return (
    <Routes>
      <Route path="/login" element={<AdminLogin />} />
      <Route path="/facebook-login" element={<EmbeddedSignUp />} />
      <Route path="/forgot-password" element={<LoginForgotPassword />} />
      <Route path="/new-password" element={<NewPassword />} />
      <Route path="/verification-code" element={<VerificationCode />} />
      <Route path="/reset-password" element={<ResetPassword />} />

      {token && (
        <Route element={<ProtectedRoute isAuthenticated={isAuthenticated} />}>
          <Route path="/" element={<Home />}>
            <Route
              path="/profile/manage-account/:id"
              element={<ManageAccount />}
            />
            <Route
              path="/profile/manage-account/payments"
              element={<ManagePayments />}
            />
            <Route
              path="/meta-callback"
              element={<RenderBillingCallbackPage />}
            />

            <Route
              path="/profile/manage-account/:id/edit"
              element={<EditManageAccount />}
            />

            <Route
              path="/profile/manage-company/:id"
              element={<ManageCompany />}
            />
            <Route
              path="/profile/manage-company/:id/edit"
              element={<EditManageCompany />}
            />
            <Route path="/profile/manage-clients" element={<ManageClients />} />
            <Route
              path="/profile/manage-clients/:id"
              element={<ManageClientDetails />}
            />
            <Route
              path="/profile/manage-clients/edit/:id"
              element={<EditManageClient />}
            />
            <Route
              path="/profile/manage-permissions"
              element={<ManagePermissions />}
            />
            <Route
              path="/profile/manage-notifications"
              element={<ManageNotifications />}
            />
            <Route path="/profile/manage-tags" element={<Tags />} />
            <Route path="/profile/subscription" element={<Subscription />} />
            <Route path="/profile/upgrade-plan" element={<UpgradePlan />} />
            <Route path="/profile/payment" element={<ProfilePaymentMethod />} />
            <Route path="/profile/help-center" element={<HelpCenter />} />
            <Route path="/inbox/:id/" element={<Inbox />} />
            <Route path="/campaigns/one-time" element={<Campaigns />} />
            <Route path="/campaigns/scheduled" element={<Scheduled />} />
            <Route path="/analytics/overview" element={<AnalyticsOverview />} />
            <Route
              path="/analytics/agent-performance"
              element={<AnalyticsAgentPerformance />}
            />
            <Route
              path="/analytics/inbox-analytics"
              element={<InboxAnalytics />}
            />
            <Route
              path="/analytics/ad-performance"
              element={<AdPerformance />}
            />
            <Route path="/contacts" element={<Contacts />} />
            <Route path="/automation/inbox-settings" element={<Automation />} />
            {/* <Route path="/automation/auto-reply" element={<AutoReply />} /> */}
            <Route path="/automation/workflows" element={<WorkFlow />} />

            <Route
              path="/automation/optin-management"
              element={<OptinManagement />}
            />
            <Route path="/automation/bot" element={<Bot />} />
            <Route path="/automation" element={<Automation />} />
            <Route path="/templates-library" element={<TemplateLibrary />} />
            <Route path="/templates-all" element={<AllTemplates />} />
            <Route path="/templates">
              <Route index element={<TemplateComponent />} />
              <Route path=":templatesId" element={<TemplateComponent />} />
            </Route>
            <Route path="/wallet" element={<Wallet />} />
            <Route
              path="/wallet/billing-details"
              element={<BillingDetails />}
            />
            <Route
              path="/wallet/billing-details/edit"
              element={<BillingDetailsEdit />}
            />
            {/* <Route path="/commerce-settings" element={<CommerceSettings />} />
            <Route path="/catalog" element={<Catalog />} /> */}
            <Route path="/integrations-discover" element={<Discover />} />
            <Route
              path="/integrations/whatsapp-link"
              element={<WhatsappLink />}
            />
            <Route path="/whatsapp-link" element={<WhatsappLink />} />
            <Route path="/whatsapp-widget" element={<WhatsappWidget />} />

            {/* <Route path="/order-panel" element={<OrderPanel />} />
            <Route path="/auto-checkout-flow" element={<AutoCheckoutFlow />} /> */}
            <Route path="*" element={<Navigate to="/inbox/help" />} />
          </Route>
          <Route
            path="/automation/workflows/:id"
            element={<WorkflowReactFlow />}
          />
          {currentPlanDetails !== undefined &&
          currentPlanDetails?.subscriptionPlan?.isActive ? (
            <Route path="*" element={<Navigate to="/inbox/help" />} />
          ) : (
            <Route
              path="*"
              element={
                <Navigate to={`/profile/manage-account/${loginData?.userId}`} />
              }
            />
          )}
        </Route>
      )}
      <Route
        path="*"
        element={isIFrame ? <NoAccessPage /> : <Navigate to="/login" />}
      />
    </Routes>
  );
  // : (
  //   <DesktopOnlyMessage />
  // );
};

export default AppRoutes;
