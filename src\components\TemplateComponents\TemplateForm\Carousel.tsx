import React, { useEffect, useRef, useState } from "react";
import {
  Box,
  FormControl,
  FormHelperText,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  Tooltip,
  Typography,
} from "@mui/material";
import EditIcon from "@mui/icons-material/Edit";
import { makeStyles } from "@mui/styles";
import { DraftEditorComponent } from "../../common/DraftEditorComponent";
import { LuImagePlus } from "react-icons/lu";
import { reactDraftWysiwygToolbarOptionsarticle } from "../../../utils/react-draft-wysiwyg-options";
import { EditorState, Modifier } from "draft-js";
import {
  formatContent,
  getMaxFileSize,
  parseTextToDraft,
} from "../../../utils/functions";
import EmojiPopover from "../../InboxComponents/inboxDetailsComponents/EmojiPicker";
import { bgColors } from "../../../utils/bgColors";
import { DeleteOutlineRounded } from "@mui/icons-material";
import { buttonsArray, predefinedVariables } from "../../../utils/constants";
import { useAppDispatch, useAppSelector } from "../../../utils/redux-hooks";
import ActionButtonsComponent from "./actionButtons";
import { ICarousel } from "../../../utils/interfaces";
import { toastActions } from "../../../utils/toastSlice";
import { uploadFile } from "../../../redux/slices/Templates/uploadFileSlice";

const useStyles = makeStyles({
  blackColor: {
    color: "#303030 !important",
    fontWeight: "500 !important",
  },
});

const Carousel: React.FC<ICarousel> = ({
  currentCarousel,
  carouselIndex,
  carouselId,
  templateState,
  setTemplateState,
  predefinedVariables,
  selectedVariable,
  setSelectedVariable,
  formErrors,
  handleRemoveCarousel,
  handleCarouselFileChange,
  setFormErrors,
  selectStyles,
  menuItemStyles,
  muiSelectNoBlue,
}) => {
  const classes = useStyles();

  // states

  const dispatch: any = useAppDispatch();
  const urlButtonRef = useRef(null);
  const bodyRef = useRef(null);
  const [editorState, setEditorState] = useState(() =>
    EditorState.createWithContent(parseTextToDraft(currentCarousel.body || ""))
  );

  // Initialize the EditorState when the component mounts
  useEffect(() => {
    setEditorState(
      EditorState.createWithContent(
        parseTextToDraft(currentCarousel.body || "")
      )
    );
  }, []);

  const [emojiPopoverOpen, setEmojiPopoverOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<HTMLDivElement | null>(null);
  const carouselBtnTypes = buttonsArray.filter(
    (btn: any) => btn.value !== "None"
  );

  const countryCodeArray = useAppSelector(
    (state: any) => state.getAllCoutryCodes?.data
  );
  const codeArray =
    countryCodeArray &&
    countryCodeArray?.map((item: any) => ({
      countryCode: item?.countryCode,
      countryImage: item?.countryImage,
    }));

  const phoneNumberButtonCount = templateState?.carouselCards[
    carouselIndex
  ]?.carouselButtons?.filter(
    (btn: any) => btn.buttonType === "PHONE_NUMBER"
  ).length;
  const urlButtonCount = templateState?.carouselCards[
    carouselIndex
  ]?.carouselButtons?.filter((btn: any) => btn.buttonType === "URL").length;
  const replyButtonCount = templateState?.carouselCards[
    carouselIndex
  ]?.carouselButtons?.filter(
    (btn: any) => btn.buttonType === "QUICK_REPLY"
  ).length;
  // handler functions

  const handleEditorStateChange = (newEditorState: EditorState) => {
    const formattedContent = formatContent(newEditorState.getCurrentContent());

    // Check for variables in the content
    const normalVariableMatches = formattedContent.match(/\{\{(\d+)\}\}/g);
    const leadratVariableMatches = formattedContent.match(
      /#([^\s#][\w\s]*[^\s#])#/g
    );

    // Update the template state with the new content
    setTemplateState((prevState: any) => {
      const updateCarouselCard = {
        ...prevState?.carouselCards[carouselIndex],
      };
      updateCarouselCard.body = formattedContent;
      const updatedCarouselCards = [...prevState?.carouselCards];
      updatedCarouselCards[carouselIndex] = updateCarouselCard;

      // Filter variables that are no longer in the content
      const updatedVariables = prevState.variables.filter((variable: any) => {
        if (variable.type === "carouselBody") {
          return (
            normalVariableMatches && normalVariableMatches.includes(variable.id)
          );
        }
        return true;
      });

      const updatedLeadratVariables = prevState.leadratVariables.filter(
        (variable: any) => {
          if (variable.type === "carouselBody") {
            return (
              leadratVariableMatches &&
              leadratVariableMatches.includes(variable.id)
            );
          }
          return true;
        }
      );

      // Check if all variables have been removed
      const hasNoVariables =
        updatedVariables.length === 0 && updatedLeadratVariables.length === 0;

      // If all variables have been removed, reset the selectedVariable state
      if (hasNoVariables && selectedVariable !== "") {
        setSelectedVariable("");
      }

      return {
        ...prevState,
        carouselCards: updatedCarouselCards,
        variables: updatedVariables,
        leadratVariables: updatedLeadratVariables,
      };
    });

    // Update the editor state to maintain cursor position
    setEditorState(newEditorState);

    // Clear any form errors
    setFormErrors((prev: any) => {
      const updatedCarouselCard = { ...prev.carouselCards[carouselIndex] };
      updatedCarouselCard.body = "";
      const updatedCarouselCards = [...prev.carouselCards];
      updatedCarouselCards[carouselIndex] = updatedCarouselCard;
      return {
        ...prev,
        carouselCards: updatedCarouselCards,
      };
    });
  };
  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>,
    index: any
  ) => {
    if (event.target.files && event.target.files.length > 0) {
      const file = event.target.files[0];

      const maxSize = getMaxFileSize(file?.type);

      if (file?.size > maxSize) {
        dispatch(
          toastActions.setToaster({
            type: "error",
            message: `The file size exceeds the limit. The maximum allowed size for ${
              file.type
            } is ${maxSize / (1024 * 1024)} MB.`,
          })
        );
        return;
      }

      const formData = new FormData();
      formData.append("File", file);
      const uploadResponse: any = await dispatch(uploadFile(formData));

      // handleMediaChange(uploadResponse, file);

      handleCarouselFileChange(uploadResponse, carouselIndex, file);
    }
  };

  const handleBoxClick = () => {
    const fileInput = document.getElementById(
      `carousel-image-input-${carouselId}`
    ) as HTMLInputElement;
    if (fileInput) {
      fileInput.click();
    }
  };
  const handleEmojiSelect = (emoji: string) => {
    // const { current } = bodyRef;
    // if (current) {
    //   const { selectionStart, selectionEnd } = current;
    //   const currentBody = templateState?.body;
    //   const newBody =
    //     currentBody?.slice(0, selectionStart) +
    //     emoji +
    //     currentBody?.slice(selectionEnd);
    //   setTemplateState((prevState) => ({
    //     ...prevState,
    //     body: newBody,
    //   }));
    //   // Set cursor position after the inserted emoji
    //   setTimeout(() => {
    //     bodyRef?.current?.focus();
    //     bodyRef?.current?.setSelectionRange(
    //       selectionStart + emoji?.length,
    //       selectionStart + emoji?.length
    //     );
    //   }, 0);
  };

  const handleCloseEmojiPopover = () => {};

  const handleAddCarouselBtns = (e: any) => {
    setTemplateState((prev: any) => {
      const carouselIndex = prev.carouselCards.findIndex(
        (card: any) => card.id === carouselId
      );
      const updatedCarouselButtons = [
        ...prev?.carouselCards[carouselIndex]?.carouselButtons,
        {
          id: crypto.randomUUID(),
          buttonType: e?.target?.value,
          buttonValue: "",
          buttonName: "",
          countryCode: "+91",
        },
      ];
      const updatedCarouselCard = {
        ...prev.carouselCards[carouselIndex],
        carouselButtons: updatedCarouselButtons,
      };
      const updatedCarouselCards = [...prev.carouselCards];
      updatedCarouselCards[carouselIndex] = updatedCarouselCard;
      return {
        ...prev,
        carouselCards: updatedCarouselCards,
      };
    });
  };

  const handleUpdateCarouselBtn = (index: number, updateButton: any) => {
    let updatedCarouselCards = templateState.carouselCards.map(
      (carousel: any, i: number) => {
        if (carouselIndex === i) {
          let updatedCarouselButtons = carousel.carouselButtons.map(
            (button: any, j: number) => {
              if (j === index) {
                return updateButton;
              }
              return button;
            }
          );
          return {
            ...carousel,
            carouselButtons: updatedCarouselButtons,
          };
        }
        return carousel;
      }
    );
    setTemplateState((prev: any) => {
      return {
        ...prev,
        carouselCards: updatedCarouselCards,
      };
    });
    setFormErrors((prev: any) => {
      const updateCarouselButtons = [
        ...prev.carouselCards?.[carouselIndex]?.carouselButtons,
      ];
      let updateCarouselBtn: any = {};
      if (updateButton.buttonType === "PHONE_NUMBER") {
        if (!updateButton.buttonValue) {
          updateCarouselBtn.phoneNumber = "Phone number is required";
        }
        if (!updateButton.buttonName) {
          updateCarouselBtn.callButtonName = "Name is required";
        }
      } else if (updateButton.buttonType === "URL") {
        if (!updateButton.buttonValue) {
          updateCarouselBtn.redirectUrl = "URL is required";
        }
        if (!updateButton.buttonName) {
          updateCarouselBtn.urlButtonName = "Name is required";
        }
      } else if (updateButton.buttonType === "QUICK_REPLY") {
        if (!updateButton.buttonValue) {
          updateCarouselBtn.quickReply = "Quick reply is required";
        }
      }
      updateCarouselButtons[index] = updateCarouselBtn;
      const updateCarouselCards = [...prev.carouselCards];
      updateCarouselCards[carouselIndex].carouselButtons =
        updateCarouselButtons;
      return {
        ...prev,
        carouselCards: updateCarouselCards,
      };
    });
  };

  const handleRemoveCarouselBtn = (index: number) => {
    setTemplateState((prev: any) => {
      const carouselIndex = prev.carouselCards.findIndex(
        (card: any) => card.id === carouselId
      );
      const updatedCarouselButtons = prev.carouselCards[
        carouselIndex
      ]?.carouselButtons.filter((btn: any, i: number) => i !== index);
      const updatedCarouselCard = {
        ...prev.carouselCards[carouselIndex],
        carouselButtons: updatedCarouselButtons,
      };
      const updatedCarouselCards = [...prev.carouselCards];
      updatedCarouselCards[carouselIndex] = updatedCarouselCard;
      return {
        ...prev,
        carouselCards: updatedCarouselCards,
      };
    });

    setFormErrors((prev: any) => {
      const updateCarouselButtons = prev.carouselCards?.[
        carouselIndex
      ]?.carouselButtons?.filter((btn: any, i: number) => i !== index);
      const updateCarouselCards = [...prev.carouselCards];
      updateCarouselCards[carouselIndex].carouselButtons =
        updateCarouselButtons;
      return {
        ...prev,
        carouselCards: updateCarouselCards,
      };
    });
  };

  const handleAddVariable = (
    type: string,
    urlButtonIndex?: number,
    buttonIndex?: number
  ) => {
    // Get current template state
    // const urlButtons =
    //   templateState?.buttons?.filter(
    //     (button: any) => button?.buttonType === "carouselURL"
    //   ) || [];
    // // Determine input references and positions based on type
    // let input, startPos, endPos, newText;
    // if (type === "url Button") {
    //   // Check if a specific URL button is targeted
    //   if (
    //     typeof buttonIndex === "number" &&
    //     urlButtons[urlButtonIndex ? urlButtonIndex : 0]
    //   ) {
    //     input = urlButtonRef?.current;
    //     newText = urlButtons[urlButtonIndex ? urlButtonIndex : 0].buttonValue;
    //   }
    // }
    // // If input is undefined, exit early
    // if (!input) return;
    // Assign default values to startPos and endPos if they are null or undefined
    // startPos = input?.selectionStart ?? 0;
    // endPos = input?.selectionEnd ?? 0;
    // // Update variables with the proper count for the type
    // const { newVariables, newCount } = updateVariables(
    //   type,
    //   templateState,
    //   urlButtonIndex
    // // );
    // const variableText =
    //   type === "url Button" ? `{{${newCount}}}` : ` {{${newCount}}} `;
    // // Update the appropriate text based on type
    // const startPosIndex = startPos || 0;
    // const endPosIndex = endPos || 0;
    // // Construct the new text with the variable inserted at the correct position
    // const updatedText =
    //   newText?.substring(0, startPosIndex) +
    //   variableText +
    //   newText?.substring(endPosIndex, newText?.length);
    // Set the new template state
    // setTemplateState((prevState) => ({
    //   ...prevState,
    //   body: type === "body" ? updatedText : prevState.body,
    //   header: type === "header" ? updatedText : prevState.header,
    //   buttons:
    //     type === "url Button"
    //       ? prevState.buttons.map((button: any, index: number) =>
    //           button.buttonType === "URL" && index === buttonIndex
    //             ? { ...button, buttonValue: updatedText }
    //             : button
    //         )
    //       : prevState.buttons,
    //   variables: newVariables,
    // }));
  };
  const handleEditorAddPrebuildVariable = (event: any) => {
    const contentState = editorState.getCurrentContent();
    const selection = editorState.getSelection();
    const variable = event?.target?.value;

    // Insert the variable at the current cursor position
    const newContentState = Modifier.replaceText(
      contentState,
      selection,
      variable
    );

    // Create a new EditorState with the updated content
    const newEditorState = EditorState.push(
      editorState,
      newContentState,
      "insert-characters"
    );

    // Update the editor state to maintain cursor position
    setEditorState(newEditorState);

    // Format the content for storage
    const formattedContent = formatContent(newContentState);

    // Create a new variable object
    const newVariable = {
      type: "carouselBody",
      id: variable,
      value: "",
      field: "",
      fallBackValue: "",
    };

    // Check if there are any normal variables in the template
    const hasNormalVariables =
      templateState.variables && templateState.variables.length > 0;

    // Only allow adding leadrat variables if there are no normal variables
    if (!hasNormalVariables) {
      // Update the template state with the new content and variable
      setTemplateState((prevState: any) => {
        const updateCarouselCard = {
          ...prevState?.carouselCards[carouselIndex],
        };
        updateCarouselCard.body = formattedContent;
        const updatedCarouselCards = [...prevState?.carouselCards];
        updatedCarouselCards[carouselIndex] = updateCarouselCard;
        return {
          ...prevState,
          carouselCards: updatedCarouselCards,
          leadratVariables: [...prevState.leadratVariables, newVariable],
        };
      });

      // Set the selectedVariable to indicate we're using leadrat variables
      setSelectedVariable("leadratVariable");

      // Clear any form errors
      setFormErrors((prev: any) => {
        const updatedCarouselCard = { ...prev.carouselCards[carouselIndex] };
        updatedCarouselCard.body = "";
        const updatedCarouselCards = [...prev.carouselCards];
        updatedCarouselCards[carouselIndex] = updatedCarouselCard;
        return {
          ...prev,
          carouselCards: updatedCarouselCards,
        };
      });
    }
  };

  const getNextVariableCount = () => {
    const body = templateState.carouselCards[carouselIndex]?.body;

    // Find all variables in the body text
    const variableMatches = body.match(/\{\{(\d+)\}\}/g);

    if (!variableMatches) return 1; // If no variables found, start with 1

    // Extract the numbers from the variables and convert to integers
    const variableNumbers = variableMatches.map((match: any) =>
      parseInt(match.replace(/\{\{|\}\}/g, ""), 10)
    );

    // Find the maximum number
    const maxCount = Math.max(...variableNumbers);

    // Return the next number after the maximum
    return maxCount + 1;
  };
  const handleEditorAddVariable = () => {
    const contentState = editorState.getCurrentContent();
    const selection = editorState.getSelection();

    // Get the next variable count
    const newCount = getNextVariableCount();

    // Create a new variable object
    const newVariable = {
      type: "carouselBody",
      id: `{{${newCount}}}`,
      value: "",
      field: "",
      fallBackValue: "",
    };

    // Check if there are any leadrat variables in the template
    const hasLeadratVariables =
      templateState.leadratVariables &&
      templateState.leadratVariables.length > 0;

    // Only allow adding normal variables if there are no leadrat variables
    if (!hasLeadratVariables) {
      // Construct the new variables array
      const newVariables = [...(templateState?.variables || []), newVariable];

      // Create the variable text to insert
      const variableText = ` {{${newCount}}} `;

      // Insert the variable at the current cursor position
      const newContentState = Modifier.replaceText(
        contentState,
        selection,
        variableText
      );

      // Create a new EditorState with the updated content
      const newEditorState = EditorState.push(
        editorState,
        newContentState,
        "insert-characters"
      );

      // Update the editor state to maintain cursor position
      setEditorState(newEditorState);

      // Format the content for storage
      const formattedContent = formatContent(newContentState);

      // Update the template state with the new content and variable
      setTemplateState((prevState: any) => {
        const updateCarouselCard = {
          ...prevState?.carouselCards[carouselIndex],
        };
        updateCarouselCard.body = formattedContent;
        const updatedCarouselCards = [...prevState?.carouselCards];
        updatedCarouselCards[carouselIndex] = updateCarouselCard;
        return {
          ...prevState,
          carouselCards: updatedCarouselCards,
          variables: newVariables,
        };
      });

      // Set the selectedVariable to indicate we're using normal variables
      setSelectedVariable("addVariable");

      // Clear any form errors
      setFormErrors((prev: any) => {
        const updatedCarouselCard = { ...prev.carouselCards[carouselIndex] };
        updatedCarouselCard.body = "";
        const updatedCarouselCards = [...prev.carouselCards];
        updatedCarouselCards[carouselIndex] = updatedCarouselCard;
        return {
          ...prev,
          carouselCards: updatedCarouselCards,
        };
      });
    }
  };

  const handleCountryCodeChange = (value: string) => {
    // setCountryCodeValue(value);
    // updateButton({
    //   buttonType: selectedType,
    //   buttonValue: buttonValueState,
    //   countryCode: value,
    //   buttonName: buttonTextLabel,
    // });
    // if (selectedType === "PHONE_NUMBER" && value.trim() === "") {
    //   setErrors((prevErrors) => ({
    //     ...prevErrors,
    //     phoneNumber: "Country code is required",
    //   }));
    // } else {
    //   setErrors((prevErrors) => ({
    //     ...prevErrors,
    //     phoneNumber: "",
    //   }));
    // }
  };

  // Check for variables in the template when the component mounts
  useEffect(() => {
    // Check if there are any variables in the template
    const hasNormalVariables =
      templateState.variables && templateState.variables.length > 0;
    const hasLeadratVariables =
      templateState.leadratVariables &&
      templateState.leadratVariables.length > 0;

    // Set the selectedVariable state based on the variables in the template
    if (hasNormalVariables) {
      setSelectedVariable("addVariable");
    } else if (hasLeadratVariables) {
      setSelectedVariable("leadratVariable");
    } else {
      setSelectedVariable("");
    }
  }, [templateState.variables, templateState.leadratVariables]);

  useEffect(() => {
    // Update the EditorState only when the carouselId changes
    // This ensures that the editor content persists when carousels are reordered
    // without resetting the cursor position during typing
    setEditorState(
      EditorState.createWithContent(
        parseTextToDraft(currentCarousel.body || "")
      )
    );
  }, [carouselId]);

  return (
    <Box id={`carousel-${carouselId}`} sx={{ 
      marginTop: 2,
      padding: 2,
      border: '1px solid #e0e0e0',
      borderRadius: 2,
      backgroundColor: '#ffffff',
      boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
      '&:hover': {
        backgroundColor: '#ffffff',
        boxShadow: '0 2px 5px rgba(0,0,0,0.15)',
        transition: 'box-shadow 0.2s ease'
      }
    }}>
      <Box
        sx={{
          display: "flex",
          alignItems: "start",
          // justifyContent: "space-between",
          gap: 1,
        }}
      >
        <Box>
          <input
            type="file"
            accept="image/*,video/*"
            id={`carousel-image-input-${carouselId}`}
            style={{ display: "none" }}
            onChange={(e: any) => handleFileChange(e, carouselIndex)}
          />
          <Box
            onClick={handleBoxClick}
            sx={{
              position: "relative",
              width: 100,
              height: 120,
              border: `2px dashed ${
                formErrors?.carouselCards?.[carouselIndex]?.headerMediaUrl
                  ? "#d32f2f"
                  : "#4CAF50"
              }`,
              borderRadius: 2,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              cursor: "pointer",
              fontSize: "12px",
              color: "#4CAF50",
              textAlign: "center",
              overflow: "hidden",
            }}
          >
            {!currentCarousel?.headerMediaUrl && (
              <Box>
                <LuImagePlus
                  fontSize="large"
                  color={
                    formErrors?.carouselCards[carouselIndex]?.headerMediaUrl
                      ? "#d32f2f"
                      : "#4CAF50"
                  }
                />
                <Typography
                  variant="body2"
                  fontWeight="bold"
                  sx={{
                    color: `${
                      formErrors.carouselCards[carouselIndex]?.headerMediaUrl
                        ? "#d32f2f"
                        : "#4CAF50"
                    }`,
                  }}
                >
                  Upload card header
                </Typography>
              </Box>
            )}
            {currentCarousel?.headerMediaUrl && (
              <>
                {currentCarousel.mediaUrlType === 4 ? (
                  <video
                    controls
                    width="100%" // Adjust the width as needed
                    style={{ borderRadius: "8px" }} // Optional styling
                  >
                    <source
                      src={
                        currentCarousel?.headerMediaUrl instanceof File
                          ? URL.createObjectURL(currentCarousel?.headerMediaUrl)
                          : currentCarousel?.headerMediaUrl
                      }
                      type="video/mp4"
                    />
                    Your browser does not support the video tag.
                  </video>
                ) : (
                  <img
                    src={
                      currentCarousel?.headerMediaUrl instanceof File
                        ? URL.createObjectURL(currentCarousel?.headerMediaUrl)
                        : currentCarousel?.headerMediaUrl
                    }
                    alt="Template Preview"
                    style={{
                      width: "100%",
                      height: "100%",
                      // maxHeight: "400px",
                      borderRadius: "8px",
                      objectFit: "fill",
                    }}
                  />
                )}

                <Box
                  sx={{
                    position: "absolute",
                    top: 0,
                    right: 0,
                    backgroundColor: "rgba(0, 0, 0, 0.5)",
                    padding: "1px",
                    borderRadius: "50%",
                  }}
                >
                  <IconButton
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent parent click
                      handleBoxClick();
                    }}
                    sx={{ color: "#fff" }}
                  >
                    <EditIcon fontSize="small" />
                  </IconButton>
                </Box>
              </>
            )}
          </Box>
          {formErrors.carouselCards?.[carouselIndex]?.headerMediaUrl && (
            <FormHelperText sx={{ color: "#d32f2f", fontWeight: "500" }}>
              {formErrors.carouselCards[carouselIndex]?.headerMediaUrl}
            </FormHelperText>
          )}
        </Box>
        <Box width="100%">
          {/* // error={!!formErrors.body} */}
          <style>{muiSelectNoBlue}</style>
          <FormControl fullWidth>
            <div
              id={`carousel-body-${carouselId}`}
              style={{ position: "relative" }}
            >
              <EmojiPopover
                open={emojiPopoverOpen}
                anchorEl={anchorEl}
                onClose={handleCloseEmojiPopover}
                onEmojiSelect={handleEmojiSelect}
              />
              <Typography
                variant="body2"
                sx={{
                  position: "absolute",
                  color:
                    currentCarousel?.body?.length > 160
                      ? "error.main"
                      : "inherit",
                  right: 8,
                  top: 8,
                }}
              >
                {currentCarousel.body?.length} / {160}
              </Typography>
              <DraftEditorComponent
                key={`carousel-editor-${carouselId}`}
                selectedVariable={selectedVariable}
                setSelectedVariable={setSelectedVariable}
                predefinedVariables={predefinedVariables}
                editorState={editorState}
                setEditorState={setEditorState}
                handleEditorStateChange={handleEditorStateChange}
                //   handleSaveInboxSettings={handleSaveInboxSettings}
                reactDraftWysiwygToolbarOptionsarticle={
                  reactDraftWysiwygToolbarOptionsarticle
                }
                //   blockRendererFn={blockRendererFn}
                handleAddVariable={handleEditorAddVariable}
                handleEditorAddPrebuildVariable={
                  handleEditorAddPrebuildVariable
                }
                //   file={file}
                bodyRef={bodyRef}
                //   chatAreaRef={chatAreaRef}
                // handleFileUpload={handleFileUpload}
                borderColor={
                  formErrors.carouselCards?.[carouselIndex]?.body
                    ? "red"
                    : "rgb(205, 205, 205)"
                }
              />
              {formErrors.carouselCards?.[carouselIndex]?.body && (
                <FormHelperText sx={{ color: "#d32f2f", fontWeight: "500" }}>
                  {formErrors?.carouselCards[carouselIndex]?.body}
                </FormHelperText>
              )}
            </div>
          </FormControl>
          <Box id={`carousel-buttons-${carouselId}`}>
            {templateState?.carouselCards?.[carouselIndex]?.carouselButtons
              ?.length > 0 ? (
              <>
                {currentCarousel.carouselButtons?.map(
                  (item: any, index: any) => (
                    <Box key={index}>
                      <ActionButtonsComponent
                        formErrors={
                          formErrors?.carouselCards?.[carouselIndex]
                            ?.carouselButtons?.[index]
                        }
                        // setFormErrors={setFormErrors}
                        buttonType={item?.buttonType}
                        buttonTypesList={carouselBtnTypes}
                        buttonValue={item?.buttonValue}
                        countryCode={item?.countryCode}
                        buttonName={item?.buttonName}
                        index={index}
                        phoneNumberCount={phoneNumberButtonCount}
                        urlsCount={urlButtonCount}
                        repliesCount={replyButtonCount}
                        onRemoveButton={() => handleRemoveCarouselBtn(index)}
                        updateButton={(updatedButton: any) =>
                          handleUpdateCarouselBtn(index, updatedButton)
                        }
                        handleAddVariable={handleAddVariable}
                        urlButtonVariablesCount={[]}
                        isUrlDynamic={false}
                        urlButtonRef={urlButtonRef}
                        setTemplateState={setTemplateState}
                        templateState={templateState}
                        selectStyles={selectStyles}
                        menuItemStyles={menuItemStyles}
                        muiSelectNoBlue={muiSelectNoBlue}
                      />
                      
                    </Box>
                  )
                )}
                {templateState?.carouselCards?.[carouselIndex]?.carouselButtons
                  .length < 2 && (
                  <Typography
                    onClick={handleAddCarouselBtns}
                    sx={{
                      marginTop: 1,
                      cursor: "pointer",
                      color: bgColors?.green,
                      textTransform: "none",
                      fontSize: 12,
                      width: 85,
                    }}
                  >
                    + Add Button
                  </Typography>
                )}
              </>
            ) : (
              <Box width="100%" mt={1}>
                <FormControl fullWidth size="small">
                  <InputLabel
                    id="demo-simple-select-label"
                    sx={{
                      fontSize: { xs: 12, md: 14 },
                    }}
                  >
                    Select Button
                  </InputLabel>
                  <Select
                    labelId="demo-simple-select-label"
                    id="demo-simple-select"
                    value={currentCarousel?.carouselButtons}
                    name="carouselBtns"
                    label="Select Button"
                    // inputProps={{ style: { fontSize: 14 } }}
                    sx={selectStyles}
                    MenuProps={{
                      PaperProps: {
                        sx: {
                          "& .MuiMenuItem-root": {
                            menuItemStyles,
                          },
                        },
                      },
                    }}
                    onChange={handleAddCarouselBtns}
                  >
                    {carouselBtnTypes?.map((item, index) => (
                      <MenuItem
                        value={item?.value}
                        key={index}
                        sx={menuItemStyles}
                      >
                        {item?.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
                {formErrors.carouselCards?.[carouselIndex]?.minimumButton && (
                  <FormHelperText sx={{ color: "#d32f2f", fontWeight: "500" }}>
                    {formErrors.carouselCards?.[carouselIndex]?.minimumButton}
                  </FormHelperText>
                )}
              </Box>
            )}
          </Box>
        </Box>
        <Tooltip title="Delete Button">
          <Typography
            onClick={() => handleRemoveCarousel(currentCarousel.id)}
            sx={{
              fontSize: 12,
              cursor: "pointer",
              color:
                templateState?.carouselCards?.length === 1
                  ? "gray"
                  : bgColors?.red,
              textTransform: "none",
            }}
          >
            <DeleteOutlineRounded />
          </Typography>
        </Tooltip>
      </Box>
    </Box>
  );
};

export default Carousel;
